Index: wedof-backend/src/Library/utils/Tools.php
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+><?php\n\nnamespace App\\Library\\utils;\n\nuse App\\Entity\\Organism;\nuse App\\Entity\\Subscription;\nuse App\\Entity\\Tag;\nuse App\\Exception\\WedofBadRequestHttpException;\nuse App\\Library\\utils\\enums\\PeriodTypes;\nuse Beelab\\TagBundle\\Entity\\AbstractTaggable;\nuse Closure;\nuse DateInterval;\nuse DatePeriod;\nuse DateTime;\nuse DateTimeInterface;\nuse DateTimeZone;\nuse Doctrine\\Common\\Collections\\ArrayCollection;\nuse Doctrine\\Common\\Collections\\Collection;\nuse DOMDocument;\nuse DOMXPath;\nuse ErrorException;\nuse Exception;\nuse PhpOffice\\PhpSpreadsheet\\Writer\\Xlsx;\nuse RecursiveArrayIterator;\nuse RecursiveIteratorIterator;\nuse SimpleXMLElement;\nuse Symfony\\Component\\HttpClient\\CurlHttpClient;\nuse Symfony\\Component\\HttpClient\\HttpClient;\nuse Symfony\\Component\\HttpFoundation\\Response;\nuse Symfony\\Component\\HttpFoundation\\StreamedResponse;\nuse Symfony\\Contracts\\HttpClient\\HttpClientInterface;\n\nclass Tools\n{\n    const MIMETYPE_CONVERSION = [\n        \"link\" => [\"application/link-format\"],\n        \".pdf\" => [\"application/pdf\"],\n        \".doc\" => [\"application/msword\"],\n        \".docx\" => [\"application/vnd.openxmlformats-officedocument.wordprocessingml.document\"],\n        \".xls\" => [\"application/vnd.ms-excel\"],\n        \".xlsx\" => [\"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet\"],\n        \".ppt\" => [\"application/vnd.ms-powerpoint\"],\n        \".pptx\" => [\"application/vnd.openxmlformats-officedocument.presentationml.presentation\"],\n        \".txt\" => [\"text/plain\"],\n        \".png\" => [\"image/png\"],\n        \".jpg\" => [\"image/jpeg\"],\n        \".zip\" => [\"application/zip\", \"application/x-zip-compressed\"],\n        \".rar\" => [\"application/vnd.rar\"],\n        \".7z\" => [\"application/x-7z-compressed\"],\n        \".ace\" => [\"application/x-ace-compressed\"],\n        \".tar.gz\" => [\"application/tar\", \"application/tar+gzip\"]\n    ];\n    const MOBILEPHONE_PATTERN = '/^(0[6-7]\\d{8}|002305\\d{7}|00491\\d{9,10}|00212\\d{9}|0041\\d{9}|0022901\\d{8})$/';\n    // 06 or 07 for metropolitan france, 00230 for Maurice, 0049 for Allemagne, 00212 for Morocco, 0041 for Switzerland, 00229 for Benin (cf. https://fr.chahaoba.com/Maurice)\n    const PHONE_PATTERN = '/^((?:(?:\\+|00)33|0)[1-9]\\d{8}|00212\\d{9}|0041\\d{9})$/';\n\n    /**\n     * @param string|null $text\n     * @return string|null\n     */\n    public static function removeAccent(?string $text): ?string\n    {\n        if (empty($text)) {\n            return $text;\n        }\n        $replacements = array('Š' => 'S', 'š' => 's', 'Ž' => 'Z', 'ž' => 'z', 'À' => 'A', 'Á' => 'A', 'Â' => 'A', 'Ã' => 'A', 'Ä' => 'A', 'Å' => 'A', 'Æ' => 'A', 'Ç' => 'C', 'È' => 'E', 'É' => 'E',\n            'Ê' => 'E', 'Ë' => 'E', 'Ì' => 'I', 'Í' => 'I', 'Î' => 'I', 'Ï' => 'I', 'Ñ' => 'N', 'Ò' => 'O', 'Ó' => 'O', 'Ô' => 'O', 'Õ' => 'O', 'Ö' => 'O', 'Ø' => 'O', 'Ù' => 'U',\n            'Ú' => 'U', 'Û' => 'U', 'Ü' => 'U', 'Ý' => 'Y', 'Þ' => 'B', 'ß' => 'Ss', 'à' => 'a', 'á' => 'a', 'â' => 'a', 'ã' => 'a', 'ä' => 'a', 'å' => 'a', 'æ' => 'a', 'ç' => 'c',\n            'è' => 'e', 'é' => 'e', 'ê' => 'e', 'ë' => 'e', 'ì' => 'i', 'í' => 'i', 'î' => 'i', 'ï' => 'i', 'ð' => 'o', 'ñ' => 'n', 'ò' => 'o', 'ó' => 'o', 'ô' => 'o', 'õ' => 'o',\n            'ö' => 'o', 'ø' => 'o', 'ù' => 'u', 'ú' => 'u', 'û' => 'u', 'ý' => 'y', 'þ' => 'b', 'ÿ' => 'y');\n        return strtr($text, $replacements);\n    }\n\n    /**\n     * \"deburr\" the text so that it can be compared to another one\n     * @param string $text\n     * @return string\n     */\n    public static function normalizeString(string $text): string\n    {\n        return str_replace('-', ' ', strtolower(self::removeAccent($text)));\n    }\n\n    /**\n     * Replace weird quotes and dash by their standard version so they are compatible with Latin-1\n     * Only replace with equivalent number of chars (don't do euro sign, ... etc.)\n     * @param string|null $text\n     * @return string|null\n     */\n    public static function normalizeUnicodeChars(?string $text): ?string\n    {\n        if (empty($text)) {\n            return $text;\n        }\n        $replacements = [\n            '‘' => \"'\", '’' => \"'\", '‛' => \"'\", '′' => \"'\", // Single quote\n            '“' => '\"', '”' => '\"', '‟' => '\"', '″' => '\"', // Double quote\n            '–' => '-', '—' => '-', '―' => '-' // Dash\n        ];\n        return strtr($text, $replacements);\n    }\n\n    /**\n     * Percentage of similarity\n     * @param string $a\n     * @param string $b\n     * @return float\n     */\n    public static function computeStringSimilarity(string $a, string $b): float\n    {\n        // See https://www.php.net/manual/en/function.levenshtein.php\n        // Weights of insert / delete / replace can be configured\n        // For other options see\n        // For how the text sounds, see https://www.php.net/manual/en/function.soundex.php and https://www.php.net/manual/en/function.metaphone.php\n        // Another option, more forgiving regarding order of similar characters: https://www.php.net/manual/en/function.similar-text.php\n        $normalizedA = self::normalizeString($a);\n        $normalizedB = self::normalizeString($b);\n\n        $similarityRate = 0.0;\n        if (strlen($normalizedA) > strlen($normalizedB)) {\n            if (str_contains($normalizedA, $normalizedB)) {\n                $similarityRate = 100;\n            }\n        } else if (strlen($normalizedA) < strlen($normalizedB)) {\n            if (str_contains($normalizedB, $normalizedA)) {\n                $similarityRate = 100;\n            }\n        }\n        if ($similarityRate === 0.0) {\n            $length = max(strlen($normalizedA), strlen($normalizedB));\n            $similarityRate = 100 - levenshtein($normalizedA, $normalizedB) / $length * 100;\n        }\n\n        return $similarityRate;\n    }\n\n    /**\n     * @param string $text\n     * @return string\n     */\n    public static function fixWeirdSymbols(string $text): string\n    {\n        $unwanted_array = array('Ã©' => 'é', 'Ã¨' => 'è', 'Ã ' => 'à', 'Ã¯' => 'ï', 'Ã´' => 'ô', 'Ã§' => 'ç', 'Ãª' => 'ê', 'Ã¹' => 'ù', 'Ã¦' => 'æ', 'Å' => 'œ', 'Ã«' => 'ë', 'Ã¼' => 'ü', 'Ã¢' => 'â', 'Â©' => '©', 'Â¤' => '¤', 'â¬' => '€');\n        return strtr($text, $unwanted_array);\n    }\n\n    /**\n     * @param string $text\n     * @return string\n     */\n    public static function toTitleCase(string $text): string\n    {\n        return str_replace('- ', '-', ucwords(str_replace('-', '- ', mb_strtolower($text))));\n    }\n\n    /**\n     * @param string $haystack\n     * @param string $needle\n     * @param bool $case_insensitive\n     * @return bool\n     */\n    public static function startsWith(string $haystack, string $needle, bool $case_insensitive = false): bool\n    {\n        return substr_compare($haystack, $needle, 0, strlen($needle), $case_insensitive) === 0;\n    }\n\n    /**\n     * @param string $haystack\n     * @param string $needle\n     * @return bool\n     */\n    public static function endsWith(string $haystack, string $needle): bool\n    {\n        return substr_compare($haystack, $needle, -strlen($needle)) === 0;\n    }\n\n    /**\n     * @param string $haystack\n     * @param string $needle\n     * @return bool\n     */\n    public static function contains(string $haystack, string $needle): bool\n    {\n        return strpos($haystack, $needle) !== false;\n    }\n\n    /**\n     * @param string $string\n     * @param string $prefix\n     * @return string\n     */\n    public static function removePrefix(string $string, string $prefix): string\n    {\n        return self::startsWith($string, $prefix) ? substr($string, strlen($prefix)) : $string;\n    }\n\n    /**\n     * @param string $string\n     * @param string $suffix\n     * @return string\n     */\n    public static function removeSuffix(string $string, string $suffix): string\n    {\n        return self::endsWith($string, $suffix) ? substr($string, 0, (strrpos($string, $suffix))) : $string;\n    }\n\n    /**\n     * @param string $needle\n     * @param string $replace\n     * @param string $haystack\n     * @return string|string[]\n     */\n    public static function strReplaceFirst(string $needle, string $replace, string $haystack): string\n    {\n        $pos = strpos($haystack, $needle);\n        if ($pos !== false) {\n            $haystack = substr_replace($haystack, $replace, $pos, strlen($needle));\n        }\n        return $haystack;\n    }\n\n    /**\n     * @param $a\n     * @param $b\n     * @param string $field\n     * @return int\n     */\n    public static function sortArrayByDate($a, $b, string $field = \"issued\"): int\n    {\n        return strtotime($b[$field]) - strtotime($a[$field]);\n    }\n\n    /**\n     * @param array $defaultOptions\n     * @param int $maxHostConnections\n     * @param int $maxPendingPushes\n     * @return HttpClientInterface\n     */\n    public static function getHttpClient(array $defaultOptions = [], int $maxHostConnections = 6, int $maxPendingPushes = 50): HttpClientInterface\n    {\n        if ($_ENV[\"HTTP_CLIENT_WITH_CURL\"] == 'yes') {\n            return new CurlHttpClient($defaultOptions, $maxHostConnections, $maxPendingPushes);\n        } else {\n            return HttpClient::create($defaultOptions, $maxHostConnections, $maxPendingPushes);\n        }\n    }\n\n    /**\n     * @param $arr\n     * @return array\n     */\n    public static function flatten($arr): array\n    {\n        $it = new RecursiveIteratorIterator(new RecursiveArrayIterator($arr));\n        return iterator_to_array($it);\n    }\n\n    /**\n     * @param DateTime $date\n     * @param int $weekDaysNumber\n     * @param int $hours\n     * @param int $minutes\n     * @param array $holidays array of DateTime\n     * @return DateTime\n     */\n    public static function addWeekDays(DateTime $date, int $weekDaysNumber, int $hours = 0, int $minutes = 0, array $holidays = []): DateTime\n    {\n        if (!empty($holidays)) {\n            $nextBusinessDay = clone $date;\n            for ($i = 1; $i <= $weekDaysNumber; $i++) {\n                $nextBusinessDay->modify(\"+1 weekdays\");\n                if (in_array($nextBusinessDay, $holidays)) {\n                    $weekDaysNumber++;\n                }\n            }\n        }\n        return $date->modify(\"+$weekDaysNumber weekdays + $hours hours + $minutes minutes\");\n    }\n\n    /**\n     * @param DateTime $startDate\n     * @param DateTime $endDate\n     * @return int\n     */\n    public static function getNumberWeekDays(DateTime $startDate, DateTime $endDate): int\n    {\n        $workingDays = [1, 2, 3, 4, 5];\n        $holidayDays = ['*-01-01', '*-05-01', '*-05-08', '*-07-14', '*-08-15', '*-11-01', '*-11-11', '*-12-25'];\n        // missing : Lundi de Pâques, Ascension, Lundi de Pentecôte\n        $interval = new DateInterval('P1D');\n        $periods = new DatePeriod($startDate, $interval, $endDate);\n        $days = 0;\n        foreach ($periods as $period) {\n            if (!in_array($period->format('N'), $workingDays)) continue;\n            if (in_array($period->format('Y-m-d'), $holidayDays)) continue;\n            if (in_array($period->format('*-m-d'), $holidayDays)) continue;\n            $days++;\n        }\n        return $days;\n    }\n\n    /**\n     * @param int $length\n     * @param bool $allowUpper\n     * @param bool $allowLower\n     * @param bool $allowDigit\n     * @return string\n     * @throws Exception\n     */\n    public static function generateRandomString(int $length, bool $allowUpper = true, bool $allowLower = true, bool $allowDigit = true): string\n    {\n        $permittedChars = '';\n        if ($allowUpper) {\n            $permittedChars = 'ABCDEFGHJKLMNPQRSTUVWXYZ';\n        }\n        if ($allowLower) {\n            $permittedChars = $permittedChars . 'abcdefghijkmnopqrstuvwxyz';\n        }\n        if ($allowDigit) {\n            $permittedChars = $permittedChars . '123456789';\n        }\n\n        $input_length = strlen($permittedChars);\n        $random_string = '';\n        for ($i = 0; $i < $length; $i++) {\n            $random_character = $permittedChars[random_int(0, $input_length - 1)];\n            $random_string .= $random_character;\n        }\n\n        return $random_string;\n    }\n\n    public static function isNoResultException(ErrorException $e): bool\n    {\n        return in_array($e->getCode(), [404, 418]);\n    }\n\n    // Polecat's Multi-dimensional array_replace function\n    //\n    /**\n     * Will take all data in second array and apply to first array leaving any non-corresponding values untouched and intact\n     *\n     * @param array $array1\n     * @param array $array2\n     * @return array\n     */\n    public static function multidimensionalArrayReplace(array &$array1, array &$array2): array\n    {\n        foreach ($array2 as $key => $val) {\n            if (is_array($val) && array_key_exists($key, $array1) && $array1[$key] != null) {\n                self::tierParse($array1[$key], $array2[$key]);\n            } else {\n                $array1[$key] = $val;\n            }\n        }\n        return $array1;\n    }\n\n    /**\n     * @param array $reference\n     * @param array $against\n     * @param string $key\n     * @return array\n     */\n    public static function arrayDiffAssocKey(array $reference, array $against, string $key): array\n    {\n        $diff = $reference;\n        $referenceColumn = array_column($reference, $key);\n\n        foreach ($against as $a_key => $value) {\n            //if array1 name value exist in array 2 get that key and delete from array 2 using unset.\n            $index = array_search($value[$key], $referenceColumn);\n            if ($index !== false) {\n                unset($diff[$index]);\n            }\n        }\n\n        return $diff;\n    }\n\n    /**\n     * @param array $reference\n     * @param array $against\n     * @param string $key\n     * @return array\n     */\n    public static function arrayIntersectAssocKey(array $reference, array $against, string $key): array\n    {\n        $intersection = [];\n        $referenceColumn = array_column($reference, $key);\n\n        foreach ($against as $a_key => $value) {\n            //if array1 name value exist in array 2 get that key and delete from array 2 using unset.\n            $index = array_search($value[$key], $referenceColumn);\n            if ($index !== false) {\n                $intersection[] = $reference[$index];\n            }\n        }\n\n        return $intersection;\n    }\n\n    // This sub function is the iterator that will loop back on itself ad infinitum till it runs out of array dimensions\n    private static function tierParse(array &$t_array1, array &$t_array2): void\n    {\n        foreach ($t_array2 as $k2 => $v2) {\n            if (is_array($v2) && array_key_exists($k2, $t_array1)) {\n                self::tierParse($t_array1[$k2], $t_array2[$k2]);\n            } else {\n                $t_array1[$k2] = $v2;\n            }\n        }\n    }\n\n    /**\n     * This methods tells wether we should update CPF raw data for catalog objects\n     * If new data is better => update, otherwise we keep the old data.\n     * It is based on public / private data differences\n     * @param $object\n     * @param array $newRawData\n     * @param string $publicField\n     * @return bool\n     */\n    public static function shouldUpdateCpfRawData($object, array $newRawData, string $publicField): bool\n    {\n        // If no new data, it is useless => don't update\n        if (!isset($newRawData)) {\n            return false;\n        }\n        // If no old data, any new data is better => update\n        $oldRawData = $object->getRawData();\n        if (!isset($oldRawData)) {\n            return true;\n        }\n        // Otherwise\n        // - If old data is public, any new data is better (private or public) => update\n        // - If new data is private, we want it => update\n        return array_key_exists($publicField, $oldRawData) || !array_key_exists($publicField, $newRawData);\n    }\n\n    public static function dashesToCamelCase($string, $capitalizeFirstCharacter = false)\n    {\n\n        $str = str_replace(' ', '', ucwords(str_replace('-', ' ', $string)));\n\n        if (!$capitalizeFirstCharacter) {\n            $str[0] = strtolower($str[0]);\n        }\n\n        return $str;\n    }\n\n    /**\n     * @param $object\n     * @param array $data\n     * @param array $updatableProperties\n     * @return array\n     */\n    public static function filterDataToUpdateOnObject($object, array $data, array $updatableProperties): array\n    {\n        // Keeps only properties XXX listed as updatable\n        // where there exist a method getXXX or isXXX on the object\n        // and where data has changed\n        return array_filter($data, function ($newValue, $propertyName) use ($object, $updatableProperties) {\n            if (!in_array($propertyName, $updatableProperties)) {\n                return false;\n            }\n            $ucKey = ucwords($propertyName);\n            if (method_exists($object, \"get\" . $ucKey)) {\n                $currentValue = $object->{\"get\" . $ucKey}();\n                if ($propertyName === 'tags') {\n                    // Hack to handle equality with an array in input (e.g. ['tag1', 'tag2'])\n                    $currentValue = array_map(function ($tagObject) {\n                        return (string)$tagObject; // Call __toString()\n                    }, $currentValue->toArray());\n                }\n                if (is_object($currentValue) || is_object($newValue)) {\n                    return $currentValue != $newValue; // Triple equals check for ref equality on objects while we only want value equality\n                } else {\n                    return $currentValue !== $newValue;\n                }\n            } else if (method_exists($object, \"is\" . $ucKey)) {\n                $currentValue = $object->{\"is\" . $ucKey}();\n                return $currentValue !== $newValue;\n            } else {\n                return false;\n            }\n        }, ARRAY_FILTER_USE_BOTH);\n    }\n\n    /**\n     * @param $tempFile\n     * @param string $fileName\n     * @return Response\n     */\n    public static function getCsvResponse($tempFile, string $fileName): Response\n    {\n        rewind($tempFile);\n        $response = new Response(stream_get_contents($tempFile));\n        fclose($tempFile);\n        $response->headers->set('Content-Type', 'text/csv');\n        $response->headers->set('Content-Disposition', 'attachment; filename=\"' . $fileName . '.csv\"');\n        return $response;\n    }\n\n    /**\n     * @param Xlsx $writer\n     * @param string $fileName\n     * @return Response\n     */\n    public static function getExcelResponse(Xlsx $writer, string $fileName): Response\n    {\n        $response = new StreamedResponse();\n        $response->headers->set('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');\n        $response->headers->set('Content-Disposition', 'attachment; filename=\"' . $fileName . '.xlsx\"');\n        $response->setPrivate();\n        $response->headers->addCacheControlDirective('no-cache');\n        $response->headers->addCacheControlDirective('must-revalidate');\n        $response->setCallback(function () use ($writer) {\n            $writer->save('php://output');\n        });\n        return $response;\n    }\n\n    /**\n     * @param $data\n     * @param array $availableColumns\n     * @param string|null $includedColumns\n     * @param null $tempFile\n     * @param bool $isForCertificationFolders\n     * @return false|resource\n     */\n    public static function convertDataToCSVFile($data, array $availableColumns, string $includedColumns = null, $tempFile = null, bool $isForCertificationFolders = false)\n    {\n        $csvColumns = isset($includedColumns) ? explode(',', strtoupper($includedColumns)) : null;\n        $unknownColumns = $csvColumns ? array_diff($csvColumns, $availableColumns) : [];\n        if (!empty($unknownColumns)) {\n            throw new WedofBadRequestHttpException(\"Erreur, les colonnes suivantes ne sont pas reconnues pour l'export csv : \" . join(\",\", $unknownColumns));\n        }\n        $columns = $csvColumns ? array_intersect($csvColumns, $availableColumns) : $availableColumns;\n        $separator = ';';\n        $addHeaders = empty($tempFile);\n        $tempFile = $tempFile ?? fopen('php://temp', 'w');\n        if ($addHeaders) fputcsv($tempFile, $columns, $separator);\n        if ($isForCertificationFolders) {\n            $value = ['', 'PAR_SCORING ou PAR_ADMISSION', '', '', '', 'M ou F', '', '', '', '', '', '', '', 'A_DISTANCE ou EN_PRESENTIEL ou MIXTE', '', '', '', '', '', 'URL', '', '',\n                '(Facultatif) C2 ou C1 ou B2 ou B1 ou A2 ou A1 ou INSUFFISANT', 'SANS_MENTION ou MENTION_ASSEZ_BIEN ou MENTION_BIEN ou MENTION_TRES_BIEN ou MENTION_TRES_BIEN_AVEC_FELICITATIONS_DU_JURY', '', 'FORMATION_INITIALE_HORS_APPRENTISSAGE ou \n            FORMATION_INITIALE_APPRENTISSAGE ou FORMATION_CONTINUE_HORS_CONTRAT_DE_PROFESSIONNALISATION ou FORMATION_CONTINUE_CONTRAT_DE_PROFESSIONNALISATION ou VAE ou EQUIVALENCE ou CANDIDAT_LIBRE',\n                '(Doit être rempli si MODALITE_ACCESS est VAE) CONGES_VAE ou VAE_CLASSIQUE', 'OF ou CERTIFIE ou POLE_EMPLOI ou EMPLOYEUR ou AUTRE ', '', '', '', '', '', 'Dossier complet pour export CDC', 'toExport (Dossier à exporter) ou doNotExport (Dossier ne doit pas être exporté, ex: doublon)'];\n            fputcsv($tempFile, $value, $separator);\n        }\n        foreach ($data as $data_entry) {\n            fputcsv($tempFile, call_user_func(array(get_class($data_entry), 'getCSVFormat'), $data_entry, $columns), $separator);\n        }\n        return $tempFile;\n    }\n\n    public static function cleanString($string)\n    {\n        $string = str_replace(' ', '-', $string); // Replaces all spaces with hyphens.\n\n        return preg_replace('/[^A-Za-z0-9\\-]/', '', $string); // Removes special chars.\n    }\n\n    /**\n     * @param $dateString\n     * @param false $endOfDay\n     * @param DateTimeZone|null $dateTimeZone\n     * @return DateTime|bool\n     */\n    public static function generateDateStartOrEndOfDay($dateString, bool $endOfDay = false, DateTimeZone $dateTimeZone = null)\n    {\n        $date = DateTime::createFromFormat('d/m/Y', $dateString);\n        if ($date) {\n            $date->setTime($endOfDay ? 23 : 0, $endOfDay ? 59 : 0, $endOfDay ? 59 : 0);\n            if ($dateTimeZone) {\n                $date->setTimezone($dateTimeZone);\n            }\n        }\n        return $date;\n    }\n\n\n    /**\n     * Custom XML serialization that supports attributes, cdata, repeting keys, conditions...\n     * Config is provided with \"_\" and if there is config, the value must be provided in \"_value\"\n     * Supported config:\n     * _value: any\n     * _key: string\n     * _cdata: bool\n     * _attributes: array\n     * _if: bool\n     * _ifNotEmpty: bool // not set, null, empty string or empty array. \"false\" boolean value, '0' and 0 are kept\n     * @param SimpleXMLElement $parent\n     * @param array $data\n     * @return void\n     */\n    public static function arrayToXml(SimpleXMLElement $parent, array $data): void\n    {\n        /* EXAMPLE\n        $parent = new SimpleXMLElement('<root></root>');\n        $data = [\n            // Raw value\n            'toto' => 'tutu',\n            // Nested element\n            'foo' => [\n                'bar' => 'baz'\n            ],\n            // Attributes\n            'zz' => [\n                '_attributes' => [\n                    'id' => '123',\n                ],\n                '_value' => 'xx'\n            ],\n            // CDATA\n            'blu' => [\n                '_cdata' => true,\n                '_value' => '<strong>Bla</strong>'\n            ],\n            // Override key so it can be repeated\n            [\n                '_key' => 'li',\n                '_value' => 'john'\n            ],\n            [\n                '_key' => 'li',\n                '_value' => 'jane'\n            ]\n        ];\n        ======>\n        <root>\n            <toto>tutu</toto>\n            <foo>\n              <bar>baz</bar>\n            </foo>\n            <zz id=\"123\">xx</zz>\n            <blu><![CDATA[<strong>Bla</strong>]]></blu>\n            <li>john</li>\n            <li>jane</li>\n        </root>\n        */\n        foreach ($data as $key => $value) {\n            if (isset($value['_if']) && !$value['_if']) {\n                continue;\n            }\n            if (isset($value['_ifNotEmpty']) &&\n                (empty($value['_value']) && $value['_value'] !== 0 && $value['_value'] !== '0' && $value['_value'] !== false || is_string($value['_value']) && trim($value['_value']) === '')) {\n                continue;\n            }\n            $attributes = $value['_attributes'] ?? [];\n            $cdata = !empty($value['_cdata']);\n            if (!empty($value['_key'])) {\n                $key = $value['_key']; // Override in special cases\n            }\n            if (isset($value['_value'])) {\n                $value = $value['_value'];\n            }\n            if (is_array($value)) {\n                $child = $parent->addChild($key);\n                self::arrayToXml($child, $value); // recursive call\n            } else if ($cdata) {\n                $child = $parent->addChild($key);\n                $domNode = dom_import_simplexml($child);\n                $domNode->appendChild($domNode->ownerDocument->createCDATASection($value));\n            } else {\n                if ($value === false) {\n                    $value = '0'; // Because (string)true == '1\" and (string)false == '', which is not consistent\n                }\n                $child = $parent->addChild($key, htmlspecialchars((string)$value));\n            }\n            foreach ($attributes as $attributeName => $attributeValue) {\n                $child->addAttribute($attributeName, $attributeValue);\n            }\n        }\n    }\n\n    /**\n     * @param $object\n     * @return mixed\n     */\n    public static function xmlToArray($object)\n    {\n        return json_decode(json_encode($object), 1);\n    }\n\n    /**\n     * @param DateTimeInterface $dateTimeInterface\n     * @return DateTime\n     */\n    public static function convertDateTimeInterfaceToDateTime(DateTimeInterface $dateTimeInterface): DateTime\n    {\n        $dateTime = new DateTime();\n        return $dateTime->setTimestamp($dateTimeInterface->getTimestamp());\n    }\n\n    /**\n     * @param object $object\n     * @return string\n     */\n    public static function getClassName(object $object): string\n    {\n        $exploded = explode('\\\\', get_class($object));\n        return array_pop($exploded);\n    }\n\n    /**\n     * @param int|null $cog\n     * @param string|null $name\n     * @param string|null $code\n     * @return array|null\n     */\n    public static function findCountry(int $cog = null, string $name = null, string $code = null): ?array\n    {\n        try {\n            $cog = $cog > 99000 ? ($cog - 99000) : $cog;\n            if ($cog == 100 || strtolower($name) == 'france' || strtolower($code) == 'fr' || strtolower($code) == 'fra') {\n                return [\n                    \"name\" => \"France\",\n                    \"cog\" => 100,\n                    \"code\" => \"FR\",\n                    \"code3\" => \"FRA\"\n                ];\n            }\n            $inputFileName = __DIR__ . '/../../../data/countriesListCode.json';\n            $json = file_get_contents($inputFileName);\n            $array = json_decode($json, true);\n            if ($name && self::normalizeString($name) === self::normalizeString('COTE D IVOIRE')) {\n                // Hack for when data comes from portail\n                $name = \"COTE D'IVOIRE\";\n            }\n            foreach ($array as $element) {\n                if ($cog && $cog == $element['cog']) {\n                    return $element;\n                } else if ($name && self::normalizeString($name) == self::normalizeString($element['name'])) {\n                    return $element;\n                } else if ($code && (strtolower($code) == strtolower($element['code']) || strtolower($code) == strtolower($element['code3']))) {\n                    return $element;\n                }\n            }\n        } catch (Exception $e) {\n            return null;\n        }\n        return null;\n    }\n\n    /**\n     * @param string $email\n     * @return string\n     */\n    public static function obfuscateEmailAddress(string $email): string\n    {\n        $emailParts = explode('@', $email);\n        $username = $emailParts[0];\n        $domain = $emailParts[1];\n        // firstname.lastname@... => f******e.l******e@...\n        $usernameParts = explode('.', $username);\n        $nbUsernameParts = count($usernameParts);\n        $obfuscatedUsername = '';\n        foreach ($usernameParts as $index => $usernamePart) {\n            $usernamePartLength = strlen($usernamePart);\n            $obfuscatedUsernamePart = $usernamePartLength > 1 ? $usernamePart[0] . str_repeat('*', $usernamePartLength - 2) . $usernamePart[$usernamePartLength - 1] : $usernamePart;\n            $obfuscatedUsername .= $obfuscatedUsernamePart . ($index < $nbUsernameParts - 1 ? '.' : '');\n        }\n        // ...@subdomain.domain.tld => ...@s*******n.d****n.tld\n        $domainParts = explode('.', $domain);\n        $nbDomainParts = count($domainParts);\n        $obfuscatedDomain = '';\n        foreach ($domainParts as $index => $domainPart) {\n            if ($index < $nbDomainParts - 1) {\n                $domainPartLength = strlen($domainPart);\n                $obfuscatedDomainPart = $domainPartLength > 1 ? $domainPart[0] . str_repeat('*', $domainPartLength - 2) . $domainPart[$domainPartLength - 1] : $domainPart;\n                $obfuscatedDomain .= $obfuscatedDomainPart . '.';\n            } else {\n                $obfuscatedDomain .= $domainPart;\n            }\n        }\n        // <EMAIL> => f*******e.l******e@s*******n.d****n.tld\n        return $obfuscatedUsername . '@' . $obfuscatedDomain;\n    }\n\n    /**\n     * @param DateTimeInterface|null $dateTime\n     * throws WedofBadRequestHttpException\n     */\n    public static function throwIfDateNotContemporary(?DateTimeInterface $dateTime)\n    {\n        if (!empty($dateTime)) {\n            $year = $dateTime->format('Y');\n            if ($year <= 1900 || $year >= 2100) { // loosely based on cdc dictionnary\n                throw new WedofBadRequestHttpException(\"Erreur, l'année \" . $year . \" n'est pas contemporaine\");\n            }\n        }\n    }\n\n    /**\n     * @param array $events\n     * @param array $eventTypes\n     * @return array|string[]\n     */\n    public static function cleanEvents(array $events, array $eventTypes): array\n    {\n        $eventsFinal = [];\n        $events = new ArrayCollection($events);\n        if ($events->contains(\"*\") || empty($events)) {\n            $eventsFinal = ['*'];\n        } else {\n            foreach ($eventTypes as $type) {\n                $events_for_type = $events->filter(function ($element) use ($type) {\n                    return str_starts_with($element, $type . \".\");\n                });\n                if ($events_for_type->contains($type . \".*\")) {\n                    $eventsFinal[] = $type . \".*\";\n                } else {\n                    foreach ($events_for_type as $event) {\n                        $eventsFinal[] = $event;\n                    }\n                }\n            }\n        }\n        return $eventsFinal;\n    }\n\n    /**\n     * @param $html\n     * @param $tag\n     * @return string|null\n     */\n    public static function getMetaTagsFromString($html, $tag): ?string\n    {\n        $doc = new DOMDocument();\n        libxml_use_internal_errors(true);\n        $doc->loadHTML($html);\n        libxml_clear_errors();\n\n        $xpath = new DOMXPath($doc);\n        $nodes = $xpath->query('//head/meta[@name]');\n        $meta = [];\n\n        foreach ($nodes as $node) {\n            $meta[$node->getAttribute('name')] = $node->getAttribute('content');\n        }\n\n        return $meta[$tag] ?? null;\n    }\n\n    /**\n     * @param string $date\n     * @return DateTime\n     * @throws Exception\n     */\n    public static function createDateFromString(string $date): DateTime\n    {\n        if (preg_match('/^(\\d{2})\\/(\\d{2})\\/(\\d{4})$/', $date)) {\n            return DateTime::createFromFormat(\"d/m/Y\", $date);\n        } else if (preg_match('/^(\\d{4})-(\\d{2})-(\\d{2})$/', $date)) {\n            return DateTime::createFromFormat(\"Y-m-d\", $date);\n        } else {\n            return new DateTime($date);\n        }\n    }\n\n    /**\n     * @param array $fileTypes\n     * @param Collection $files A collection of files (either RegistrationFolderFile or CertificationFolderFile or CertificationPartnerFile).\n     * @param string $state\n     * @return array\n     */\n    public static function getMissingFileTypesForState(array $fileTypes, Collection $files, string $state): array\n    {\n        $missingFileTypes = [];\n        $requiredFileTypes = array_filter($fileTypes, function ($fileType) use ($state) {\n            return isset($fileType['toState']) && $fileType['toState'] === $state && empty($fileType['generated']);\n        });\n        $uploadedFileTypeIds = $files->map(function ($file) {\n            return $file->getTypeId();\n        });\n        foreach ($requiredFileTypes as $requiredFileType) {\n            if (!$uploadedFileTypeIds->contains($requiredFileType['id'])) {\n                $missingFileTypes[] = $requiredFileType;\n            }\n        }\n        return $missingFileTypes;\n    }\n\n    /**\n     * @param array $fileTypes\n     * @param iterable $entityTags\n     * @param string|null $entityCertifInfo\n     * @return array\n     */\n    public static function filterFileTypes(array $fileTypes, iterable $entityTags, ?string $entityCertifInfo): array\n    {\n        return array_values(array_filter($fileTypes, function ($fileType) use ($entityTags, $entityCertifInfo) {\n            $fileTypeTags = $fileType['tags'] ?? [];\n            $fileTypeCertifInfos = $fileType['certifications'] ?? [];\n            $certifOk = empty($fileTypeCertifInfos) || (!empty($entityCertifInfo) && in_array($entityCertifInfo, $fileTypeCertifInfos));\n            if (empty($fileTypeTags)) {\n                $tagsOk = true;\n            } else {\n                $tagsOk = false;\n                foreach ($entityTags as $entityTag) {\n                    if (in_array($entityTag, $fileTypeTags)) { // One matching tag is enough\n                        $tagsOk = true;\n                        break;\n                    }\n                }\n            }\n            return $tagsOk && $certifOk;\n        }));\n    }\n\n    /**\n     * @param $name\n     * @param null $defaultValue\n     * @return mixed|true|null\n     */\n    public static function getEnvValue($name, $defaultValue = null)\n    {\n        if (isset($_ENV[$name])) {\n            if (is_numeric($_ENV[$name])) {\n                return (int)$_ENV[$name];\n            } else if ($_ENV[$name] === 'true' || $_ENV[$name] === 'on' || $_ENV[$name] === 'yes') {\n                return true;\n            } else if ($_ENV[$name] === 'false' || $_ENV[$name] === 'off' || $_ENV[$name] === 'no') {\n                return false;\n            } else {\n                return $_ENV[$name];\n            }\n        } else {\n            return $defaultValue;\n        }\n    }\n\n    /**\n     * @return string\n     */\n    public static function getEnv(): string\n    {\n        return $_ENV['APP_ENV'];\n    }\n\n    /**\n     * @param array $envs\n     * @return bool\n     */\n    public static function isEnvIn(array $envs): bool\n    {\n        return in_array(self::getEnv(), $envs);\n    }\n\n    /**\n     * @param string $period\n     * @param DateTimeZone|null $timezone\n     * @param Subscription|null $subscription\n     * @return array\n     * @throws Exception\n     */\n    public static function getSinceAndUntilDates(string $period, DateTimeZone $timezone = null, Subscription $subscription = null): array\n    {\n        if (!$timezone) {\n            $timezone = new DateTimeZone('UTC');\n        }\n        $getSinceStart = function ($dateString) use ($timezone) {\n            return (new DateTime($dateString, $timezone))->modify('midnight');\n        };\n        $getUntilEnd = function ($dateString) use ($timezone) {\n            return (new DateTime($dateString, $timezone))->modify('midnight')->modify('+1 day -1 microsecond');\n        };\n        $since = null;\n        $until = null;\n        switch ($period) {\n            // year\n            case PeriodTypes::NEXT_YEAR()->getValue():\n                $since = $getSinceStart('next year January 1st');\n                $until = $getUntilEnd('next year December 31st');\n                break;\n            case PeriodTypes::CURRENT_YEAR()->getValue():\n                $since = $getSinceStart('this year January 1st');\n                $until = $getUntilEnd('this year December 31st');\n                break;\n            case PeriodTypes::PREVIOUS_YEAR()->getValue():\n                $since = $getSinceStart('last year January 1st');\n                $until = $getUntilEnd('last year December 31st');\n                break;\n            case PeriodTypes::ROLLING_YEAR()->getValue():\n                $since = $getSinceStart('- 12 months');\n                $until = $getUntilEnd('today');\n                break;\n            case PeriodTypes::ROLLING_YEAR_FUTURE()->getValue():\n                $since = $getSinceStart('today');\n                $until = $getUntilEnd('+ 12 months');\n                break;\n            // Months\n            case PeriodTypes::NEXT_MONTH()->getValue():\n                $since = $getSinceStart('first day of next month');\n                $until = $getUntilEnd('last day of next month');\n                break;\n            case PeriodTypes::CURRENT_MONTH()->getValue():\n                $since = $getSinceStart('first day of this month');\n                $until = $getUntilEnd('last day of this month');\n                break;\n            case PeriodTypes::PREVIOUS_MONTH()->getValue():\n                $since = $getSinceStart('first day of previous month');\n                $until = $getUntilEnd('last day of previous month');\n                break;\n            case PeriodTypes::ROLLING_MONTH()->getValue():\n                $since = $getSinceStart('- 30 days');\n                $until = $getUntilEnd('today');\n                break;\n            case PeriodTypes::ROLLING_MONTH_FUTURE()->getValue():\n                $since = $getSinceStart('today');\n                $until = $getUntilEnd('- 30 days');\n                break;\n            // Weeks\n            case PeriodTypes::NEXT_WEEK()->getValue():\n                $since = $getSinceStart('next week monday');\n                $until = $getUntilEnd('next week sunday');\n                break;\n            case PeriodTypes::CURRENT_WEEK()->getValue():\n                $day = date('w') - 1;\n                $since = $getSinceStart('-' . $day . ' days');\n                $until = $getUntilEnd('+' . (6 - $day) . ' days');\n                break;\n            case PeriodTypes::PREVIOUS_WEEK()->getValue():\n                $since = $getSinceStart('last week monday');\n                $until = $getUntilEnd('last week sunday');\n                break;\n            case PeriodTypes::ROLLING_WEEK()->getValue():\n                $since = $getSinceStart('-7 days');\n                $until = $getUntilEnd('today');\n                break;\n            case PeriodTypes::ROLLING_WEEK_FUTURE()->getValue():\n                $since = $getSinceStart('today');\n                $until = $getUntilEnd('+7 days');\n                break;\n            // Days\n            case PeriodTypes::TOMORROW()->getValue():\n                $since = $getSinceStart('tomorrow');\n                $until = $getUntilEnd('tomorrow');\n                break;\n            case PeriodTypes::TODAY()->getValue():\n                $since = $getSinceStart('today');\n                $until = $getUntilEnd('today');\n                break;\n            case PeriodTypes::YESTERDAY()->getValue():\n                $since = $getSinceStart('yesterday');\n                $until = $getUntilEnd('yesterday');\n                break;\n            // Other\n            case PeriodTypes::WEDOF_INVOICE()->getValue():\n                if ($subscription) {\n                    $since = $subscription->getCertifierPeriodStartDate();\n                    $until = new DateTime();\n                    $until->setTimestamp($subscription->getCertifierPeriodEndDate()->getTimestamp())->modify('- 1 second'); // Prevent overlap with next month period\n                } else {\n                    throw new WedofBadRequestHttpException(\"L'abonnement est obligatoire pour pouvoir filtrer sur la période de facturation.\");\n                }\n                break;\n            case PeriodTypes::WEDOF_QUOTA()->getValue():\n                if ($subscription) {\n                    $since = $subscription->getCertificationFoldersNumberPeriodStartDate();\n                    $until = new DateTime();\n                    $until->setTimestamp($subscription->getCertificationFoldersNumberPeriodEndDate()->getTimestamp())->modify('- 1 second'); // Prevent overlap with next month period\n                } else {\n                    throw new WedofBadRequestHttpException(\"L'abonnement est obligatoire pour pouvoir filtrer sur la période de facturation.\");\n                }\n                break;\n            default:\n                throw new WedofBadRequestHttpException(\"Période inconnue !\");\n        }\n        return ['since' => $since, 'until' => $until];\n    }\n\n    /**\n     * @param string $csvRawFileContent\n     * @param string $separator\n     * @param bool $convertEmptyToNull\n     * @return array\n     */\n    public static function csvToArray(string $csvRawFileContent, string $separator = ';', bool $convertEmptyToNull = false): array\n    {\n        $array = [];\n        if (!empty($csvRawFileContent)) {\n            $lines = array_map(fn($line) => mb_convert_encoding($line, 'UTF-8', 'auto'), explode(PHP_EOL, $csvRawFileContent));\n            $lines = array_filter($lines, fn($line) => trim($line) !== ''); // Filter last line and potential empty lines\n            $rows = array_map(function ($line) use ($separator, $convertEmptyToNull) {\n                $row = str_getcsv($line, $separator);\n                $row = array_map(fn($cell) => trim($cell), $row);\n                if ($convertEmptyToNull) {\n                    $row = array_map(fn($cell) => $cell === '' ? null : $cell, $row); // by default empty cell is \"\", replace it by null instead\n                }\n                return $row;\n            }, $lines);\n            $keys = array_shift($rows); // Extract header row to make it keys of the associative array\n            $keys[0] = preg_replace('/^\\x{FEFF}/u', '', $keys[0]); // Remove BOM if present\n            foreach ($rows as $row) {\n                $entry = array_combine($keys, $row);\n                $array[] = $entry;\n            }\n        }\n        return $array;\n    }\n\n    /**\n     * @param $str\n     * @param string $allowable_tags\n     * @param bool $strip_attrs\n     * @param bool $preserve_comments\n     * @param callable|null $callback\n     * @return string\n     */\n    public static function stripTagsContent($str, string $allowable_tags = '', bool $strip_attrs = false, bool $preserve_comments = false, callable $callback = null): string\n    {\n        $allowable_tags = array_map('strtolower', array_filter( // lowercase\n            preg_split('/(?:>|^)\\\\s*(?:<|$)/', $allowable_tags, -1, PREG_SPLIT_NO_EMPTY), // get tag names\n            function ($tag) {\n                return preg_match('/^[a-z][a-z0-9_]*$/i', $tag);\n            } // filter broken\n        ));\n        $comments_and_stuff = preg_split('/(<!--.*?(?:-->|$))/', $str, -1, PREG_SPLIT_DELIM_CAPTURE);\n        foreach ($comments_and_stuff as $i => $comment_or_stuff) {\n            if ($i % 2) { // html comment\n                if (!($preserve_comments && preg_match('/<!--.*?-->/', $comment_or_stuff))) {\n                    $comments_and_stuff[$i] = '';\n                }\n            } else { // stuff between comments\n                $tags_and_text = preg_split(\"/(<(?:[^>\\\"']++|\\\"[^\\\"]*+(?:\\\"|$)|'[^']*+(?:'|$))*(?:>|$))/\", $comment_or_stuff, -1, PREG_SPLIT_DELIM_CAPTURE);\n                foreach ($tags_and_text as $j => $tag_or_text) {\n                    $tag = false;\n                    $is_broken = false;\n                    $is_allowable = true;\n                    $result = $tag_or_text;\n                    if ($j % 2) { // tag\n                        if (preg_match(\"%^(</?)([a-z][a-z0-9_]*)\\\\b(?:[^>\\\"'/]++|/+?|\\\"[^\\\"]*\\\"|'[^']*')*?(/?>)%i\", $tag_or_text, $matches)) {\n                            $tag = strtolower($matches[2]);\n                            if (in_array($tag, $allowable_tags)) {\n                                if ($strip_attrs) {\n                                    $opening = $matches[1];\n                                    $closing = '>';\n                                    $result = $opening . $tag . $closing;\n                                }\n                            } else {\n                                $is_allowable = false;\n                                $result = '';\n                            }\n                        } else {\n                            $is_broken = true;\n                            $result = '';\n                        }\n                    }\n                    if (!$is_broken && isset($callback)) {\n                        // allow result modification\n                        call_user_func_array($callback, array(&$result, $tag_or_text, $tag, $is_allowable));\n                    }\n                    $tags_and_text[$j] = $result;\n                }\n                $comments_and_stuff[$i] = implode('', $tags_and_text);\n            }\n        }\n        return implode('', $comments_and_stuff);\n    }\n\n    /**\n     * @param Organism $organism\n     * @return string\n     */\n    public static function getSubdomainForOrganism(Organism $organism): string\n    {\n        if (Tools::getEnvValue('DOMAIN') === 'localhost' && $organism->getSiret() != \"53222292400039\") {\n            $subDomain = \"\";\n        } else {\n            $subDomain = $organism->getSubDomain() ? $organism->getSubDomain() . \".\" : \"\";\n        }\n        return 'https://' . $subDomain . Tools::getEnvValue('DOMAIN') . (Tools::getEnvValue('PORT') && !in_array($_ENV['PORT'], ['80', '443']) ? (':' . Tools::getEnvValue('PORT')) : '');\n    }\n\n    /**\n     * @param string $string\n     * @param string $obfuscateChar\n     * @return string\n     */\n    public static function obfuscateString(string $string, string $obfuscateChar = '*'): string\n    {\n        $string = Tools::removeAccent($string); // THIS IS A HACK!! Because accentued chars turn into non UTF-8 ones that break JMS Serializer\n        $chars = str_split($string);\n        $size = count($chars);\n        $min = round($size / 1.5);\n        $rand = rand($min, ($size - $min) + 1);\n        shuffle($chars);\n        for ($i = 0; $rand > $i; $i++) {\n            $chars[$i] = $obfuscateChar;\n        }\n        shuffle($chars);\n        return implode($chars);\n    }\n\n    /**\n     * @param array $parameters\n     * @param array $columnConfigs\n     * @param string $columnId\n     * @return array\n     */\n    public static function computeKanbanColumnParameters(array $parameters, array $columnConfigs, string $columnId): array\n    {\n        $columnFilter = [];\n        // If only in parameters => keep\n        // If only in filter => keep\n        // If in both\n        //   if both are arrays => intersection\n        //   else => keep filter, ignore parameter\n        // TODO KANBAN : manage complex cases where there are both filters and they conflict\n        foreach ($columnConfigs as $columnConfig) {\n            if ($columnConfig['columnId'] === $columnId) {\n                $columnFilter = $columnConfig['filter'];\n            }\n        }\n        $mergedParameters = $parameters; // copy\n        foreach ($columnFilter as $filterName => $filterValue) {\n            if (isset($mergedParameters[$filterName]) && is_array($mergedParameters[$filterName]) && is_array($filterValue)) {\n                $mergedParameters[$filterName] = array_intersect($mergedParameters[$filterName], $filterValue);\n            } else {\n                $mergedParameters[$filterName] = $filterValue;\n            }\n        }\n        return $mergedParameters;\n    }\n\n    /**\n     * @param string|null $name\n     * @param string $email\n     * @return string\n     */\n    public static function getFromEmail(?string $name, string $email): string\n    {\n        return $name ? '\"' . $name . '\" <' . $email . '>' : $email;\n    }\n\n    /**\n     * @param array $fileExtensions\n     * @return array\n     */\n    public static function fileExtensionsToMimeTypes(array $fileExtensions): array\n    {\n        $mimeTypes = [];\n        foreach ($fileExtensions as $fileExtension) {\n            $mimeTypes = array_merge($mimeTypes, self::MIMETYPE_CONVERSION[$fileExtension]);\n        }\n        return $mimeTypes;\n    }\n\n    /**\n     * @param int $qualification\n     * @return string\n     */\n    public static function getQualificationTitleFromNumber(int $qualification): string\n    {\n        $qualificationTitle = '';\n        switch ($qualification) {\n            case 0:\n                $qualificationTitle = \"Non renseigné\";\n                break;\n            case 2:\n                $qualificationTitle = \"Sans diplôme ou diplôme national du Brevet (NIVEAU 2)\";\n                break;\n            case 3:\n                $qualificationTitle = \"CAP, BEP... (NIVEAU 3)\";\n                break;\n            case 4:\n                $qualificationTitle = \"BAC : BP, BT, bac pro ou techno (NIVEAU 4)\";\n                break;\n            case 5:\n                $qualificationTitle = \"BAC+2 : DEUG, BT, DUT... (NIVEAU 5)\";\n                break;\n            case 6:\n                $qualificationTitle = \"BAC+3 ou 4 : licence, master 1, maîtrise (NIVEAU 6)\";\n                break;\n            case 7:\n                $qualificationTitle = \"BAC+5 : grade master, DEA, DESS, ingénieur... (NIVEAU 7)\";\n                break;\n            case 8:\n                $qualificationTitle = \"BAC+8 : doctorat... (NIVEAU 8)\";\n                break;\n        }\n        return $qualificationTitle;\n    }\n\n    /**\n     * @param array|null $arr\n     * @return array\n     */\n    public static function removeSpacesInKeys(?array $arr): array\n    {\n        if ($arr != null && sizeof($arr) > 0) {\n            $arr = array_combine(\n                array_map(\n                    function ($str) {\n                        return str_replace(\" \", \"_\", $str);\n                    },\n                    array_keys($arr)\n                ),\n                array_values($arr)\n            );\n        }\n        return $arr;\n    }\n\n    /**\n     * @param array $array\n     * @param string $key\n     * @return array\n     */\n    public static function findDuplicatesOnKeyInArray(array $array, string $key): array\n    {\n        return array_keys(array_diff(array_count_values(array_column($array, $key)), [1]));\n    }\n\n    public static function convertTtcToHt(float $priceTTC, Organism $organism): float\n    {\n        $rateVat = $organism->getVat();\n        if ($rateVat == 20) {\n            $convertNumber = $priceTTC / 1.2;\n            $priceHt = round($convertNumber * 100) / 100;\n        } else if ($rateVat == 5.5) {\n            $convertNumber = ($priceTTC * 100) / 1.055;\n            $priceHt = round($convertNumber * 100) / 100;\n        } else {\n            $priceHt = $priceTTC;\n        }\n        return $priceHt;\n    }\n\n    /**\n     * @param int|float|null $num\n     * @param int $precision\n     * @param int $mode\n     * @return ?float\n     */\n    public static function properRound($num, int $precision = 0, int $mode = PHP_ROUND_HALF_UP): ?float\n    {\n        // Wrapper arround round that returns null if null is provided\n        // while native round returns 0 when null is provided, which is bad\n        if (!isset($num)) {\n            return null;\n        }\n        return round($num, $precision, $mode);\n    }\n\n    /**\n     * @param string $text\n     * @param int $length\n     * @return string\n     */\n    public static function truncate(string $text, int $length): string\n    {\n        if ($length <= 3) {\n            $truncatedString = substr($text, 0, $length);\n        } else if (strlen($text) <= $length) {\n            $truncatedString = $text;\n        } else {\n            $truncatedString = substr($text, 0, $length - 3) . '...';\n        }\n        return $truncatedString;\n    }\n\n    /**\n     * @param string $jwt\n     * @return array\n     */\n    public static function unsecureDecodeJWT(string $jwt): array\n    {\n        // THIS IS UNSECURE\n        // For secure way, use :\n        // (array)JWT::decode($jwt, $key, array('RS256'))\n        list($header, $payload, $signature) = explode('.', $jwt);\n        $jsonToken = base64_decode($payload);\n        return json_decode($jsonToken, true);\n    }\n\n    /**\n     * Very naïve: does not allow callback, only first level key, does not support missing key\n     * @param array $array\n     * @param string $key\n     * @return array\n     */\n    public static function groupBy(array $array, string $key): array\n    {\n        $result = array();\n        foreach ($array as $item) {\n            $result[$item[$key]][] = $item;\n        }\n        return $result;\n    }\n\n    /**\n     * @param array|null $result\n     * @param Closure $param\n     * @return array\n     */\n    public static function array_find_returns_array(?array $result, Closure $param): array\n    {\n        foreach ($result as $x) {\n            if (call_user_func($param, $x) === true)\n                return [$x]; // result\n        }\n        return []; // not found\n    }\n\n    /**\n     * @param array $array\n     * @param callable $predicate\n     * @return mixed|null\n     */\n    public static function array_find(array $array, callable $predicate)\n    {\n        foreach ($array as $key => $value) {\n            if ($predicate($value, $key)) {\n                return $value;\n            }\n        }\n        return null;\n    }\n\n    /**\n     * Otherwise available in php 8.4\n     * @param array $array\n     * @param callable $predicate\n     * @return bool\n     */\n    public static function array_all(array $array, callable $predicate): bool\n    {\n        foreach ($array as $item) {\n            if (!$predicate($item)) {\n                return false;\n            }\n        }\n        return true;\n    }\n\n    /**\n     * Otherwise available in php 8.4\n     * @param array $array\n     * @param callable $predicate\n     * @return bool\n     */\n    public static function array_any(array $array, callable $predicate): bool\n    {\n        foreach ($array as $item) {\n            if ($predicate($item)) {\n                return true;\n            }\n        }\n        return false;\n    }\n\n    /**\n     * @param Organism $organism\n     * @return array\n     */\n    public static function retrieveWebsite(Organism $organism): array\n    {\n        $urls = [];\n        if (count($organism->getUrls()) > 0) {\n            $urls = $organism->getUrls();\n        }\n        if ($organism->getOwnedBy()) {\n            $userEmail = $organism->getOwnedBy()->getEmail();\n            $fullDomain = explode('@', $userEmail)[1];\n            $domain = explode('.', $fullDomain)[0];\n            $excludedDomainEmails = ['orange', 'gmail', 'live', 'laposte', 'wanadoo', 'outlook', 'hotmail', 'yahoo', 'free', 'icloud', 'yopmail', 'bbox', 'neuf', 'aol', 'gmx', 'mail'];\n            if (!in_array($domain, $excludedDomainEmails)) {\n                $websiteUrl = 'https://' . $fullDomain;\n                if (!in_array($websiteUrl, $urls)) {\n                    $urls[] = $websiteUrl;\n                }\n            }\n        }\n        return $urls;\n    }\n\n    /**\n     * @param AbstractTaggable $object\n     * @return array\n     */\n    public static function tagsToArray(AbstractTaggable $object): array\n    {\n        $tags = [];\n        if (!$object->getTags()->isEmpty()) {\n            /** @var Tag $tag */\n            foreach ($object->getTags() as $tag) {\n                $tags[] = $tag->getName();\n            }\n        }\n        return $tags;\n    }\n\n    /**\n     * @param string $separator\n     * @param string $string\n     * @return array\n     */\n    public static function explodeLast(string $separator, string $string): array\n    {\n        $pos = strrpos($string, $separator);\n        if ($pos === false) {\n            return [$string];\n        }\n        return [\n            substr($string, 0, $pos),\n            substr($string, $pos + strlen($separator))\n        ];\n    }\n}\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/wedof-backend/src/Library/utils/Tools.php b/wedof-backend/src/Library/utils/Tools.php
--- a/wedof-backend/src/Library/utils/Tools.php	(revision 4a8d1ed75f11bce748de81a0a2508c74bf2559e7)
+++ b/wedof-backend/src/Library/utils/Tools.php	(date 1752049243512)
@@ -6,6 +6,7 @@
 use App\Entity\Subscription;
 use App\Entity\Tag;
 use App\Exception\WedofBadRequestHttpException;
+use App\Exception\WedofConnectionException;
 use App\Library\utils\enums\PeriodTypes;
 use Beelab\TagBundle\Entity\AbstractTaggable;
 use Closure;
@@ -1463,6 +1464,25 @@
         if (count($organism->getUrls()) > 0) {
             $urls = $organism->getUrls();
         }
+
+        $siret = $organism->getSiret();
+        if ($siret) {
+            global $kernel;
+            if ($kernel) {
+                try {
+                    $automatorApiService = $kernel->getContainer()->get('App\Service\DataProviders\AutomatorApiService');
+                    $apiUrls = $automatorApiService->getOrganismUrls($organism);
+                    foreach ($apiUrls as $apiUrl) {
+                        if (!in_array($apiUrl, $urls)) {
+                            $urls[] = $apiUrl;
+                        }
+                    }
+                } catch (Exception $e) {
+                    throw new WedofConnectionException("Enable to get result");
+                }
+            }
+        }
+
         if ($organism->getOwnedBy()) {
             $userEmail = $organism->getOwnedBy()->getEmail();
             $fullDomain = explode('@', $userEmail)[1];
Index: wedof-backend/src/Service/DataProviders/AutomatorApiService.php
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+><?php\n\nnamespace App\\Service\\DataProviders;\n\nuse App\\Entity\\Certification;\nuse App\\Entity\\Connection;\nuse App\\Entity\\Organism;\nuse App\\Entity\\User;\nuse App\\Exception\\WedofConflictHttpException;\nuse App\\Exception\\WedofConnectionException;\nuse App\\Library\\utils\\enums\\DataProviders;\nuse App\\Repository\\EndPointStatusRepository;\nuse App\\Service\\CertificationPartnerService;\nuse App\\Service\\ConfigService;\nuse App\\Service\\ConnectionService;\nuse Doctrine\\ORM\\Exception\\ORMException;\nuse Doctrine\\ORM\\NonUniqueResultException;\nuse Doctrine\\ORM\\NoResultException;\nuse Doctrine\\ORM\\OptimisticLockException;\nuse ErrorException;\nuse Psr\\Container\\ContainerInterface;\nuse Psr\\Log\\LoggerInterface;\nuse Symfony\\Component\\EventDispatcher\\EventDispatcherInterface;\nuse Symfony\\Component\\HttpFoundation\\File\\File;\nuse Symfony\\Component\\HttpFoundation\\RequestStack;\nuse Symfony\\Component\\Security\\Core\\Security;\nuse Symfony\\Contracts\\HttpClient\\Exception\\ClientExceptionInterface;\nuse Symfony\\Contracts\\HttpClient\\Exception\\RedirectionExceptionInterface;\nuse Symfony\\Contracts\\HttpClient\\Exception\\ServerExceptionInterface;\nuse Symfony\\Contracts\\HttpClient\\Exception\\TransportExceptionInterface;\nuse Throwable;\nuse const true;\n\nclass AutomatorApiService extends BaseApiService\n{\n    public CertificationPartnerService $certificationPartnerService;\n    public ContainerInterface $container;\n\n    /**\n     * @param ConfigService $configService\n     * @param ConnectionService $connectionService\n     * @param RequestStack $requestStack\n     * @param EndPointStatusRepository $endPointStatusRepository\n     * @param EventDispatcherInterface $dispatcher\n     * @param CertificationPartnerService $certificationPartnerService\n     * @param ContainerInterface $container\n     * @param LoggerInterface $logger\n     * @param Security $security\n     */\n    public function __construct(\n        ConfigService $configService,\n        ConnectionService $connectionService,\n        RequestStack $requestStack,\n        EndPointStatusRepository $endPointStatusRepository,\n        EventDispatcherInterface $dispatcher,\n        CertificationPartnerService $certificationPartnerService,\n        ContainerInterface $container,\n        LoggerInterface $logger,\n        Security $security)\n    {\n        parent::__construct(DataProviders::AUTOMATOR(), $configService, $connectionService, $requestStack, $endPointStatusRepository, $dispatcher, $logger, $security);\n        $this->certificationPartnerService = $certificationPartnerService;\n        $this->container = $container;\n    }\n\n    /**\n     * @param array $parameters\n     * @param Organism|null $organism\n     * @param string|null $contentType\n     * @return array|null\n     * @throws ClientExceptionInterface\n     * @throws ErrorException\n     * @throws NoResultException\n     * @throws NonUniqueResultException\n     * @throws ORMException\n     * @throws OptimisticLockException\n     * @throws RedirectionExceptionInterface\n     * @throws ServerExceptionInterface\n     * @throws Throwable\n     * @throws TransportExceptionInterface\n     * @throws WedofConnectionException\n     */\n    public function generateCertificate(array $parameters, Organism $organism = null, string $contentType = null): ?array\n    {\n        $options = [\n            'authenticationHeaderName' => $_ENV['AUTOMATOR_AUTH_HEADER'],\n            'authenticationHeaderValue' => $_ENV['AUTOMATOR_AUTH'],\n            'fullResponse' => true,\n            'parameters' => $parameters,\n            'method' => 'POST',\n        ];\n        if ($contentType) {\n            $options = array_merge($options, ['content-type' => $contentType]);\n        }\n        if ($organism) {\n            $options = array_merge($options, ['organism' => $organism]);\n        }\n        return $this->sendRequest($_ENV['AUTOMATOR_BASE_URI'] . 'generate-certificate', $options);\n    }\n\n    /**\n     * @param string $siret\n     * @return array|null\n     * @throws ClientExceptionInterface\n     * @throws ErrorException\n     * @throws NoResultException\n     * @throws NonUniqueResultException\n     * @throws ORMException\n     * @throws OptimisticLockException\n     * @throws RedirectionExceptionInterface\n     * @throws ServerExceptionInterface\n     * @throws Throwable\n     * @throws TransportExceptionInterface\n     * @throws WedofConnectionException\n     */\n    public function getExtraDataOrganism(string $siret): ?array\n    {\n        $options = [\n            'authenticationHeaderName' => $_ENV['AUTOMATOR_AUTH_HEADER'],\n            'authenticationHeaderValue' => $_ENV['AUTOMATOR_AUTH'],\n            'parameters' => [\"siret\" => $siret],\n            'content-type' => \"application/json\",\n            'method' => 'POST'\n        ];\n        return $this->sendRequest($_ENV['AUTOMATOR_BASE_URI'] . 'get-extra-data-organism', $options);\n    }\n\n    /**\n     * @param Certification $certification\n     * @param Organism $certifier\n     * @return array|null\n     * @throws ClientExceptionInterface\n     * @throws ErrorException\n     * @throws NoResultException\n     * @throws NonUniqueResultException\n     * @throws ORMException\n     * @throws OptimisticLockException\n     * @throws RedirectionExceptionInterface\n     * @throws ServerExceptionInterface\n     * @throws Throwable\n     * @throws TransportExceptionInterface\n     */\n    public function generateCertificateTemplate(Certification $certification, Organism $certifier): ?array\n    {\n        $options = [\n            'authenticationHeaderName' => $_ENV['AUTOMATOR_AUTH_HEADER'],\n            'authenticationHeaderValue' => $_ENV['AUTOMATOR_AUTH'],\n            'fullResponse' => true,\n            'parameters' => [\n                'siret' => $certifier->getSiret(),\n                'certifInfo' => $certification->getCertifInfo(),\n                'name' => $certification->getExternalId()\n            ],\n            'method' => 'POST',\n            'organism' => $certifier,\n            'files' => [\n                'file' => $certification->getCertificateTemplateFile()->getPath() . \"/\" . $certification->getCertificateTemplateFile()->getFilename(),\n            ],\n        ];\n        return $this->sendRequest($_ENV['AUTOMATOR_BASE_URI'] . 'template-certificate', $options);\n    }\n\n    /**\n     * @param string $googleId\n     * @param string $certifInfo\n     * @return array|null\n     * @throws ClientExceptionInterface\n     * @throws ErrorException\n     * @throws NoResultException\n     * @throws NonUniqueResultException\n     * @throws ORMException\n     * @throws OptimisticLockException\n     * @throws RedirectionExceptionInterface\n     * @throws ServerExceptionInterface\n     * @throws Throwable\n     * @throws TransportExceptionInterface\n     * @throws WedofConnectionException\n     */\n    public function updateCertificateTemplateThumbnail(string $googleId, string $certifInfo): ?array\n    {\n        $options = [\n            'authenticationHeaderName' => $_ENV['AUTOMATOR_AUTH_HEADER'],\n            'authenticationHeaderValue' => $_ENV['AUTOMATOR_AUTH'],\n            'fullResponse' => true,\n            'parameters' => [\n                'uid' => $googleId,\n                'certifInfo' => $certifInfo\n            ],\n            'method' => 'POST'\n        ];\n        return $this->sendRequest($_ENV['AUTOMATOR_BASE_URI'] . 'template-thumbnail', $options);\n    }\n\n    /**\n     * @param array $parameters\n     * @param Organism $organism\n     * @param File $template\n     * @return array|null\n     * @throws ClientExceptionInterface\n     * @throws ErrorException\n     * @throws NoResultException\n     * @throws NonUniqueResultException\n     * @throws ORMException\n     * @throws OptimisticLockException\n     * @throws RedirectionExceptionInterface\n     * @throws ServerExceptionInterface\n     * @throws Throwable\n     * @throws TransportExceptionInterface\n     * @throws WedofConnectionException\n     */\n    public function generateDocumentTemplate(array $parameters, Organism $organism, File $template): ?array\n    {\n        $options = [\n            'authenticationHeaderName' => $_ENV['AUTOMATOR_AUTH_HEADER'],\n            'authenticationHeaderValue' => $_ENV['AUTOMATOR_AUTH'],\n            'fullResponse' => true,\n            'parameters' => $parameters,\n            'method' => 'POST',\n            'organism' => $organism,\n            'files' => [\n                'file' => $template\n            ]\n        ];\n        return $this->sendRequest($_ENV['AUTOMATOR_BASE_URI'] . 'template-document', $options);\n    }\n\n    /**\n     * @param string $path\n     * @param string|null $googleId\n     * @param string|null $fileName\n     * @return array|true[]|null\n     * @throws ClientExceptionInterface\n     * @throws ErrorException\n     * @throws NoResultException\n     * @throws NonUniqueResultException\n     * @throws ORMException\n     * @throws OptimisticLockException\n     * @throws RedirectionExceptionInterface\n     * @throws ServerExceptionInterface\n     * @throws Throwable\n     * @throws TransportExceptionInterface\n     * @throws WedofConnectionException\n     */\n    public function deleteDocumentTemplate(string $path, string $googleId = null, string $fileName = null): array\n    {\n        $options = [\n            'authenticationHeaderName' => $_ENV['AUTOMATOR_AUTH_HEADER'],\n            'authenticationHeaderValue' => $_ENV['AUTOMATOR_AUTH'],\n            'fullResponse' => true,\n            'parameters' => [\n                'id' => $googleId,\n                'fileName' => $fileName\n            ],\n            'method' => 'POST',\n        ];\n        return $this->sendRequest($_ENV['AUTOMATOR_BASE_URI'] . $path, $options);\n    }\n\n    /**\n     * @param array $fileType\n     * @param $computedContext\n     * @param string $targetFileType\n     * @return array|null\n     * @throws ClientExceptionInterface\n     * @throws ErrorException\n     * @throws NoResultException\n     * @throws NonUniqueResultException\n     * @throws ORMException\n     * @throws OptimisticLockException\n     * @throws RedirectionExceptionInterface\n     * @throws ServerExceptionInterface\n     * @throws Throwable\n     * @throws TransportExceptionInterface\n     * @throws WedofConnectionException\n     */\n    public function generateDocumentFromTemplateAndContext(array $fileType, $computedContext, string $targetFileType = 'pdf'): ?array\n    {\n        $options = [\n            'authenticationHeaderName' => $_ENV['AUTOMATOR_AUTH_HEADER'],\n            'authenticationHeaderValue' => $_ENV['AUTOMATOR_AUTH'],\n            'fullResponse' => true,\n            'parameters' => [\n                'uid' => $fileType['googleId'],\n                'fileName' => $fileType['name'],\n                'fileTypeId' => $fileType['id'],\n                'targetFileType' => $targetFileType,\n                'computedContext' => json_encode($computedContext)\n            ],\n            'method' => 'POST',\n        ];\n        return $this->sendRequest($_ENV['AUTOMATOR_BASE_URI'] . 'template-document/generate', $options);\n    }\n\n    /**\n     * @param string|null $mimeType\n     * @param string|null $googleId\n     * @param string|null $fileName\n     * @return array|null\n     * @throws ClientExceptionInterface\n     * @throws ErrorException\n     * @throws NoResultException\n     * @throws NonUniqueResultException\n     * @throws ORMException\n     * @throws OptimisticLockException\n     * @throws RedirectionExceptionInterface\n     * @throws ServerExceptionInterface\n     * @throws Throwable\n     * @throws TransportExceptionInterface\n     * @throws WedofConnectionException\n     */\n    public function createOrUpdateTemplate(string $mimeType = null, string $googleId = null, string $fileName = null): ?array\n    {\n        $options = [\n            'authenticationHeaderName' => $_ENV['AUTOMATOR_AUTH_HEADER'],\n            'authenticationHeaderValue' => $_ENV['AUTOMATOR_AUTH'],\n            'fullResponse' => true,\n            'parameters' => [\n                'googleId' => $googleId,\n                'fileName' => $fileName,\n                'mimeType' => $mimeType\n            ],\n            'method' => 'POST',\n        ];\n        return $this->sendRequest($_ENV['AUTOMATOR_BASE_URI'] . 'template-document/scratch', $options);\n    }\n\n    /**\n     * @param string $templateName\n     * @param string $folderName\n     * @param string|null $fileName\n     * @return array|null\n     * @throws ClientExceptionInterface\n     * @throws ErrorException\n     * @throws NoResultException\n     * @throws NonUniqueResultException\n     * @throws ORMException\n     * @throws OptimisticLockException\n     * @throws RedirectionExceptionInterface\n     * @throws ServerExceptionInterface\n     * @throws Throwable\n     * @throws TransportExceptionInterface\n     * @throws WedofConnectionException\n     */\n    public function duplicateFromTemplate(string $templateName, string $folderName, string $fileName): ?array\n    {\n        $options = [\n            'authenticationHeaderName' => $_ENV['AUTOMATOR_AUTH_HEADER'],\n            'authenticationHeaderValue' => $_ENV['AUTOMATOR_AUTH'],\n            'fullResponse' => true,\n            'parameters' => [\n                'templateName' => $templateName,\n                'folderName' => $folderName,\n                'fileName' => $fileName\n            ],\n            'method' => 'POST',\n        ];\n        return $this->sendRequest($_ENV['AUTOMATOR_BASE_URI'] . 'duplicate-templateWedofFileTypes', $options);\n    }\n\n    /**\n     * @param string $googleIdToDuplicate\n     * @param string|null $fileName\n     * @return array|null\n     * @throws ClientExceptionInterface\n     * @throws ErrorException\n     * @throws NoResultException\n     * @throws NonUniqueResultException\n     * @throws ORMException\n     * @throws OptimisticLockException\n     * @throws RedirectionExceptionInterface\n     * @throws ServerExceptionInterface\n     * @throws Throwable\n     * @throws TransportExceptionInterface\n     * @throws WedofConnectionException\n     */\n    public function duplicateFromTemplateGoogleId(string $googleIdToDuplicate, string $fileName): ?array\n    {\n        $options = [\n            'authenticationHeaderName' => $_ENV['AUTOMATOR_AUTH_HEADER'],\n            'authenticationHeaderValue' => $_ENV['AUTOMATOR_AUTH'],\n            'fullResponse' => true,\n            'parameters' => [\n                'googleIdToDuplicate' => $googleIdToDuplicate,\n                'fileName' => $fileName\n            ],\n            'method' => 'POST',\n        ];\n        return $this->sendRequest($_ENV['AUTOMATOR_BASE_URI'] . 'template-document/duplicate', $options);\n    }\n\n    /**\n     * @param string $link\n     * @param string $fileName\n     * @return array|null\n     * @throws ClientExceptionInterface\n     * @throws ErrorException\n     * @throws NoResultException\n     * @throws NonUniqueResultException\n     * @throws ORMException\n     * @throws OptimisticLockException\n     * @throws RedirectionExceptionInterface\n     * @throws ServerExceptionInterface\n     * @throws Throwable\n     * @throws TransportExceptionInterface\n     * @throws WedofConnectionException\n     */\n    public function generatePdfFromLink(string $link, string $fileName): ?array\n    {\n        $options = [\n            'authenticationHeaderName' => $_ENV['AUTOMATOR_AUTH_HEADER'],\n            'authenticationHeaderValue' => $_ENV['AUTOMATOR_AUTH'],\n            'fullResponse' => true,\n            'parameters' => [\n                'link' => $link,\n                'fileName' => $fileName\n            ],\n            'method' => 'POST',\n        ];\n        return $this->sendRequest($_ENV['AUTOMATOR_BASE_URI'] . 'file-convert', $options);\n    }\n\n    /**\n     * @param string $field\n     * @param string $folderId\n     * @return array|null\n     * @throws ClientExceptionInterface\n     * @throws ErrorException\n     * @throws NoResultException\n     * @throws NonUniqueResultException\n     * @throws ORMException\n     * @throws OptimisticLockException\n     * @throws RedirectionExceptionInterface\n     * @throws ServerExceptionInterface\n     * @throws Throwable\n     * @throws TransportExceptionInterface\n     * @throws WedofConnectionException\n     */\n    public function listFileTypesWedof(string $field, string $folderId): ?array\n    {\n        $options = [\n            'authenticationHeaderName' => $_ENV['AUTOMATOR_AUTH_HEADER'],\n            'authenticationHeaderValue' => $_ENV['AUTOMATOR_AUTH'],\n            'fullResponse' => true,\n            'parameters' => [\n                'folderId' => $folderId,\n                'field' => $field\n            ],\n            'method' => 'GET',\n        ];\n        return $this->sendRequest($_ENV['AUTOMATOR_BASE_URI'] . 'templateWedofFileTypes', $options);\n    }\n\n    /**\n     * @throws ORMException\n     * @throws RedirectionExceptionInterface\n     * @throws ClientExceptionInterface\n     * @throws WedofConnectionException\n     * @throws OptimisticLockException\n     * @throws TransportExceptionInterface\n     * @throws Throwable\n     * @throws ServerExceptionInterface\n     * @throws NonUniqueResultException\n     * @throws NoResultException\n     * @throws ErrorException\n     */\n    public function getLinkedInPageUrlInfos(?Organism $organism): ?array\n    {\n        $options = [\n            'authenticationHeaderName' => $_ENV['AUTOMATOR_AUTH_HEADER'],\n            'authenticationHeaderValue' => $_ENV['AUTOMATOR_AUTH'],\n            'parameters' => [\n                'organisationUrl' => $organism->getLinkedInPageUrl(),\n            ],\n            'method' => 'POST',\n            'organism' => $organism\n        ];\n        return $this->sendRequest($_ENV['AUTOMATOR_BASE_URI'] . 'get-linkedin-organisation-id', $options);\n    }\n\n    /**\n     * @param Organism $organism\n     * @param User $user\n     * @param array $body\n     * @return array\n     * @throws ClientExceptionInterface\n     * @throws ErrorException\n     * @throws NoResultException\n     * @throws NonUniqueResultException\n     * @throws ORMException\n     * @throws OptimisticLockException\n     * @throws RedirectionExceptionInterface\n     * @throws ServerExceptionInterface\n     * @throws Throwable\n     * @throws TransportExceptionInterface\n     * @throws WedofConnectionException\n     */\n    public function importCertificationFolders(Organism $organism, User $user, array $body): array\n    {\n        $options = [\n            'authenticationHeaderName' => $_ENV['KASTORR_AUTH_HEADER'],\n            'authenticationHeaderValue' => $_ENV['KASTORR_AUTH'],\n            'parameters' => [\n                'siret' => $organism->getSiret(),\n                'name' => $organism->getName(),\n                'userName' => $user->getName(),\n                'userEmail' => $user->getEmail(),\n                'apiKey' => $user->getApiTokens()[0]->getToken()\n            ],\n            'method' => 'POST',\n            'organism' => $organism,\n            'files' => [\n                'file' => $body['file']\n            ],\n        ];\n        return $this->sendRequest($_ENV['KASTORR_BASE_URI'] . 'import-certification-folders', $options);\n    }\n\n    /**\n     * @param DataProviders $dataProvider\n     * @param string $siret\n     * @return array\n     * @throws ClientExceptionInterface\n     * @throws ErrorException\n     * @throws NoResultException\n     * @throws NonUniqueResultException\n     * @throws ORMException\n     * @throws OptimisticLockException\n     * @throws RedirectionExceptionInterface\n     * @throws ServerExceptionInterface\n     * @throws Throwable\n     * @throws TransportExceptionInterface\n     * @throws WedofConnectionException\n     */\n    public function checkExistAtDataProvider(DataProviders $dataProvider, string $siret): array\n    {\n        $options = [\n            'authenticationHeaderName' => $_ENV['AUTOMATOR_AUTH_HEADER'],\n            'authenticationHeaderValue' => $_ENV['AUTOMATOR_AUTH'],\n            'parameters' => [\n                'dataprovider' => $dataProvider->getValue(),\n                'siret' => $siret,\n            ],\n            'method' => 'GET',\n        ];\n        return $this->sendRequest($_ENV['AUTOMATOR_BASE_URI'] . 'dataprovider/checkExist', $options);\n    }\n\n    /// BASE API SERVICE STUFF\n\n    public function getMaxAttemptsBeforeStop(): int\n    {\n        return 3;\n    }\n\n    public function authenticate(Connection $connection, bool $checkOrganismAccess = true): array\n    {\n        throw new WedofConflictHttpException(\"Pas d'implémentation de la fonction authenticate dans AutomatorApiService\");\n    }\n\n    public function checkBeforeHabilitate(Organism $organism, array $params = null)\n    {\n    }\n\n    public function habilitate(Organism $organism, Connection $connection, array $params): bool\n    {\n        return true;\n    }\n\n    public function getUsername(Connection $connection): string\n    {\n        return '';\n    }\n\n    public function requiresAuthentication(): bool\n    {\n        return false;\n    }\n\n    public function setActiveOrganism(Organism $organism): bool\n    {\n        return true;\n    }\n\n    public function setAuthentication(array $options = []): array\n    {\n        return [];\n    }\n}\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/wedof-backend/src/Service/DataProviders/AutomatorApiService.php b/wedof-backend/src/Service/DataProviders/AutomatorApiService.php
--- a/wedof-backend/src/Service/DataProviders/AutomatorApiService.php	(revision 4a8d1ed75f11bce748de81a0a2508c74bf2559e7)
+++ b/wedof-backend/src/Service/DataProviders/AutomatorApiService.php	(date 1751879987903)
@@ -585,4 +585,53 @@
     {
         return [];
     }
+
+    /**
+     * @param Organism $organism
+     * @return array
+     * @throws ClientExceptionInterface
+     * @throws ErrorException
+     * @throws NoResultException
+     * @throws NonUniqueResultException
+     * @throws ORMException
+     * @throws OptimisticLockException
+     * @throws RedirectionExceptionInterface
+     * @throws ServerExceptionInterface
+     * @throws Throwable
+     * @throws TransportExceptionInterface
+     * @throws WedofConnectionException
+     */
+    public function getOrganismUrls(Organism $organism): array
+    {
+        $urls = [];
+        $siret = $organism->getSiret();
+
+        if (empty($siret)) {
+            return $urls;
+        }
+
+        $options = [
+            'content-type' => "application/json",
+            'parameters' => [
+                'siret' => $siret
+            ],
+            'method' => 'GET',
+        ];
+
+        $response = $this->sendRequest($_ENV['KASTORR_BASE_URI'] . '/getUrls', $options);
+
+        if (is_array($response) && !empty($response)) {
+            foreach ($response as $item) {
+                if (isset($item['output']) && isset($item['output']['urls']) && is_array($item['output']['urls'])) {
+                    foreach ($item['output']['urls'] as $apiUrl) {
+                        if (!empty($apiUrl)) {
+                            $urls[] = $apiUrl;
+                        }
+                    }
+                }
+            }
+        }
+
+        return $urls;
+    }
 }
