<?php
// src/Controller/api/OrganismController.php

namespace App\Controller\api;

use App\Entity\ApiToken;
use App\Entity\Organism;
use App\Entity\User;
use App\Exception\WedofAccessDeniedHttpException;
use App\Exception\WedofBadRequestHttpException;
use App\Exception\WedofConflictHttpException;
use App\Exception\WedofConnectionException;
use App\Exception\WedofNotFoundHttpException;
use App\Library\utils\enums\ConnectionStates;
use App\Library\utils\enums\DataProviders;
use App\Library\utils\enums\OrganismTypes;
use App\Library\utils\enums\SubscriptionCertifierTypes;
use App\Library\utils\enums\SubscriptionTrainingTypes;
use App\Library\utils\SimpleXMLElementTools;
use App\Library\utils\Tools;
use App\Security\Voter\OrganismVoter;
use App\Service\AccessService;
use App\Service\CatalogXMLService;
use App\Service\CertificationService;
use App\Service\DataProviders\CpfApiService;
use App\Service\OrganismService;
use App\Service\SubscriptionService;
use App\Service\UserService;
use DateTime;
use DateTimeZone;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\NonUniqueResultException;
use Doctrine\ORM\NoResultException;
use Doctrine\ORM\OptimisticLockException;
use ErrorException;
use Exception;
use Firebase\JWT\JWT;
use FOS\RestBundle\Context\Context;
use FOS\RestBundle\Controller\Annotations as Rest;
use FOS\RestBundle\Request\ParamFetcherInterface;
use FOS\RestBundle\View\View;
use Knp\Component\Pager\PaginatorInterface;
use Lexik\Bundle\JWTAuthenticationBundle\Security\Http\Authentication\AuthenticationSuccessHandler;
use Nelmio\ApiDocBundle\Annotation as ApiDoc;
use OpenApi\Annotations as OA;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\IsGranted;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Security;
use SLLH\IsoCodesValidator\Constraints\Siret;
use Symfony\Component\HttpFoundation\File\File;
use Symfony\Component\HttpFoundation\File\UploadedFile;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\StreamedResponse;
use Symfony\Component\Validator\Constraints as Assert;
use Symfony\Component\Validator\ConstraintViolationListInterface;
use Symfony\Component\Validator\Validation;
use Symfony\Contracts\HttpClient\Exception\ClientExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\RedirectionExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\ServerExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\TransportExceptionInterface;
use Throwable;
use Vich\UploaderBundle\Handler\DownloadHandler;

/**
 * Class OrganismController
 * @package App\Controller\api
 *
 * @OA\Tag(name="Organism")
 * @ApiDoc\Security(name="accessCode")
 */
class OrganismController extends AbstractWedofController
{
    //-------------------
    // METHODES PUBLIQUES
    //-------------------
    /**
     * @Rest\Get("/app/public/organisms", host="{organismName}.{domain}", defaults={"domain"="%domain%"},requirements={"domain"="%domain%"})
     * @Rest\View(StatusCode = 200, serializerGroups={"public"})
     *
     * @param string $organismName
     * @param OrganismService $organismService
     * @return ?Organism
     * @throws Throwable
     */
    public function showPublicBySubDomain(string $organismName, OrganismService $organismService): ?Organism
    {
        return $organismService->getOrganism(["subDomain" => $organismName]);
    }

    /**
     * @Rest\Get("/app/organisms/{siret}/ownerEmail")
     * @Rest\View(StatusCode = 200)
     *
     * @param Organism $organism
     * @return Response|JsonResponse
     */
    public function showOwnerEmail(Organism $organism): Response
    {
        $owner = $organism->getOwnedBy();
        if (!$owner) {
            return new Response('', 204);
        }
        return new JsonResponse([
            "email" => Tools::obfuscateEmailAddress($owner->getEmail())
        ]);
    }

    /**
     * @Rest\Get("/app/organisms/me", name="organism_show_me")
     * @Rest\Get("/api/organisms/me", name="my_organism")
     * @Security("is_granted('ROLE_USER') or is_granted('ROLE_SALES')", message="not allowed")
     * @Rest\View(StatusCode = 200, serializerGroups={"app"})
     *
     * @ApiDoc\Areas({"organisms", "default"})
     * @OA\Get (
     *     summary="Récupération de l'organisme courant.",
     *     description="Récupération de l'organisme associé à l'utilisateur courant. Via OAuth2, cet appel nécessite le scope 'user:read'."
     * )
     *
     * @OA\Response(
     *     response=200,
     *     description="Un json contenant les informations de l'organimse.",
     *     @ApiDoc\Model(type=Organism::class))
     *     )
     * )
     *
     * @return Organism
     */
    public function me(): ?Organism
    {
        /** @var User $user */
        $user = $this->getUser();
        return $user->getMainOrganism(); // on registration the organism can be null
    }

    /**
     * @Rest\Get("/app/public/organisms/verifyIdentitySuccess")
     */
    public function verifyIdentitySuccess(OrganismService $organismService): Response
    {
        $emailAddress = $organismService->verifySendAsSuccess();
        return $this->render('organism/verifyIdentitySuccess.html.twig', [
            'email' => $emailAddress
        ]);
    }

    /**
     * @Rest\Get("/app/public/organisms/verifyIdentityFailure")
     */
    public function verifyIdentityFailure(): Response
    {
        return $this->render('organism/verifyIdentityFailure.html.twig');
    }

    /**
     * @Rest\Post("/app/organisms/{siret}/importCertificationFolders")
     * @IsGranted(OrganismVoter::EDIT, subject="organism")
     * @Rest\View(StatusCode = 200, serializerGroups={"app"})
     *
     * @param Organism $organism
     * @param OrganismService $organismService
     * @return Organism
     * @throws ClientExceptionInterface
     * @throws ErrorException
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws Throwable
     * @throws TransportExceptionInterface
     * @throws WedofConnectionException
     */
    public function importCertificationFolders(Organism $organism, OrganismService $organismService): Organism
    {
        /** @var User $user */
        $user = $this->getUser();
        $subscription = $organism->getSubscription();
        if (!$user->isOwner() || $organism->getOwnedBy() !== $user || !$organism->isCertifierOrganism() || !$subscription->isAllowCertifiers() || !in_array($subscription->getCertifierType(), SubscriptionCertifierTypes::getPaidCertifierTypes())) {
            throw new WedofBadRequestHttpException("Erreur, vous n'avez pas les droits pour effectuer cette action d'import.");
        }
        if (!$organism->isAllowImportCertificationFolders()) {
            throw new WedofBadRequestHttpException("Erreur, un import de dossiers de certification est déjà en cours.");
        }
        $body = $this->getData();
        if (empty($body) || !isset($body['file']) || !($body['file'] instanceof File)) {
            throw new WedofBadRequestHttpException("Erreur, vous devez renvoyer un fichier.");
        }
        return $organismService->importCertificationFolders($organism, $user, $body);
    }

    //-------------------
    // API (and some APP) ENDPOINTS
    //-------------------
    /**
     * @Rest\Get("/app/public/resellers/{siret}/connect/{jwt}", name="reseller_connect_public", requirements={"siret"="\d{14}"})
     * @Rest\View(StatusCode = 200, serializerGroups={"app"})
     * @throws Throwable
     */
    public function resellerConnectAsOrganism(Organism $reseller, string $jwt, Request $request, SubscriptionService $subscriptionService, OrganismService $organismService, UserService $userService, AuthenticationSuccessHandler $authenticationSuccessHandler): Response
    {
        //json or webcomponent?
        $component = filter_var($request->get('component', false), FILTER_VALIDATE_BOOLEAN);
        $connectionType = DataProviders::from($request->get('connection', "cpf"));
        if (!$reseller->isReseller() || !$reseller->getResellerMetadata()) {
            throw new WedofBadRequestHttpException("Your organism isn't a reseller, <NAME_EMAIL> to get an offer");
        }
        //get jwt and check validity
        try {
            /** @var ApiToken $apiToken */
            $apiToken = $reseller->getOwnedBy()->getApiTokens()->first();
            $data = (array)JWT::decode($jwt, $apiToken->getToken(), array('HS256'));
            $data = $this->prepareJwtData($data);
            //TODO
            //$this->validateJwtData($data);
        } catch (Exception $e) {
            throw new WedofBadRequestHttpException("Invalid connect (jwt invalid)");
        }
        //get organism
        $resellerBaseUrl = Tools::getSubdomainForOrganism($reseller);
        $organism = $organismService->getBySiret($data['organism']['siret']);
        if ($organism && in_array($organism->getApe(), $reseller->getResellerMetadata()['allowedApeCode']) && $organism !== $reseller) {
            //TODO WTF SECURITY , maybe check the connections ??
            $organism = $this->checkInitialization($organism, $data, $subscriptionService, $userService);
            $tokens = json_decode($authenticationSuccessHandler->handleAuthenticationSuccess($organism->getOwnedBy())->getContent(), true);
            $connectData = [
                'baseUrl' => $resellerBaseUrl,
                "url" => "/connect/?token={$tokens['token']}&refresh_token={$tokens['refresh_token']}"
            ];
        } else if (!$organism) {
            $connectData = [
                "baseUrl" => $resellerBaseUrl,
                "url" => "/app/public/resellers/{$reseller->getSiret()}/initialize/{$jwt}"
            ];
        } else {
            throw new WedofConflictHttpException();
        }
        $connectionStatus = $this->getConnectionStatusForCustomerReseller($connectionType, $organism);
        $connectData = array_merge([
            "connexions" => [
                "state" => $connectionStatus['state'],
                "description" => $connectionStatus['text'],
            ]
        ], $connectData);
        if ($component) {
            $variables = [
                'baseUrl' => $connectData['baseUrl'],
                'text' => $connectionStatus['text'],
                'state' => $connectionStatus['state'],
                'class' => filter_var($request->get('inverted', false), FILTER_VALIDATE_BOOLEAN) ? "inverted" : ""
            ];
            $connectData["component"] = $this->container->get('twig')->render("organism/resellerConnectButton.html.twig", $variables);
        }
        return new Response(json_encode($connectData));
    }

    /**
     * @Rest\Get("/app/public/resellers/{siret}/initialize/{jwt}", name="reseller_initialize_public", requirements={"siret"="\d{14}"})
     * @Rest\View(StatusCode = 200, serializerGroups={"app"})
     * @throws Throwable
     */
    public function resellerInitializeOrganism(Organism $reseller, string $jwt, SubscriptionService $subscriptionService, OrganismService $organismService, UserService $userService, AuthenticationSuccessHandler $authenticationSuccessHandler): Response
    {
        if (!$reseller->isReseller() || !$reseller->getResellerMetadata()) {
            throw new WedofBadRequestHttpException("Your organism isn't a reseller, <NAME_EMAIL> to get an offer");
        }
        //get jwt and check validity
        try {
            /** @var ApiToken $apiToken */
            $apiToken = $reseller->getOwnedBy()->getApiTokens()->first();
            $data = (array)JWT::decode($jwt, $apiToken->getToken(), array('HS256'));
            $data = $this->prepareJwtData($data);
            //TODO
            //$this->validateJwtData($data);
        } catch (Exception $e) {
            throw new WedofBadRequestHttpException("Invalid connect (jwt invalid)");
        }
        //get organism
        $organism = $organismService->getBySiret($data['organism']['siret']);
        //create if not exist and set reseller
        if (!$organism || $organism->getReseller() === $reseller) {
            $organism = $organismService->getOrganism(['siret' => $data['organism']['siret']]);
            if (in_array($organism->getApe(), $reseller->getResellerMetadata()['allowedApeCode'])) {
                $organism = $organismService->createOrUpdate(['reseller' => $reseller, 'agreement' => $data['organism']['agreement'] ?? null], $organism);
                $organism = $this->checkInitialization($organism, $data, $subscriptionService, $userService);
                $resellerBaseUrl = Tools::getSubdomainForOrganism($reseller);
                $tokens = json_decode($authenticationSuccessHandler->handleAuthenticationSuccess($organism->getOwnedBy())->getContent(), true);
                return new RedirectResponse($resellerBaseUrl . "/connect/?token={$tokens['token']}&refresh_token={$tokens['refresh_token']}");
            }
        }
        throw new WedofConflictHttpException();
    }

    /**
     * @Rest\Post("/app/organisms/verifySendAs/{email}", requirements={"email" = ".*@{1}.*"})
     * @Rest\View(StatusCode = 204)
     *
     */
    public function verifySendAs(string $email, OrganismService $organismService): void
    {
        /** @var User $user */
        $user = $this->getUser();
        $organism = $user->getMainOrganism();
        if ($user !== $organism->getOwnedBy()) {
            throw new WedofBadRequestHttpException("Erreur, seul le propriétaire peut ajouter un expéditeur");
        }
        $organismService->verifySendAs($organism, $email);
    }

    /**
     * @Rest\Delete("/app/organisms/deleteSendAs/{email}", requirements={"email" = ".*@{1}.*"})
     * @Rest\View(StatusCode = 204)
     *
     */
    public function deleteSendAs(string $email, OrganismService $organismService): void
    {
        /** @var User $user */
        $user = $this->getUser();
        $organism = $user->getMainOrganism();
        if ($user !== $organism->getOwnedBy()) {
            throw new WedofBadRequestHttpException("Erreur, seul le propriétaire peut retirer un expéditeur");
        }
        $organismService->deleteSendAs($organism, $email);
    }

    /**
     * @Rest\Get("/app/attendees/organisms/{siret}", name="organism_show_attendee", requirements={"siret"="\d{14}"})
     * @Rest\Get("/app/public/organisms/{siret}", name="organism_show_public", requirements={"siret"="\d{14}"})
     * @Rest\Get("/api/organisms/{siret}", name="organism_show", requirements={"siret"="\d{14}"})
     * It will try to create organism if siret exist
     * @Rest\View(StatusCode = 200)
     *
     * @ApiDoc\Areas({"organisms", "default"})
     * @OA\Get (
     *     summary="Récupération d'un organisme.",
     *     description="Récupération d'un organisme par son siret."
     * )
     *
     * @OA\Response(
     *     response=200,
     *     description="Un json contenant les informations de l'organisme",
     *     @OA\MediaType(mediaType="application/json",
     *         @OA\Schema(ref="#/components/schemas/Organism")
     *     )
     * )
     *
     * @OA\Parameter(
     *     name="siret",
     *     in="path",
     *     description="Siret de l'organisme",
     *     @OA\Schema(type="string")
     * )
     *
     * @param OrganismService $organismService
     * @param string|null $siret
     * @param Request $request
     * @return Response
     * @throws Throwable
     */
    public function show(OrganismService $organismService, string $siret, Request $request): Response
    {
        $context = new Context();
        $validator = Validation::createValidator();
        $refresh = filter_var($request->get('refresh', false), FILTER_VALIDATE_BOOLEAN);
        $violations = $validator->validate($siret, new Siret());
        if (!$violations->count()) {
            $organism = $organismService->getOrganism(['siret' => $siret], $refresh);
            if ($organism) {
                if ($this->getUser() && $this->isGranted('ROLE_ADMIN')) {
                    $context->addGroup('app');
                } else if ($this->getUser() && $this->isGranted(OrganismVoter::VIEW, $organism)) {
                    $context->addGroup('Default');
                } else if ($this->getUser() && $this->isGranted(OrganismVoter::ATTENDEE_VIEW, $organism)) {
                    $context->addGroup('attendee');
                } else {
                    $context->addGroup('public');
                }
                $view = $this->view($organism, 200);
                $view->setContext($context);
                return $this->handleView($view);
            }
        }
        if (count($violations)) {
            $view = $this->view($violations, Response::HTTP_BAD_REQUEST);
            $view->setContext($context);
            return $this->handleView($view);
        }
        return new Response(null, 404);
    }

    /**
     * @Rest\Get("/app/organisms/inPartnershipWith")
     * @IsGranted("ROLE_USER", message="not allowed")
     * @Rest\View(StatusCode = 200)
     * @Rest\QueryParam(name="limit", requirements="\d+", default="100")
     * @Rest\QueryParam(name="page", requirements="\d+", default="1")
     * @Rest\QueryParam(name="sort", requirements=@Assert\Choice({"name", "siret"}), default="name", description="Tri les résultats sur un critère.")
     * @Rest\QueryParam(name="order", requirements=@Assert\Choice({"asc", "desc"}), default="asc", description="Tri les résultats par ordre ascendant ou descendant - par défaut ascendant.")
     *
     * @ApiDoc\Areas({"organisms", "default"})
     * @OA\Get (
     *     summary="Récupération d'une liste d'organismes.",
     *     description="Récupération d'une liste d'organismes en partenariat avec l'organisme courant."
     * )
     *
     * @OA\Response(
     *     response=200,
     *     description="Un json contenant les informations de l'organisme",
     *     @OA\MediaType(mediaType="application/json",
     *         @OA\Schema(ref="#/components/schemas/Organism")
     *     )
     * )
     *
     * @param ParamFetcherInterface $paramFetcher
     * @param PaginatorInterface $paginator
     * @param OrganismService $organismService
     * @return Response
     */
    public function listInPartnershipWith(ParamFetcherInterface $paramFetcher, PaginatorInterface $paginator, OrganismService $organismService): Response
    {
        /** @var User $user */
        $user = $this->getUser();
        $parameters = $paramFetcher->all(true);
        $result = $organismService->listInPartnershipWith($user->getMainOrganism(), $parameters);
        $data = $paginator->paginate($result, intval($parameters['page']), intval($parameters['limit']));
        $view = $this->view($data->getItems(), 200);
        $view->setHeader("x-total-count", $data->getTotalItemCount());
        $view->setHeader("x-current-page", $data->getCurrentPageNumber());
        $view->setHeader("x-item-per-page", $data->getItemNumberPerPage());
        return $this->handleView($view);
    }

    /**
     * @Rest\Get("/app/organisms/potentialCertificationFolderHolders")
     * @IsGranted("ROLE_USER", message="not allowed")
     * @Rest\View(StatusCode = 200)
     * @Rest\QueryParam(name="query", requirements=@Assert\Type("string"), nullable=true, description="Permet d'effectuer une recherche libre sur les champs 'nom de l'organisme' et 'siret'.")
     * @Rest\QueryParam(name="limit", requirements="\d+", default="100")
     * @Rest\QueryParam(name="page", requirements="\d+", default="1")
     * @Rest\QueryParam(name="sort", requirements=@Assert\Choice({"name", "siret"}), default="name", description="Tri les résultats sur un critère.")
     * @Rest\QueryParam(name="order", requirements=@Assert\Choice({"asc", "desc"}), default="asc", description="Tri les résultats par ordre ascendant ou descendant - par défaut ascendant.")
     *
     * @param ParamFetcherInterface $paramFetcher
     * @param PaginatorInterface $paginator
     * @param OrganismService $organismService
     * @return Response
     */
    public function listPotentialCertificationFolderHolders(ParamFetcherInterface $paramFetcher, PaginatorInterface $paginator, OrganismService $organismService): Response
    {
        /** @var User $user */
        $user = $this->getUser();
        $parameters = $paramFetcher->all(true);
        $result = $organismService->listPotentialCertificationFolderHolders($user->getMainOrganism(), $parameters);
        $data = $paginator->paginate($result, intval($parameters['page']), intval($parameters['limit']));
        $view = $this->view($data->getItems(), 200);
        $view->setHeader("x-total-count", $data->getTotalItemCount());
        $view->setHeader("x-current-page", $data->getCurrentPageNumber());
        $view->setHeader("x-item-per-page", $data->getItemNumberPerPage());
        return $this->handleView($view);
    }

    /**
     * @Rest\Get("/api/organisms")
     * @Rest\QueryParam(name="organismType", requirements=@Assert\Type("string"), default="all", description="Permet de choisir le type d'organisme filtré par certification : 'certifier', 'partner', 'all'")
     * @Rest\QueryParam(name="externalId", requirements=@Assert\Type("string"), nullable=true, description="Permet de filtrer les organismes de type défini par le paramètre organismType liés à une certification identifiée par code RS ou RNCP. Exemple: 'RS5696'")
     * @Rest\QueryParam(name="certifInfo", requirements=@Assert\Type("string"), nullable=true, description="Permet de filtrer les organismes de type défini par le paramètre organismType liés à une certification identifiée par son certifInfo.")
     * @Rest\QueryParam(name="reseller", requirements=@Assert\Choice({"false", "true"}), nullable=true, description="Permet de filtrer les organismes lié à un siret d'un revendeur.")
     * @Rest\QueryParam(name="isTrainingOrganism", requirements=@Assert\Choice({"false", "true"}), nullable=true, description="Permet de filtrer les organismes de formation.")
     * @Rest\QueryParam(name="isCertifierOrganism", requirements=@Assert\Choice({"false", "true"}), nullable=true, description="Permet de filtrer les organismes de certification.")
     * @Rest\QueryParam(name="query", requirements=@Assert\Type("string"), nullable=true, description="Permet d'effectuer une recherche libre sur les champs 'nom de l'organisme' et 'siret'.")
     * @Rest\QueryParam(name="sort", requirements=@Assert\Choice({"name", "siret"}), default="name", description="Tri les résultats sur un critère.")
     * @Rest\QueryParam(name="order", requirements=@Assert\Choice({"asc", "desc"}), default="asc", description="Tri les résultats par ordre ascendant ou descendant - par défaut ascendant.")
     * @Rest\QueryParam(name="limit", requirements="\d+", default="100")
     * @Rest\QueryParam(name="page", requirements="\d+", default="1")
     * @Rest\View(StatusCode = 200)
     * @Security("is_granted('ROLE_USER') or is_granted('ROLE_OAUTH2_ORGANISM:READ')", message="not allowed")
     *
     * @ApiDoc\Areas({"organisms", "default"})
     * @OA\Get (
     *     summary="Liste les organismes selon des critères.",
     *     description="Récupère l'ensemble des organismes en fonction des critères. Via OAuth2, cet appel nécessite le scope 'organism:read'."
     * )
     * @OA\Response(
     *     response=200,
     *     description="Un tableau d'organismes au format JSON.",
     *     @OA\JsonContent(
     *          type="array",
     *          @OA\Items(ref="#/components/schemas/Organism")
     *     )
     * )
     *
     * @param ParamFetcherInterface $paramFetcher
     * @param CertificationService $certificationService
     * @param PaginatorInterface $paginator
     * @param OrganismService $organismService
     * @param AccessService $accessService
     * @return Response
     * @throws Throwable
     */
    public function list(ParamFetcherInterface $paramFetcher, CertificationService $certificationService, PaginatorInterface $paginator, OrganismService $organismService, AccessService $accessService): Response
    {
        /** @var User $user */
        $user = $this->getUser();
        $parameters = $paramFetcher->all(true);
        $context = new Context();

        $resellerParam = filter_var($parameters['reseller'], FILTER_VALIDATE_BOOLEAN);
        if ($resellerParam) {
            $currentOrganism = $user->getMainOrganism();
            $reseller = $organismService->getBySiret($currentOrganism->getSiret());
            if (!$reseller) {
                throw new WedofBadRequestHttpException("Erreur, revendeur inconnu.");
            }
            if ($currentOrganism !== $reseller) {
                throw new WedofBadRequestHttpException("Erreur, seul le revendeur peut accéder à ces organismes clients.");
            }
            if (!$currentOrganism->isReseller()) {
                throw new WedofBadRequestHttpException("Erreur, vous n'êtes pas un revendeur.");
            }
            unset($parameters['organismType']);
            unset($parameters['certifInfo']);
            unset($parameters['externalId']);
            $parameters['reseller'] = $reseller;
            $data = $paginator->paginate($organismService->listReturnQueryBuilder($parameters), intval($parameters['page']), intval($parameters['limit']));
        } else if (!empty($parameters['certifInfo']) || !empty($parameters['externalId'])) {
            if (!empty($parameters['externalId'])) {
                $certification = $certificationService->getByExternalId($parameters['externalId']);
            } else {
                $certification = $certificationService->getByCertifInfo($parameters['certifInfo']);
            }
            if (!empty($certification)) {
                $parameters['certification'] = $certification;
                if (!empty($parameters['organismType'])) {
                    $parameters['organismType'] = explode(",", $parameters['organismType']);
                    foreach ($parameters['organismType'] as $organismType) {
                        if (!in_array($organismType, OrganismTypes::valuesTypes())) {
                            throw new WedofBadRequestHttpException("Erreur sur les valeurs renvoyées 'organismType', elles doivent être : " . join(",", OrganismTypes::valuesTypes()) . ".");
                        }
                    }
                    foreach ($parameters['organismType'] as $organismType) {
                        switch ($organismType) {
                            case "certifier":
                                if (!$accessService->hasCertifiersView($user, $certification)) {
                                    $context->addGroup('public');
                                }
                                break;
                            case "partner":
                                if (!$accessService->hasPartnersView($user, $certification)) {
                                    $context->addGroup('public');
                                }
                                break;
                            case "all":
                                if (!$accessService->hasCertifiersView($user, $certification) || !$accessService->hasPartnersView($user, $certification)) {
                                    $context->addGroup('public');
                                }
                                break;
                        }
                    }
                }
                $data = $paginator->paginate($organismService->listReturnQueryBuilder($parameters), intval($parameters['page']), intval($parameters['limit']));
            } else {
                throw new WedofNotFoundHttpException("La certification associée au certifInfo " . $parameters['certifInfo'] . " n'a pas été trouvée.");
            }
        } else {
            if (isset($parameters['query'])) {
                $parameters['organismType'] = ['all'];
                $result = $organismService->listReturnQueryBuilder($parameters);
                $data = $paginator->paginate($result, intval($parameters['page']), intval($parameters['limit']));
                $context->addGroup('public');
                if (strlen($parameters['query']) === 14 && $data->count() === 0 && intval($parameters['page']) === 1) { //todo validate siret ??
                    $result = $organismService->getOrganism(['siret' => $parameters['query']]);
                    $data = $result ? [$result] : [];
                    $view = $this->view($data, 200);
                    $view->setHeader("x-total-count", count($data));
                    $view->setHeader("x-current-page", 1);
                    $view->setHeader("x-item-per-page", 20);
                    return $this->handleView($view);
                }
            } else {
                $data = $paginator->paginate($user->getOrganisms(), intval($parameters['page']), intval($parameters['limit']));
            }
        }

        $view = $this->view($data->getItems(), 200);
        $view->setContext($context);
        $view->setHeader("x-total-count", $data->getTotalItemCount());
        $view->setHeader("x-current-page", $data->getCurrentPageNumber());
        $view->setHeader("x-item-per-page", $data->getItemNumberPerPage());
        return $this->handleView($view);
    }

    /**
     * @Rest\Post ("/api/organisms", name="organism_create")
     * @Security("is_granted('ROLE_ADMIN')", message="not allowed")
     * @Rest\View(StatusCode = 201)
     *
     * @param Request $request
     * @param OrganismService $organismService
     * @return Organism
     * @throws Throwable
     */
    public function create(Request $request, OrganismService $organismService): Organism
    {
        $body = json_decode($request->getContent(), true);
        if (!empty($body['siret']) && is_numeric($body['siret']) && strlen($body['siret']) === 14) {
            return $organismService->getOrganism(['siret' => $body['siret']]);
        } else {
            throw new WedofBadRequestHttpException('Erreur, un siret valide est obligatoire.');
        }
    }

    /**
     * @Rest\Route("/app/organisms/{siret}", name="app_organism_update", methods={"POST", "PUT"})
     * @Rest\Put("/api/organisms/{siret}", name="organism_update")
     * @IsGranted(OrganismVoter::EDIT, subject="organism",  message="not allowed")
     * @Rest\View(StatusCode = 200, serializerGroups={"app"})
     *
     * @ApiDoc\Areas({"organisms", "default"})
     * @OA\Put (
     *     summary="Met à jour un organisme.",
     *     description="Permet de mettre à jour un organisme. Via OAuth2, cet appel nécessite le scope 'organism:write'."
     * )
     * @OA\Response(
     *     response=200,
     *     description="Un json contenant les informations de l'organisme",
     *     @OA\MediaType(mediaType="application/json",
     *         @OA\Schema(ref="#/components/schemas/Organism")
     *     )
     * )
     * @OA\Parameter(
     *     name="siret",
     *     in="path",
     *     description="Siret de l'organisme",
     *     @OA\Schema(type="string")
     * )
     * @OA\RequestBody(
     *     @OA\MediaType(mediaType="application/json",
     *         @OA\Schema(ref="#/components/schemas/OrganismUpdateBody")
     *     )
     * )
     *
     *
     * @param Organism $organism
     * @param OrganismService $organismService
     * @return Organism|View|null
     * @throws Throwable
     * @throws ClientExceptionInterface
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws TransportExceptionInterface
     */
    public function update(Organism $organism, OrganismService $organismService)
    {
        $body = $this->getData();
        if (isset($body['vat'])) {
            $body['vat'] = floatval($body['vat']);
            if (!in_array($body['vat'], [0, 5.5, 20])) { // Assert choice not working with 0 or 20 with [0, 5.5, 20]
                throw new WedofBadRequestHttpException("Erreur, sur la valeur retournée pour la TVA. Les valeurs possibles sont : 0, 5.5 ou 20.");
            }
        }

        if ((isset($body['cdcClientId']) || isset($body['cdcContractId']) || isset($body['accrochageDelegationDate'])) && !$organism->isCertifierOrganism()) {
            throw new WedofBadRequestHttpException("Erreur, les champs 'cdcClientId', 'cdcContractId', 'accrochageDelegationDate' ne sont destinés qu'aux organismes certificateurs.");
        }

        if ((isset($body['cdcClientId']) || isset($body['cdcContractId'])) && $organism->getAccrochageDelegationDate()) {
            throw new WedofBadRequestHttpException("Erreur, vous ne pouvez pas modifier les champs 'cdcClientId', 'cdcContractId' dans le cadre d'un accrochage automatique.");
        }

        if (!empty($body['accrochageDelegationDate']) && strtotime($body['accrochageDelegationDate'])) {
            $body['accrochageDelegationDate'] = (new DateTime($body['accrochageDelegationDate']))->setTimezone(new DateTimeZone("GMT"));
        }

        if (!empty($body['driveId']) && !$this->isGranted('ROLE_ADMIN')) {
            throw new WedofAccessDeniedHttpException("Erreur, le champ 'driveId' ne peut être renseigné que par l'administrateur.");
        }

        if (array_key_exists('metadata', $body)) {
            if (is_string($body['metadata'])) {
                $body['metadata'] = json_decode($body['metadata'], true);
                if (json_last_error() !== JSON_ERROR_NONE) {
                    throw new WedofBadRequestHttpException("Erreur, le champ metadata doit être un JSON valide");
                }
            }
            if (!is_array($body['metadata']) && $body['metadata'] !== null) {
                throw new WedofBadRequestHttpException("Erreur, le champ metadata doit être un JSON valide");
            }
        }

        if (!empty($body['uaiNumber']) && !$organism->isTrainingOrganism()) {
            throw new WedofAccessDeniedHttpException("Erreur, seul un organism de formation peut modifier son numéro UAI.");
        }

        $violations = $this->validateUpdateBody($body);

        if (count($violations)) {
            return $this->view($violations, Response::HTTP_BAD_REQUEST);
        }
        return $organismService->createOrUpdate($body, $organism, true);
    }

    /**
     * @Rest\Delete("/api/organisms/{siret}")
     * @Security("is_granted('ROLE_OAUTH2_ORGANISM:WRITE') or is_granted('ROLE_USER')", message="not allowed")
     * @Rest\View(StatusCode = 501)
     *
     * @param Organism $organism
     * @return WedofBadRequestHttpException
     */
    public function delete(Organism $organism): WedofBadRequestHttpException
    {
        throw new WedofBadRequestHttpException("Méthode non supportée - L'organisme associé au siret " . $organism->getSiret() . " ne peut être supprimé.");
    }

    /**
     * @Rest\Post("/api/organisms/{siret}/refreshCatalog", name="app_organism_catalog_refresh")
     * @Security("is_granted('ROLE_ADMIN') or is_granted('ROLE_ALLOWED_TO_SWITCH') or is_granted('IS_IMPERSONATOR')", message="not allowed")
     * @Rest\View(StatusCode = 200)
     *
     * @param Organism $organism
     * @param OrganismService $organismService
     * @throws Exception
     */
    public function refreshCatalog(Organism $organism, OrganismService $organismService)
    {
        $organismService->refreshCatalog($organism, DataProviders::CPF());
    }

    /**
     * This synchronous method is here for dev tests only, it is not publicly documented
     * @Rest\Post("/api/organisms/{siret}/generateCpfCatalogXmlForDev")
     * @Security("is_granted('ROLE_ADMIN') or is_granted('ROLE_ALLOWED_TO_SWITCH') or is_granted('IS_IMPERSONATOR')", message="not allowed")
     * @Rest\QueryParam(name="noValidation", requirements=@Assert\Choice({"true", "false"}), nullable=true)
     * @param ParamFetcherInterface $paramFetcher
     * @param Organism $organism
     * @param CatalogXMLService $catalogXMLService
     * @return void
     * @throws Exception
     */
    public function generateCpfCatalogXmlForDev(ParamFetcherInterface $paramFetcher, Organism $organism, CatalogXMLService $catalogXMLService)
    {
        ini_set('memory_limit', '4096M');
        ini_set('max_execution_time', 1800); // 30 min timeout for a start
        $parameters = $paramFetcher->all(true);
        $xmlString = $catalogXMLService->generateCpfCatalogXML($organism)['xml'];
        if (!isset($parameters['noValidation']) || !filter_var($parameters['noValidation'], FILTER_VALIDATE_BOOLEAN)) {
            try {
                SimpleXMLElementTools::validateCdcXML($xmlString, '/../../../data/lheo_v5r2.xsd');
            } catch (Throwable $e) {
                if (!empty($e->getMessage())) {
                    throw new WedofBadRequestHttpException("Erreur de validation XSD : " . $e->getMessage());
                } else {
                    throw $e;
                }
            }
        }
        Header('Content-type: text/xml; charset=ISO-8859-1');
        echo $xmlString;
        die();
    }

    /**
     * @Rest\Post("/api/organisms/{siret}/exportCpfCatalogXml")
     * @Rest\View(StatusCode = 200, serializerGroups={"app"})
     * @IsGranted(OrganismVoter::EDIT, subject="organism")
     *
     * @ApiDoc\Areas({"organisms", "default"})
     * @OA\Post(
     *      summary="Export votre catalogue EDOF au format XML Lhéo",
     *      description="Export votre catalogue EDOF publié actuel au format XML Lhéo. Cette opération peut être longue. Vous recevrez le fichier et un rapport par email et webhook."
     * )
     * @OA\Parameter(
     *     name="siret",
     *     in="path",
     *     description="Siret de l'organisme",
     *     @OA\Schema(type="string")
     * )
     * @OA\Response(
     *     response=200,
     *     description="Un json contenant les informations de l'organisme",
     *     @OA\MediaType(mediaType="application/json",
     *         @OA\Schema(ref="#/components/schemas/Organism")
     *     )
     * )
     * @param Organism $organism
     * @param OrganismService $organismService
     * @return Organism
     */
    public function exportCpfCatalogXml(Organism $organism, OrganismService $organismService): Organism
    {
        /** @var User $user */
        $user = $this->getUser();
        if (!in_array($organism->getSubscription()->getTrainingType(), SubscriptionTrainingTypes::getPaidTrainingTypes(false))) {
            throw new WedofBadRequestHttpException("Erreur : votre niveau d'abonnement ne permet pas d'exporter le catalogue EDOF en XML.");
        }
        $cpfCatalogMetadata = $organism->getCpfCatalogMetadata();
        // $now = new DateTime('now');
        // if (isset($cpfCatalogMetadata['export']['startDate']) && (new DateTime($cpfCatalogMetadata['export']['startDate']))->modify('+1 day') > $now) {
        //    throw new WedofBadRequestHttpException("Erreur : il n'est possible de demander l'export du catalogue qu'une fois toutes les 24 heures.");
        // }
        if (isset($cpfCatalogMetadata['export']['state']) && in_array($cpfCatalogMetadata['export']['state'], ['pendingSynchronize', 'inProgress'])) {
            throw new WedofBadRequestHttpException("Erreur : un export est déjà en cours");
        }
        $cpfCatalogMetadata['export'] = [
            'startDate' => (new DateTime('now'))->format('Y-m-d\TH:i:s.u\Z'),
            'state' => 'pendingSynchronize'
        ];
        $organism->setCpfCatalogMetadata($cpfCatalogMetadata);
        $organismService->save($organism);
        $organismService->refreshCatalog($user->getMainOrganism(), DataProviders::CPF()); // When refresh ends, it will check whether there is an export pending
        return $organism;
    }

    /**
     * @Rest\Post("/api/organisms/{siret}/importCpfCatalogXml")
     * @Rest\View(StatusCode = 200, serializerGroups={"app"})
     * @IsGranted(OrganismVoter::EDIT, subject="organism")
     *
     * @ApiDoc\Areas({"organisms", "default"})
     * @OA\Post(
     *     summary="Importer votre catalogue EDOF au format XML Lhéo",
     *     description="Fournissez un fichier XML au format Lhéo EDOF encodé en ISO-8859-1. Le nom du fichier doit être au format suivant <SIRET>_<nomfichier>.xml et <nomfichier> ne doit pas contenir de point. Votre fichier ne doit pas dépasser 100 Mo. Vous recevrez un rapport par email et webhook."
     * )
     * @OA\Parameter(
     *     name="siret",
     *     in="path",
     *     description="Siret de l'organisme",
     *     @OA\Schema(type="string")
     * )
     * @OA\RequestBody(
     *       @OA\MediaType(mediaType="multipart/form-data",
     *           @OA\Schema(ref="#/components/schemas/UploadCatalogFile")
     *       )
     *   )
     * @OA\Response(
     *     response=200,
     *     description="Un json contenant les informations de l'organisme",
     *     @OA\MediaType(mediaType="application/json",
     *         @OA\Schema(ref="#/components/schemas/Organism")
     *     )
     * )
     * @param Request $request
     * @param Organism $organism
     * @param CpfApiService $cpfApiService
     * @return Organism|Response
     * @throws Throwable
     */
    public function importCpfCatalogXml(Request $request, Organism $organism, CpfApiService $cpfApiService)
    {
        $simulate = filter_var($request->get('simulate', false), FILTER_VALIDATE_BOOLEAN);

        $additionalReportRecipients = $request->get("additionalReportRecipients");
        $additionalReportRecipients = $additionalReportRecipients ? explode(',', trim($additionalReportRecipients)) : [];
        $validator = Validation::createValidator();
        foreach ($additionalReportRecipients as $additionalReportRecipient) {
            $violations = $validator->validate($additionalReportRecipient, new Assert\Email());
            if (count($violations)) {
                $view = $this->view($violations, Response::HTTP_BAD_REQUEST);
                $view->setContext(new Context());
                return $this->handleView($view);
            }
        }

        if (!in_array($organism->getSubscription()->getTrainingType(), SubscriptionTrainingTypes::getPaidTrainingTypes(false))) {
            throw new WedofBadRequestHttpException("Erreur : votre niveau d'abonnement ne permet pas d'importer le catalogue EDOF en XML.");
        }
        /** @var User $user */
        $user = $this->getUser();
        if (!$user->isOwner()) {
            throw new WedofAccessDeniedHttpException("Erreur, seul le propriétaire de l'organisme peut mettre à jour le catalogue EDOF.");
        }
        /** @var UploadedFile $file */
        $file = !empty($request->files->all()) ? $request->files->get('file') : $request->get('file');
        $content = $file->getContent();
        if (!$content) {
            throw new WedofBadRequestHttpException("Erreur, le fichier n'a pas été transmis correctement.");
        }
        if ($file->getClientOriginalExtension() !== 'xml') {
            throw new WedofBadRequestHttpException('Erreur, le fichier doit être au format XML');
        }
        if (!Tools::startsWith($file->getClientOriginalName(), $organism->getSiret() . '_')) {
            throw new WedofBadRequestHttpException("Erreur, le nom du fichier doit commencer par le SIRET de votre organisme suivi d'un tiret du bas (underscore) : <SIRET>_nomFichier.xml");
        }
        if (Tools::contains(pathinfo($file->getClientOriginalName(), PATHINFO_FILENAME), '.')) {
            throw new WedofBadRequestHttpException('Erreur, le nom du fichier ne doit pas contenir de point');
        }
        if (!$file->getSize() || $file->getSize() > 100 * 1024 * 1024) { // 100 Mo
            throw new WedofBadRequestHttpException('Erreur, le fichier ne doit pas dépasser 100Mo');
        }
        if (!mb_check_encoding($content, 'ISO-8859-1')) {
            throw new WedofBadRequestHttpException('Erreur, le fichier doit être encodé en ISO-8859-1');
        }
        try {
            SimpleXMLElementTools::validateCdcXML($content, '/../../../data/lheo_v5r2.xsd');
        } catch (Exception $e) {
            if (!empty($e->getMessage())) {
                throw new WedofBadRequestHttpException("Erreur, ceci n'est pas un fichier LHEO EDOF valide d'après la validation XSD : " . $e->getMessage());
            } else {
                throw new WedofBadRequestHttpException("Erreur, ceci n'est pas un fichier LHEO EDOF valide d'après la validation XSD");
            }
        }
        if ($cpfApiService->isCatalogUploadInProgress($organism)) {
            throw new WedofBadRequestHttpException("Erreur, il y a déjà un fichier LHEO en cours d'import sur votre organisme");
        }
        if ($simulate) {
            return $organism;
        }
        return $cpfApiService->uploadCatalogXml($organism, $file, $additionalReportRecipients);
    }

    /**
     * @Rest\GET("/api/organisms/{siret}/cpfCatalogUploadReport")
     * @Rest\View(StatusCode = 200)
     * @IsGranted(OrganismVoter::EDIT, subject="organism")
     *
     * @param Organism $organism
     * @param DownloadHandler $downloadHandler
     * @return StreamedResponse
     */
    public function downloadCpfCatalogUploadReport(Organism $organism, DownloadHandler $downloadHandler): StreamedResponse
    {
        if ($organism->getCpfCatalogUploadReportName()) {
            $fileName = $organism->getCpfCatalogMetadata()['upload']['reportFileName'] ?? 'report.csv';
            return $downloadHandler->downloadObject($organism, 'cpfCatalogUploadReport', null, $fileName);
        } else {
            throw new WedofNotFoundHttpException("Aucun rapport n'a été trouvé");
        }
    }

    /**
     * @Rest\GET("/api/organisms/{siret}/cpfCatalogUploadStatus")
     * @Rest\View(StatusCode = 200)
     * @IsGranted(OrganismVoter::EDIT, subject="organism")
     *
     * @ApiDoc\Areas({"organisms", "default"})
     * @OA\Get (
     *     summary="Récupération du statut de l'import du catalogue EDOF",
     *     description="Récupère le statut et le rapport d'import XML de votre catalogue EDOF"
     * )
     * @OA\Parameter(
     *     name="siret",
     *     in="path",
     *     description="Siret de l'organisme",
     *     @OA\Schema(type="string")
     * )
     * @OA\Response(
     *     response=200,
     *     description="Statut de l'import votre catalogue EDOF",
     *     @OA\JsonContent(
     *         type="object",
     *         @OA\Property(property="state", type="string", enum={"inProgress", "done"}),
     *         @OA\Property(property="fileName", type="string"),
     *         @OA\Property(property="startDate", type="string", format="date-time"),
     *         @OA\Property(property="endDate", type="string", format="date-time", nullable=true),
     *         @OA\Property(property="reportFileName", type="string", nullable=true),
     *         @OA\Property(
     *             property="report",
     *             type="array",
     *             nullable=true,
     *             @OA\Items(
     *                 type="object",
     *                 @OA\Property(property="OBJET", type="string"),
     *                 @OA\Property(property="NUMERO_FORMATION", type="string"),
     *                 @OA\Property(property="NUMERO_ACTION", type="string"),
     *                 @OA\Property(property="NUMERO_SESSION", type="string"),
     *                 @OA\Property(property="NUMERO_COORDONNEES", type="string"),
     *                 @OA\Property(property="NUMERO_ADRESSE", type="string"),
     *                 @OA\Property(property="STATUT", type="string"),
     *                 @OA\Property(property="CODE_ANO", type="string"),
     *                 @OA\Property(property="LIBELLE_ANO", type="string")
     *             )
     *         )
     *     )
     * )
     * @param Organism $organism
     * @param OrganismService $organismService
     * @return array
     */
    public function cpfCatalogUploadStatus(Organism $organism, OrganismService $organismService): array
    {
        return $organismService->getCpfCatalogUploadStatus($organism); // TODO manage empty
    }

    //----------------
    // METHODES PRIVES
    //----------------
    private function validateUpdateBody(array $body): ConstraintViolationListInterface
    {
        $validator = Validation::createValidator();
        $constraints = new Assert\Collection([
            'allowExtraFields' => true,
            'fields' => [
                'emails' => new Assert\Optional(new Assert\All([new Assert\NotBlank(), new Assert\Email()])),
                'phones' => new Assert\Optional(new Assert\All([new Assert\NotBlank(), new Assert\Regex(['pattern' => '/^(?:(?:\+|00)33|0)\s*[1-9](?:[\s.-]*\d{2}){4}$/'])])),
                'urls' => new Assert\Optional([new Assert\Type('array'), new Assert\All([new Assert\Url()])]),
                'address' => new Assert\Optional([new Assert\NotBlank(), new Assert\Type('string'), new Assert\Length(['max' => 1000])]),
                'postalCode' => new Assert\Optional([new Assert\NotBlank(), new Assert\Length(5), new Assert\Regex(['pattern' => '/^[0-9]*$/'])]),
                'city' => new Assert\Optional([new Assert\NotBlank(), new Assert\Type('string'), new Assert\Length(['max' => 255])]),
                'agreement' => new Assert\Optional([new Assert\NotBlank(), new Assert\Regex(['pattern' => '/^[0-9A-Za-z]*$/']), new Assert\Length(['min' => 10, 'max' => 11])]),
                'vat' => new Assert\Optional([new Assert\NotBlank(), new Assert\Type('float')]),
                'cdcClientId' => new Assert\Optional([new Assert\Type('string'), new Assert\Length(8)]),
                'cdcContractId' => new Assert\Optional([new Assert\Type('string'), new Assert\Length(['max' => 20])]),
                'accrochageDelegationDate' => new Assert\Optional(new Assert\Type('datetime')),
                'driveId' => new Assert\Optional([new Assert\NotBlank(), new Assert\Type('string'), new Assert\Length(['max' => 255])]),
                'linkedInPageUrl' => new Assert\Optional([new Assert\Url(), new Assert\Length(['max' => 255])]),
                'customColorScheme' => new Assert\Optional([new Assert\Regex(['pattern' => '/^#[0-9A-Fa-f]*$/']), new Assert\Length(7)]),
                'metadata' => new Assert\Optional(new Assert\Type('array')),
                'uaiNumber' => new Assert\Optional(new Assert\Type('string')),
                'billingSoftware' => new Assert\Optional(new Assert\Type('string')),
                'crm' => new Assert\Optional(new Assert\Type('string'))
            ]
        ]);
        return $validator->validate($body, $constraints);
    }

    /**
     * @throws Exception
     */
    private function prepareJwtData(array $data): array
    {
        $data["organism"] = (array)$data["organism"];
        $data["user"] = (array)$data["user"];
        return $data;
    }

    /**
     * @param DataProviders $connectionType
     * @param $organism
     * @return array
     */
    private function getConnectionStatusForCustomerReseller(DataProviders $connectionType, $organism = null): array
    {
        $connection = $organism ? ($organism->getConnectionForDataProvider($connectionType) ?? null) : null;
        $state = $connection ? ($connection->getExistAtDataProvider() ? $connection->getState() : 'accountNotExist') : 'inactive';
        $state = $state === ConnectionStates::REFRESHING()->getValue() ? ConnectionStates::ACTIVE()->getValue() : $state;
        switch ($state) {
            case 'accountNotExist':
                $text = "Aucun compte EDOF";
                break;
            case ConnectionStates::IN_PROGRESS()->getValue():
                $text = "En cours d'activation";
                break;
            case ConnectionStates::ACTIVE()->getValue():
            case ConnectionStates::REFRESHING()->getValue():
                $text = "Synchronisation active";
                break;
            case ConnectionStates::FAILED()->getValue():
                $text = "Échec de synchronisation";
                break;
            case ConnectionStates::REVOKED()->getValue():
                $text = "Habilitation révoquée";
                break;
            default:
            case ConnectionStates::INACTIVE()->getValue():
                $text = "Créer une habilitation";
                break;
        }
        return ["text" => $text, "state" => $state];
    }

    /**
     * @param $organism
     * @param $data
     * @param $subscriptionService
     * @param $userService
     * @return Organism
     */
    private function checkInitialization($organism, $data, $subscriptionService, $userService): Organism
    {
        //check for user
        if (!$organism->getOwnedBy()) {
            $user = new User();
            $user->setFirstName($data['user']['firstName']);
            $user->setLastName($data['user']['lastName']);
            $user->setEmail($data['user']['email']);
            $user = $userService->create($user, false);
            //set organism
            $user = $userService->update($user,
                ['mainOrganism' =>
                    ['siret' => $organism->getSiret()]
                ]
            );
        }
        //check for subscription
        if (!$organism->getSubscription()) {
            //organism need to valid the potential subscription manually
            $subscriptionService->create($organism, [
                "training" => [
                    "type" => SubscriptionTrainingTypes::NONE()
                ],
                "certifier" => [
                    "type" => SubscriptionTrainingTypes::NONE()
                ]
            ]);
        }
        return $organism;
    }
}
