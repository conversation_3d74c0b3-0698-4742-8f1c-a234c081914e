<?php
// src/Controller/api/CertificationFolderController.php

namespace App\Controller\api;

use App\Entity\ApiToken;
use App\Entity\Attendee;
use App\Entity\Certification;
use App\Entity\CertificationFolder;
use App\Entity\CertificationFolderFile;
use App\Entity\CertificationPartner;
use App\Entity\Organism;
use App\Entity\User;
use App\Exception\WedofBadRequestHttpException;
use App\Exception\WedofConnectionException;
use App\Exception\WedofNotFoundHttpException;
use App\Exception\WedofSubscriptionException;
use App\Library\utils\enums\ActivityTypes;
use App\Library\utils\enums\AttendeeGender;
use App\Library\utils\enums\AuthMethodAttendee;
use App\Library\utils\enums\CertificationExaminationType;
use App\Library\utils\enums\CertificationFolderAccessModality;
use App\Library\utils\enums\CertificationFolderAccessModalityVae;
use App\Library\utils\enums\CertificationFolderCdcStates;
use App\Library\utils\enums\CertificationFolderEuropeanLanguageLevel;
use App\Library\utils\enums\CertificationFolderGradePass;
use App\Library\utils\enums\CertificationFolderPassportStates;
use App\Library\utils\enums\CertificationFolderStates;
use App\Library\utils\enums\CertificationFolderType;
use App\Library\utils\enums\CertificationPartnerStates;
use App\Library\utils\enums\CertificationSkillType;
use App\Library\utils\enums\CertificationTypes;
use App\Library\utils\enums\ConnectionStates;
use App\Library\utils\enums\DataProviders;
use App\Library\utils\enums\FileStates;
use App\Library\utils\enums\MessageStates;
use App\Library\utils\enums\PassportType;
use App\Library\utils\enums\PeriodTypes;
use App\Library\utils\enums\ProcessingMethod;
use App\Library\utils\enums\RegistrationFolderStates;
use App\Library\utils\enums\SubscriptionCertifierTypes;
use App\Library\utils\Tools;
use App\Repository\CertificationFolderFileRepository;
use App\Repository\CertificationFolderRepository;
use App\Security\Voter\CertificationFolderVoter;
use App\Security\Voter\CertificationVoter;
use App\Service\AccessService;
use App\Service\ActivityService;
use App\Service\AttendeeService;
use App\Service\CertificationFolderFileService;
use App\Service\CertificationFolderService;
use App\Service\CertificationPartnerService;
use App\Service\CertificationService;
use App\Service\CertifierAccessService;
use App\Service\CityService;
use App\Service\OrganismService;
use App\Service\RegistrationFolderService;
use App\Service\SkillService;
use DateTime;
use DateTimeZone;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\NonUniqueResultException;
use Doctrine\ORM\NoResultException;
use Doctrine\ORM\OptimisticLockException;
use ErrorException;
use Exception;
use Firebase\JWT\JWT;
use FOS\RestBundle\Context\Context;
use FOS\RestBundle\Controller\Annotations as Rest;
use FOS\RestBundle\Request\ParamFetcherInterface;
use FOS\RestBundle\View\View;
use Knp\Component\Pager\PaginatorInterface;
use LogicException;
use Nelmio\ApiDocBundle\Annotation as ApiDoc;
use OpenApi\Annotations as OA;
use PhpOffice\PhpSpreadsheet\Reader\Xls;
use PhpOffice\PhpSpreadsheet\Reader\Xlsx as XlsxReader;
use PhpOffice\PhpSpreadsheet\RichText\RichText;
use PhpOffice\PhpSpreadsheet\Style\NumberFormat;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use PhpOffice\PhpSpreadsheet\Writer\Csv as CsvWriter;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx as XlsxWriter;
use Psr\Container\ContainerExceptionInterface;
use Psr\Container\NotFoundExceptionInterface;
use Ramsey\Uuid\Exception\DateTimeException;
use ReflectionException;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\IsGranted;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Security;
use Symfony\Component\HttpFoundation\File\UploadedFile;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\StreamedResponse;
use Symfony\Component\Mailer\Exception\TransportExceptionInterface;
use Symfony\Component\Validator\Constraints as Assert;
use Symfony\Component\Validator\ConstraintViolation;
use Symfony\Component\Validator\ConstraintViolationList;
use Symfony\Component\Validator\ConstraintViolationListInterface;
use Symfony\Component\Validator\Validation;
use Symfony\Contracts\HttpClient\Exception\ClientExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\RedirectionExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\ServerExceptionInterface;
use Throwable;
use function fopen;

/**
 * Class CertificationFolderController
 * @package App\Controller\api
 *
 * @OA\Tag(name="Certification Folder")
 * @ApiDoc\Security(name="accessCode")
 */
class CertificationFolderController extends AbstractWedofController
{
    //--------------
    // APP ENDPOINTS
    //--------------

    /**
     * @Rest\Get("/app/public/certificationFolders/{externalId}/authMethodForAttendee")
     * @Rest\View(StatusCode = 200)
     *
     * @param CertificationFolder $certificationFolder
     * @param CertificationFolderService $certificationFolderService
     * @return array
     * @throws NoResultException
     * @throws NonUniqueResultException
     */
    public function authForAttendee(CertificationFolder $certificationFolder, CertificationFolderService $certificationFolderService): array
    {
        $attendee = $certificationFolder->getAttendee();
        $alreadyLoggedWithId360 = $attendee->getExternalId();
        $registrationFolder = $certificationFolder->getRegistrationFolder();
        $isCPF = $registrationFolder && $registrationFolder->getType() === DataProviders::CPF()->getValue();
        $cdcExcluded = $certificationFolder->isCdcExcluded();
        $hasProcessedOk = $certificationFolderService->hasNoCpfProcessedOkForAttendee($attendee);
        if ($alreadyLoggedWithId360 || $isCPF || $hasProcessedOk || $cdcExcluded) {
            $method = AuthMethodAttendee::MAGIC_LINK()->getValue();
        } else {
            $method = AuthMethodAttendee::IDENTIFICATION_360()->getValue();
        }
        return ['method' => $method];
    }


    /**
     * @Rest\Get("/app/public/certificationFolders/{externalId}/sendMagicLinkForAttendee/{redirectURL}", requirements={"redirectURL"=".+"})
     * @Rest\View(StatusCode = 200)
     *
     * @param CertificationFolder $certificationFolder
     * @param AttendeeService $attendeeService
     * @param string $redirectURL
     * @throws ReflectionException
     * @throws TransportExceptionInterface
     */
    public function sendMagicLinkForAttendee(CertificationFolder $certificationFolder, AttendeeService $attendeeService, string $redirectURL): void
    {
        $attendeeService->sendMagicLink($certificationFolder, CertificationFolder::CLASSNAME, $redirectURL);
    }

    /**
     * @Rest\Get("/app/certificationFolders/{externalId}/magicLink")
     * @Rest\View(StatusCode = 200)
     *
     * @param CertificationFolder $certificationFolder
     * @param AttendeeService $attendeeService
     * @return Response
     * @throws ReflectionException
     */
    public function getMagicLink(CertificationFolder $certificationFolder, AttendeeService $attendeeService): Response
    {
        if ($this->getUser() && $this->isGranted("IS_IMPERSONATOR")) {
            $redirectURL = '/candidat/certification/dossier/' . $certificationFolder->getExternalId();
            return new Response(json_encode(['url' => ($attendeeService->generateMagicLink($certificationFolder->getAttendee()) . '&impersonate=true&redirectURL=' . $redirectURL)]));
        } else {
            return new Response(null, 403);
        }
    }

    /**
     * @Rest\Get("/app/certificationFolders/revenue/{columnId}")
     * @Security("is_granted('ROLE_USER') or is_granted('ROLE_OAUTH2_CERTIFICATIONFOLDER:READ')", message="not allowed")
     * @Rest\QueryParam(name="siret", requirements=@Assert\Type("string"), nullable=true)
     * @Rest\QueryParam(name="registrationFolderState", requirements=@Assert\Type("string"), nullable=true)
     * @Rest\QueryParam(name="registrationFolderType", requirements=@Assert\Type("string"), nullable=true)
     * @Rest\QueryParam(name="registrationFolderCompletionRate", requirements=@Assert\Choice(">80", "<80"), nullable=true)
     * @Rest\QueryParam(name="certifInfo", requirements=@Assert\Type("string"), nullable=true)
     * @Rest\QueryParam(name="skillSets", requirements=@Assert\Type("string"), nullable=true)
     * @Rest\QueryParam(name="state", requirements=@Assert\Type("string"), default="all")
     * @Rest\QueryParam(name="cdcState", requirements=@Assert\Type("string"), default="all")
     * @Rest\QueryParam(name="cdcCompliant", requirements=@Assert\Choice({"false", "true"}), nullable=true)
     * @Rest\QueryParam(name="cdcToExport", requirements=@Assert\Choice({"false", "true"}), nullable=true)
     * @Rest\QueryParam(name="cdcExcluded", requirements=@Assert\Choice({"false", "true"}), nullable=true)
     * @Rest\QueryParam(name="cdcFile", requirements=@Assert\Type("string"), nullable=true)
     * @Rest\QueryParam(name="messageState", requirements=@Assert\Type("string"), nullable=true)
     * @Rest\QueryParam(name="messageTemplate", requirements=@Assert\Type("string"), nullable=true)
     * @Rest\QueryParam(name="survey", requirements=@Assert\Type("string"), nullable=true)
     * @Rest\QueryParam(name="tags", requirements=@Assert\Type("string"), nullable=true)
     * @Rest\QueryParam(name="metadata", requirements=@Assert\Type("string"), nullable=true, description="Permet d'effectuer une une valeur d'un 'metadata' format : cle:valeur.")
     * @Rest\QueryParam(name="query", requirements=@Assert\Type("string"), nullable=true)
     * @Rest\QueryParam(name="period", requirements=@Assert\Choice({"nextYear", "previousYear", "currentYear", "rollingYear", "rollingYearFuture", "nextMonth", "previousMonth", "currentMonth", "rollingMonth", "rollingMonthFuture", "nextWeek", "previousWeek", "currentWeek", "rollingWeek", "rollingWeekFuture", "tomorrow", "today", "yesterday", "wedofInvoice", "custom"}), nullable=true, description="Filtre les dossiers de certification selon la période choisie. Valeurs possibles : 'nextYear', 'previousYear', 'currentYear', 'rollingYear', 'rollingYearFuture', 'nextMonth', 'previousMonth', 'currentMonth', 'rollingMonth', 'rollingMonthFuture', 'nextWeek', 'previousWeek', 'currentWeek', 'rollingWeek', 'rollingWeekFuture', 'tomorrow', 'today', 'yesterday', 'custom'.")
     * @Rest\QueryParam(name="since", requirements=@Assert\AtLeastOneOf({@Assert\DateTime(format="Y-m-d\TH:i:sO"), @Assert\Date()}), nullable=true )
     * @Rest\QueryParam(name="until", requirements=@Assert\AtLeastOneOf({@Assert\DateTime(format="Y-m-d\TH:i:sO"), @Assert\Date()}), nullable=true)
     * @Rest\QueryParam(name="filterOnStateDate", requirements=@Assert\Choice({"stateLastUpdate", "updatedOn", "toTakeDate", "toRetakeDate", "toControlDate", "failedDate", "successDate", "toRegisterDate", "registeredDate", "refusedDate", "abortedDate", "notProcessedRegistrationFolderStateDate", "validatedRegistrationFolderStateDate", "acceptedRegistrationFolderStateDate", "inTrainingRegistrationFolderStateDate", "terminatedRegistrationFolderStateDate", "serviceDoneDeclaredRegistrationFolderStateDate", "serviceDoneValidatedRegistrationFolderStateDate", "billedRegistrationFolderStateDate", "refusedByAttendeeRegistrationFolderStateDate", "refusedByOrganismRegistrationFolderStateDate", "canceledByAttendeeRegistrationFolderStateDate", "canceledByOrganismRegistrationFolderStateDate", "canceledByAttendeeNotRealizedRegistrationFolderStateDate", "rejectedWithoutTitulaireSuiteRegistrationFolderStateDate", "sessionStartDateRegistrationFolderDate", "sessionEndDateRegistrationFolderDate", "wedofInvoice", "examinationDate", "examinationEndDate", "enrollmentDate"}), default="stateLastUpdate", nullable=true)
     * @Rest\QueryParam(name="order", requirements=@Assert\Choice({"asc", "desc"}), default="desc")
     * @Rest\QueryParam(name="sort", requirements=@Assert\Choice({"stateLastUpdate", "id", "successDate"}), default="stateLastUpdate")
     * @Rest\QueryParam(name="limit", requirements="\d+", default="100", description="Nombre d'éléments retourné par requête - par défaut 100.")
     * @Rest\QueryParam(name="page", requirements="\d+", default="1", description="Numéro de page de la requête - par défaut la première.")
     * @Rest\View(StatusCode = 200)
     *
     * @param CertificationFolderService $certificationFolderService
     * @param CertificationService $certificationService
     * @param AccessService $accessService
     * @param ParamFetcherInterface $paramFetcher
     * @param string $columnId
     * @param SkillService $skillService
     * @return float
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws Exception
     */
    public function revenueByColumn(CertificationFolderService $certificationFolderService, CertificationService $certificationService, AccessService $accessService, ParamFetcherInterface $paramFetcher, string $columnId, SkillService $skillService): float
    {
        /* @var $user User */
        $user = $this->getUser();
        $organism = $user->getMainOrganism();
        if (!$organism->isCertifierOrganism()) {
            return 0;
        }
        $parameters = $paramFetcher->all(true);
        $parameters = $this->validateListParameters($user, $parameters, $certificationService, $accessService, $skillService);
        return $certificationFolderService->revenueByColumn($organism, $parameters, $columnId);
    }

    /**
     * @Rest\Get("/app/public/certificationFolders/{externalId}/passport/{details}", name="certificationFolders-passport")
     * @Rest\Get("/app/public/registrationFolders/{externalId}/passport", name="registrationFolders-passport")
     * @Rest\View(StatusCode = 200)
     *
     * @param string $externalId
     * @param Request $request
     * @param CertificationFolderService $certificationFolderService
     * @param RegistrationFolderService $registrationFolderService
     * @param string|null $details
     * @return array|Response
     * @throws ContainerExceptionInterface
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws NotFoundExceptionInterface
     */
    public function passport(string $externalId, Request $request, CertificationFolderService $certificationFolderService, RegistrationFolderService $registrationFolderService, string $details = null)
    {
        $details = $details === 'details' && !Tools::contains($request->getPathInfo(), "registrationFolders");
        $component = filter_var($request->get('component', false), FILTER_VALIDATE_BOOLEAN);
        $certificationFolder = Tools::contains($request->getPathInfo(), "registrationFolders") ? ($registrationFolderService->getByExternalId($externalId) ? $registrationFolderService->getByExternalId($externalId)->getCertificationFolder() : null) : $certificationFolderService->getByExternalId($externalId);
        if (!$certificationFolder) {
            return new Response(null, Response::HTTP_NOT_FOUND);
        }
        $certifier = $certificationFolder->getCertifier();
        $subscription = $certifier->getSubscription();
        if (empty($subscription)) {
            return new Response("Not found", 404);
        } else if (!in_array($subscription->getCertifierType(), SubscriptionCertifierTypes::getPaidCertifierTypes())) {
            return new Response("Not available on free subscription", 406);
        } else if ($certificationFolder->isCdcExcluded()) {
            return new Response("CertificationFolder not available", 406);
        }
        $attendee = $certificationFolder->getAttendee();
        $certification = $certificationFolder->getCertification();
        $isPassportPrevention = $certificationFolder->getPassportType() === PassportType::PREVENTION()->getValue();
        if ($isPassportPrevention) {
            $needUpdate = !$attendee->isCdcCompliant();
        } else {
            $needUpdate = $certificationFolderService->isAttendeeDataUpdateNeeded($certificationFolder);
        }
        if ($details) {
            $result = [
                'needUpdate' => $needUpdate,
                'passportType' => $certificationFolder->getPassportType(),
                'certificationFolderState' => $certificationFolder->getState(),
                'certificationName' => $certification->getExternalId() . " " . $certification->getName(),
                'originalCandidate' => [
                    "firstName" => $attendee->getFirstName(),
                    "firstName2" => $attendee->getFirstName2(),
                    "firstName3" => $attendee->getFirstName3(),
                    "lastName" => $attendee->getLastName(),
                    "birthName" => $attendee->getBirthName(),
                    "gender" => $attendee->getGender() ? AttendeeGender::from($attendee->getGender())->getValue() : null,
                    "dateOfBirth" => $attendee->getDateOfBirth(),
                    "nameCityOfBirth" => $attendee->getNameCityOfBirth(),
                    "codeCityOfBirth" => $attendee->getCodeCityOfBirth(),
                    "nameCountryOfBirth" => $attendee->getNameCountryOfBirth(),
                    "codeCountryOfBirth" => $attendee->getCodeCountryOfBirth(),
                    "email" => $attendee->getEmail(),
                    "phoneNumber" => $attendee->getPhoneNumber(),
                    "emailValidated" => $attendee->isEmailValidated(),
                    "phoneNumberValidated" => $attendee->isPhoneNumberValidated(),
                    "nirValidated" => $attendee->isNirValidated(),
                    "retrievedNir" => $attendee->retrievedNir(),
                ],
            ];
            if (!$isPassportPrevention) {
                if ($certificationFolder->getState() !== CertificationFolderStates::SUCCESS()->getValue()) {
                    $accrochageState = 'notSuccess';
                } else {
                    $cdcState = $certificationFolder->getCdcState();
                    $accrochageState = in_array($cdcState, [CertificationFolderCdcStates::PROCESSED_OK()->getValue(), CertificationFolderCdcStates::EXPORTED()->getValue()]) ? $cdcState : 'success';
                }
                $result['accrochageState'] = $accrochageState;
            }
            return $result;
        } else {
            if (!$needUpdate) {
                if ($certificationFolder->getCdcState() === CertificationFolderCdcStates::PROCESSED_OK()->getValue()) {
                    $state = CertificationFolderPassportStates::ADDED()->getValue(); //added to Passport
                } else {
                    $state = CertificationFolderPassportStates::COMPLETED()->getValue(); //no yet (or not passed yet)
                }
            } else if ($certificationFolder->getCdcState() === CertificationFolderCdcStates::PROCESSED_KO()->getValue()) {
                if ($certificationFolder->isCdcToExport()) {
                    $state = CertificationFolderPassportStates::COMPLETED()->getValue();//completed can edit // will be exported
                } else {
                    $state = CertificationFolderPassportStates::INVALID()->getValue();//tried and failed need to be fixed
                }
            } else if ($certificationFolder->isCdcCompliant() && $certificationFolder->getAttendee()->isCdcCompliant()) {
                $state = CertificationFolderPassportStates::COMPLETED()->getValue();//data completed and can edit (case certificationFolder is not in state SUCCESS)
            } else {
                $state = CertificationFolderPassportStates::TO_COMPLETE()->getValue();//data missing
            }
            switch ($state) {
                case CertificationFolderPassportStates::ADDED()->getValue():
                    $text = "Ajouté à votre Passeport";
                    break;
                case CertificationFolderPassportStates::COMPLETED()->getValue():
                    $text = "En attente du dépôt";
                    break;
                case CertificationFolderPassportStates::TO_COMPLETE()->getValue():
                    $text = "Compléter mes données";
                    break;
                default:
                case CertificationFolderPassportStates::INVALID()->getValue():
                    $text = "Corriger mes données";
                    break;
            }
            if ($state === CertificationFolderPassportStates::COMPLETED()->getValue()) {
                $url = "https://competences.moncompteformation.gouv.fr/espace-prive/";
            } else if ($state !== CertificationFolderPassportStates::ADDED()->getValue()) {
                $url = $certificationFolder->getAddToPassportLink();
            } else {
                $url = null;
            }
            $result = [
                "externalId" => $certificationFolder->getExternalId(),
                "url" => $url,
                "state" => $state,
                "description" => $text
            ];
            if ($component) {
                $variables = [
                    'text' => $text,
                    'state' => $state,
                    'baseUrl' => Tools::getEnvValue('WEDOF_BASE_URI'),
                    'class' => filter_var($request->get('inverted', false), FILTER_VALIDATE_BOOLEAN) ? "inverted" : ""
                ];
                $result["component"] = $this->container->get('twig')->render("certificationFolder/passportButton.html.twig", $variables);
            }
            return new Response(json_encode($result));
        }
    }

    /**
     *
     * @Rest\Get("/app/public/certificationFolders/passport/{siret}/{jwt}", requirements={"siret"="\d{14}"})
     * @Rest\QueryParam(name="force", requirements=@Assert\Choice({"false", "true"}), nullable=true)
     * @Rest\View(StatusCode = 200)
     *
     * @param string $jwt
     * @param Organism $certifier
     * @param Request $request
     * @param CertificationPartnerService $certificationPartnerService
     * @param CertifierAccessService $certifierAccessService
     * @param OrganismService $organismService
     * @param CertificationService $certificationService
     * @param CertificationFolderService $certificationFolderService
     * @param RegistrationFolderService $registrationFolderService
     * @param AttendeeService $attendeeService
     * @param ActivityService $activityService
     * @param CityService $cityService
     * @return array|View|Response
     * @throws ContainerExceptionInterface
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws NotFoundExceptionInterface
     * @throws Throwable
     */
    public function passportWithToken(string                      $jwt,
                                      Organism                    $certifier,
                                      Request                     $request,
                                      CertificationPartnerService $certificationPartnerService,
                                      CertifierAccessService      $certifierAccessService,
                                      OrganismService             $organismService,
                                      CertificationService        $certificationService,
                                      CertificationFolderService  $certificationFolderService,
                                      RegistrationFolderService   $registrationFolderService,
                                      AttendeeService             $attendeeService,
                                      CityService                 $cityService,
                                      ActivityService             $activityService)
    {
        $force = filter_var($request->get('forceCertifierAccess', false), FILTER_VALIDATE_BOOLEAN);
        if (!in_array($certifier->getSubscription()->getCertifierType(), SubscriptionCertifierTypes::getPaidCertifierTypes())) {
            return new Response("Not available on free subscription (certifier not customer)", 405);
        }
        //get jwt and check validity
        try {
            /** @var ApiToken $apiToken */
            $apiToken = $certifier->getOwnedBy()->getApiTokens()->first();
            $data = (array)JWT::decode($jwt, $apiToken->getToken(), array('HS256'));
            $data = $this->prepareJwtData($data);
            //$this->validateJwtData($data);
        } catch (Exception $e) {
            return new Response("CertificationFolder not available (jwt invalid)", 406);
        }
        //check if externalId exist on certificationFolder or registrationFolder
        if (isset($data["registrationFolder"]["externalId"])) {
            $registrationFolder = $registrationFolderService->getByExternalId($data["registrationFolder"]["externalId"]);
            $certificationFolder = $registrationFolder ? $registrationFolder->getCertificationFolder() : null;
            if (!$certificationFolder || $certificationFolder->getCertifier() !== $certifier || $certificationFolder->isCdcExcluded()) {
                return new Response("CertificationFolder not available (registrationFolder invalid)", 406);
            }
        } else if (isset($data["certificationFolder"]["externalId"])) {
            $certificationFolder = $certificationFolderService->getByExternalId($data["certificationFolder"]["externalId"]);
            if (!$certificationFolder || $certificationFolder->getCertifier() !== $certifier || $certificationFolder->isCdcExcluded()) {
                return new Response("CertificationFolder not available (certificationFolder invalid)", 406);
            }
        } else {
            //otherwise we may find or create the certificationFolder
            $type = Tools::startsWith($data["certification"]["externalId"], CertificationTypes::RS()->getValue()) ? CertificationTypes::RS() : CertificationTypes::RNCP();
            $code = str_replace(strtolower($type->getValue()), "", strtolower($data["certification"]["externalId"]));
            $certification = $certificationService->getByTypeAndCode($type, $code);
            if (!$certification) {
                return new Response("CertificationFolder not available (certification invalid)", 406);
            }
            $partner = null;
            if (!empty($data["certificationPartner"]["siret"])) {
                $partner = $organismService->getBySiret($data["certificationPartner"]["siret"]);
            }
            if ($partner) {
                $certificationPartner = $certificationPartnerService->getByCertificationAndPartner($certification, $partner);
                //check if partnership is valid
                if (!$certificationPartner
                    || $certificationPartner->getCertifier() !== $certifier
                    || !in_array($certificationPartner->getState(), [CertificationPartnerStates::ACTIVE(), CertificationPartnerStates::SUSPENDED(), CertificationPartnerStates::REVOKED()])) {
                    return new Response("CertificationFolder not available (certificationPartner invalid)", 406);
                }
                $certifierAccess = $certifierAccessService->getByOrganisms($certifier, $partner);
                if (!$certifierAccess && !$force) {
                    return new Response("CertificationFolder not available (certifierAccess invalid)", 406);
                } else {
                    $synchronised = true;
                    $dataProviders = DataProviders::requiredConnectionsDataProvidersForTrainingOrganisms();
                    foreach ($dataProviders as $dataProvider) {
                        $connection = $partner->getConnectionForDataProvider($dataProvider);
                        if ($connection && $connection->getExistAtDataProvider()) {
                            $synchronised = $connection->getState() == ConnectionStates::ACTIVE()->getValue();
                        }
                        if (!$synchronised) {
                            break;
                        }
                    }
                    if (!$synchronised && !$force) {
                        return new Response("CertificationFolder not available (connection invalid)", 406);
                    }
                }
            } else if (!empty($data["certificationPartner"]["siret"])) {
                $data["certificationFolder"]["metadata"] = ["siretPartner" => $data["certificationPartner"]["siret"]];
            }
            $certificationFolders = $certificationFolderService->listReturnQueryBuilder($certifier, [
                'query' => $data["attendee"]["email"],
                'partners' => $partner ? [$partner->getSiret()] : null,
                'certifications' => [$certification->getCertifInfo()],
                'state' => array_diff(CertificationFolderStates::valuesStates(), [CertificationFolderStates::ALL(), CertificationFolderStates::ABORTED(), CertificationFolderStates::REFUSED()])
            ])->getQuery()->getResult();
            if (count($certificationFolders) === 1) { //one match is good
                $certificationFolder = $certificationFolders[0];
            } else if (count($certificationFolders) > 1) { //too many matches
                $ids = [];
                foreach ($certificationFolders as $certificationFolder) {
                    $ids[] = $certificationFolder->getExternalId();
                }
                $ids = join(",", $ids);
                return new Response("Too many certificationFolders found: " . $ids, 406);
            } else {
                try {
                    $attendee = $this->getOrCreateCandidate($attendeeService, $data["attendee"], $cityService);
                } catch (WedofBadRequestHttpException $e) {
                    return new Response($e->getMessage(), 406);
                }
                if (!$attendee) {
                    return new Response("CertificationFolder not available (candidate invalid)", 406);
                }
                $data["certificationFolder"]['attendeeId'] = $attendee->getId();
                $data["certificationFolder"]['certifInfo'] = $certification->getCertifInfo();
                $violations = $this->validateCreateBody($data["certificationFolder"], true);
                if (count($violations)) {
                    return $this->view($violations, Response::HTTP_BAD_REQUEST);
                }
                $data["certificationFolder"]['partner'] = $partner ?: null;
                $certificationFolder = $certificationFolderService->create($certification, $attendee, $partner, $data["certificationFolder"], null, $certifier->getOwnedBy(), $certifier);
                $activityService->create([
                    'title' => "Le dossier de certification a été importé depuis l'historique",
                    'type' => ActivityTypes::UPDATE(),
                    'eventTime' => new DateTime()
                ], null, $certificationFolder);
            }
        }
        if (!$certificationFolder) {
            return new Response("CertificationFolder not available", 406);
        } else {
            $violations = $this->validateForceUpdateData($data["certificationFolder"]);
            if (count($violations)) {
                return $this->view($violations, Response::HTTP_BAD_REQUEST);
            }
            try {
                if (isset($data["certificationFolder"]["stateFr"])) {
                    $targetState = CertificationFolderStates::fromFrString($data["certificationFolder"]['stateFr']);
                    unset($data["certificationFolder"]['stateFr']);
                } else {
                    $targetState = $data["certificationFolder"]['state'] ?? $certificationFolder->getState();
                    if (isset($data["certificationFolder"]['state'])) {
                        unset($data["certificationFolder"]['state']);
                    }
                    $targetState = CertificationFolderStates::from($targetState);
                }
                $preserveExistingData = in_array($certificationFolder->getCdcState(), [CertificationFolderCdcStates::EXPORTED(), CertificationFolderCdcStates::PROCESSED_OK()]);
                $options = ['preserveExistingData' => $preserveExistingData, 'ignoreMissingFiles' => $force];
                $certificationFolder = $certificationFolderService->forceUpdate($certificationFolder, $data["certificationFolder"], $targetState, $certifier->getOwnedBy(), $options);
            } catch (Throwable $e) {
                return new Response($e->getMessage(), 406);
            }
            return $this->passport($certificationFolder->getExternalId(), $request, $certificationFolderService, $registrationFolderService);
        }
    }

    /**
     * @param AttendeeService $attendeeService
     * @param $data
     * @param CityService $cityService
     * @return Attendee|null
     * @throws Exception
     */
    private function getOrCreateCandidate(AttendeeService $attendeeService, $data, CityService $cityService): ?Attendee
    {
        //get or create attendee
        $attendee = null;
        if (isset($data['email'])) {
            $attendee = $attendeeService->getByEmail($data['email']);
        }
        if (!$attendee) {
            $codeCityOfBirth = isset($data['codeCityOfBirth']) && !empty($data['codeCityOfBirth']) ? $data['codeCityOfBirth'] : null;
            $codeCountryOfBirth = isset($data['codeCountryOfBirth']) && !empty($data['codeCountryOfBirth']) ? $data['codeCountryOfBirth'] : null;
            if ($codeCityOfBirth && $codeCountryOfBirth) {
                throw new WedofBadRequestHttpException("Vous devez renseigner soit le codeCityOfBirth soit le codeCountryOfBirth.");
            } else if ($codeCityOfBirth) {
                $city = $cityService->getByCOG($codeCityOfBirth);
                if ($city) {
                    $data['nameCityOfBirth'] = $city->getName();
                } else {
                    throw new WedofBadRequestHttpException("La ville de naissance n'a pas été trouvée");
                }
            } else if ($codeCountryOfBirth) {
                $country = Tools::findCountry($codeCountryOfBirth);
                if ($country) {
                    if ($country['name'] === "France") {
                        throw new WedofBadRequestHttpException("Vous devez renseigner la ville de naissance pour un candidat est né en France");
                    } else {
                        $data['nameCountryOfBirth'] = $country['name'];
                    }
                } else {
                    throw new WedofBadRequestHttpException("Le pays de naissance n'a pas été trouvé");
                }
            }
            $attendee = $attendeeService->createOrUpdate($data);
        }
        return $attendee;
    }

    /**
     * @Rest\Get("/app/certificationFolders")
     * @Security("is_granted('ROLE_USER') or is_granted('ROLE_OAUTH2_CERTIFICATIONFOLDER:READ')", message="not allowed")
     * @Rest\QueryParam(name="siret", requirements=@Assert\Type("string"), nullable=true)
     * @Rest\QueryParam(name="registrationFolderState", requirements=@Assert\Type("string"), nullable=true)
     * @Rest\QueryParam(name="registrationFolderType", requirements=@Assert\Type("string"), nullable=true)
     * @Rest\QueryParam(name="registrationFolderCompletionRate", requirements=@Assert\Choice(">80", "<80"), nullable=true)
     * @Rest\QueryParam(name="state", requirements=@Assert\Type("string"), default="all")
     * @Rest\QueryParam(name="certifInfo", requirements=@Assert\Type("string"), nullable=true)
     * @Rest\QueryParam(name="skillSets", requirements=@Assert\Type("string"), nullable=true)
     * @Rest\QueryParam(name="cdcState", requirements=@Assert\Type("string"), default="all")
     * @Rest\QueryParam(name="cdcCompliant", requirements=@Assert\Choice({"false", "true"}), nullable=true)
     * @Rest\QueryParam(name="cdcToExport", requirements=@Assert\Choice({"false", "true"}), nullable=true)
     * @Rest\QueryParam(name="cdcExcluded", requirements=@Assert\Choice({"false", "true"}), nullable=true)
     * @Rest\QueryParam(name="cdcFile", requirements=@Assert\Type("string"), nullable=true)
     * @Rest\QueryParam(name="messageState", requirements=@Assert\Type("string"), nullable=true)
     * @Rest\QueryParam(name="messageTemplate", requirements=@Assert\Type("string"), nullable=true)
     * @Rest\QueryParam(name="survey", requirements=@Assert\Type("string"), nullable=true)
     * @Rest\QueryParam(name="tags", requirements=@Assert\Type("string"), nullable=true)
     * @Rest\QueryParam(name="metadata", requirements=@Assert\Type("string"), nullable=true, description="Permet d'effectuer une une valeur d'un 'metadata' format : cle:valeur.")
     * @Rest\QueryParam(name="query", requirements=@Assert\Type("string"), nullable=true)
     * @Rest\QueryParam(name="period", requirements=@Assert\Choice({"nextYear", "previousYear", "currentYear", "rollingYear", "rollingYearFuture", "nextMonth", "previousMonth", "currentMonth", "rollingMonth", "rollingMonthFuture", "nextWeek", "previousWeek", "currentWeek", "rollingWeek", "rollingWeekFuture", "tomorrow", "today", "yesterday", "wedofInvoice", "wedofQuota", "custom"}), nullable=true, description="Filtre les dossiers de certification selon la période choisie. Valeurs possibles : 'nextYear', 'previousYear', 'currentYear', 'rollingYear', 'rollingYearFuture', 'nextMonth', 'previousMonth', 'currentMonth', 'rollingMonth', 'rollingMonthFuture', 'nextWeek', 'previousWeek', 'currentWeek', 'rollingWeek', 'rollingWeekFuture', 'tomorrow', 'today', 'yesterday', 'custom'.")
     * @Rest\QueryParam(name="since", requirements=@Assert\AtLeastOneOf({@Assert\DateTime(format="Y-m-d\TH:i:sO"), @Assert\Date()}), nullable=true )
     * @Rest\QueryParam(name="until", requirements=@Assert\AtLeastOneOf({@Assert\DateTime(format="Y-m-d\TH:i:sO"), @Assert\Date()}), nullable=true)
     * @Rest\QueryParam(name="filterOnStateDate", requirements=@Assert\Choice({"stateLastUpdate", "updatedOn", "toTakeDate", "toRetakeDate", "toControlDate", "failedDate", "successDate", "toRegisterDate", "registeredDate", "refusedDate", "abortedDate","notProcessedRegistrationFolderStateDate", "validatedRegistrationFolderStateDate", "acceptedRegistrationFolderStateDate", "inTrainingRegistrationFolderStateDate", "terminatedRegistrationFolderStateDate", "serviceDoneDeclaredRegistrationFolderStateDate", "serviceDoneValidatedRegistrationFolderStateDate", "billedRegistrationFolderStateDate", "refusedByAttendeeRegistrationFolderStateDate", "refusedByOrganismRegistrationFolderStateDate", "canceledByAttendeeRegistrationFolderStateDate", "canceledByOrganismRegistrationFolderStateDate", "canceledByAttendeeNotRealizedRegistrationFolderStateDate", "rejectedWithoutTitulaireSuiteRegistrationFolderStateDate", "sessionStartDateRegistrationFolderDate", "sessionEndDateRegistrationFolderDate", "wedofInvoice", "examinationDate", "examinationEndDate", "enrollmentDate"}), default="stateLastUpdate", nullable=true)
     * @Rest\QueryParam(name="columnIds", requirements=@Assert\Type("string"), nullable=false)
     * @Rest\QueryParam(name="order", requirements=@Assert\Choice({"asc", "desc"}), default="desc")
     * @Rest\QueryParam(name="sort", requirements=@Assert\Choice({"stateLastUpdate", "id", "successDate"}), default="stateLastUpdate")
     * @Rest\QueryParam(name="limit", requirements="\d+", default="100", description="Nombre d'éléments retourné par requête - par défaut 100.")
     * @Rest\QueryParam(name="page", requirements="\d+", default="1", description="Numéro de page de la requête - par défaut la première.")
     * @Rest\View(StatusCode = 200)
     *
     * @param CertificationFolderService $certificationFolderService
     * @param CertificationService $certificationService
     * @param AccessService $accessService
     * @param ParamFetcherInterface $paramFetcher
     * @param SkillService $skillService
     * @return Response
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws Exception
     */
    public function listByColumn(CertificationFolderService $certificationFolderService, CertificationService $certificationService, AccessService $accessService, ParamFetcherInterface $paramFetcher, SkillService $skillService): Response
    {
        /* @var $user User */
        $user = $this->getUser();
        $parameters = $paramFetcher->all(true);

        $columnIds = explode(',', $parameters['columnIds']);
        unset($parameters['columnIds']);

        $parameters = $this->validateListParameters($user, $parameters, $certificationService, $accessService, $skillService);

        $data = $certificationFolderService->listByColumn($user, $parameters, $columnIds);
        $view = $this->view($data, 200);
        return $this->handleView($view);
    }

    /**
     * @Rest\Get("/app/attendees/certificationFolders/{externalId}/attendeeLink")
     * @IsGranted(CertificationFolderVoter::ATTENDEE_VIEW, subject="certificationFolder", message="not allowed")
     * @Rest\View(statusCode=200)
     *
     * @param CertificationFolder $certificationFolder
     * @return string
     */
    public function getAttendeeLink(CertificationFolder $certificationFolder): string
    {
        $subscription = $certificationFolder->getCertifier()->getSubscription();
        $attendeeLink = '';
        if ($subscription && $subscription->isAllowCertifierPlus()) {
            $attendeeLink = '/candidat/certification/dossier/' . $certificationFolder->getExternalId();
        }
        return $attendeeLink;
    }

    /**
     * @Rest\Get("/api/certificationFolders/{id}/getExternalId")
     * @IsGranted(CertificationFolderVoter::VIEW, subject="certificationFolder", message="not allowed")
     * @Rest\View(statusCode=200)
     *
     * @ApiDoc\Areas({"certificationFolders", "default"})
     * @OA\Get (
     *     summary="Récupération de l'externalId d'un dossier de certification.",
     *     description="Récupération de l'externalId d'un dossier de certification par son id. Via OAuth2, cet appel nécessite le scope 'certificationFolder:read'."
     * )
     * @OA\Parameter(
     *     name="id",
     *     in="path",
     *     description="Id du dossier",
     *     @OA\Schema(type="string")
     * )
     *
     * @param CertificationFolder $certificationFolder
     * @return string
     */
    public function getExternalId(CertificationFolder $certificationFolder): string
    {
        return $certificationFolder->getExternalId();
    }

    //--------------
    // METHODES CRUD
    //--------------
    /**
     * @Rest\Get("/api/certificationFolders/{externalId}", name="id-show")
     * @Rest\Get("/app/attendees/certificationFolders/{externalId}", name="externalId-show-app")
     * @Security("is_granted('view', certificationFolder) or is_granted('attendeeView', certificationFolder)", message="not allowed")
     * @Rest\View(StatusCode = 200)
     *
     * @ApiDoc\Areas({"certificationFolders", "default"})
     * @OA\Get (
     *     summary="Récupération d'un dossier de certification.",
     *     description="Récupération d'un dossier de certification par son ExternalId. Via OAuth2, cet appel nécessite le scope 'certificationFolder:read'."
     * )
     * @OA\Response(
     *     response=200,
     *     description="Un json contenant les informations du dossier de certification",
     *     @OA\MediaType(mediaType="application/json",
     *         @OA\Schema(ref="#/components/schemas/CertificationFolder")
     *     )
     * )
     * @OA\Parameter(
     *     name="externalId",
     *     in="path",
     *     description="externalId du dossier",
     *     @OA\Schema(type="string")
     * )
     *
     * @param CertificationFolder $certificationFolder
     * @return Response
     */
    public function show(CertificationFolder $certificationFolder): Response
    {
        $user = $this->getUser();
        $context = new Context();
        if ($user instanceof Attendee) {
            $context->addGroup('attendee');
        } else if ($user instanceof User) {
            if ($certificationFolder->getPartner() === $user->getMainOrganism() && !$certificationFolder->getCertification()->isCertifier($user->getMainOrganism())) {
                $context->addGroup('partnerFiles');
            }
            $context->addGroup('Default'); // Weirdly it's Default for Certifier & Partner alike
        } else {
            throw new LogicException("Classe du user inconnue.");
        }
        $view = $this->view($certificationFolder, 200);
        $view->setContext($context);
        return $this->handleView($view);
    }

    /**
     * @Rest\Get("/api/certificationFolders", name="list")
     * @Security("is_granted('ROLE_USER') or is_granted('ROLE_OAUTH2_CERTIFICATIONFOLDER:READ')", message="not allowed")
     * @Rest\QueryParam(name="siret", requirements=@Assert\Type("string"), nullable=true, description="Permet de n'obtenir que les dossiers issus de l'organisme de formation de siret considéré - par défaut l'organisme de l'utilisateur courant. Le paramètre 'all' permet de récupérer tous les dossiers de tous les organismes auquel l'utilisateur courant à droit. Il est possible de filtrer sur plusieurs organismes en séparant chaque SIRET par une virgule.")
     * @Rest\QueryParam(name="registrationFolderState", requirements=@Assert\Type("string"), nullable=true, description="Permet de n'obtenir que les dossiers dans l'état considéré - par défaut tous les états sont retournés. Valeurs possibles : 'notProcessed', 'validated', 'waitingAcceptation', 'rejectedWithoutTitulaireSuite', 'rejected', 'rejectedWithoutCdcSuite', 'accepted', 'inTraining', 'terminated', 'serviceDoneDeclared', 'serviceDoneValidated', 'canceledByAttendee', 'canceledByAttendeeNotRealized', 'canceledByOrganism', 'refusedByAttendee', 'refusedByOrganism'. Il est possible de demander plusieurs états en séparant chaque état par une virgule, ex : 'accepted,inTraining'.")
     * @Rest\QueryParam(name="registrationFolderType", requirements=@Assert\Type("string"), nullable=true, description="Permet de n'obtenir que les dossiers dans le type considéré - par défaut tous les types sont retournés. Valeurs possibles: 'cpf', 'individual', 'poleEmploi', 'company', 'opco', 'opcoCfa', 'kairosAif'. Il est possible de demander plusieurs types en séparant chaque type par une virgule, ex : 'cpf,opco'.")
     * @Rest\QueryParam(name="registrationFolderCompletionRate", requirements=@Assert\Choice(">80", "<80"), nullable=true)
     * @Rest\QueryParam(name="state", requirements=@Assert\Type("string"), default="all", description="Permet de n'obtenir que les dossiers dans l'état d'obtention de la certification considéré - par défaut tous les états sont retournés. Valeurs possibles : 'all', 'toRegister', 'refused', 'registered', 'toTake', 'toControl', 'toRetake', 'failed', 'aborted' 'success'. Il est possible de demander plusieurs états en séparant chaque état par une virgule, ex : 'toRegister,toTake'.")
     * @Rest\QueryParam(name="certifInfo", requirements=@Assert\Type("string"), nullable=true, description="Permet de n'obtenir que les dossiers liés à la certification considérée - par défaut tous les dossiers de toutes les certifications sont retournés. Il est possible de filtrer sur plusieurs certifications en séparant chaque certifInfo par une virgule.")
     * @Rest\QueryParam(name="skillSets", requirements=@Assert\Type("string"), nullable=true, description="Permet de n'otenir que les dossiers liés à une certification RNCP pour les blocs de compétences considérés - par défaut tous les dossiers sont retournés.")
     * @Rest\QueryParam(name="cdcState", requirements=@Assert\Type("string"), default="all", description="Permet de n'obtenir que les dossiers dans l'état considéré lié à l'export des dossiers - par défaut tous les dossiers sont retournés. Valeurs possibles : 'all', 'notExported', 'exported', 'processedOk', 'processedKo'")
     * @Rest\QueryParam(name="cdcCompliant", requirements=@Assert\Choice({"false", "true"}), nullable=true, description="Permet de filtrer les dossiers de certification selon le fait qu'ils contiennent les données du candidat obligatoires pour l'accrochage en cas d'obtention de la certification")
     * @Rest\QueryParam(name="cdcToExport", requirements=@Assert\Choice({"false", "true"}), nullable=true, description="Permet de filtrer les dossiers de certification qui devront être inclus dans les prochains exports pour l'accrochage (par défaut oui, sauf si déjà accroché avec succès)")
     * @Rest\QueryParam(name="cdcExcluded", requirements=@Assert\Choice({"false", "true"}), nullable=true, description="Permet de filtrer les dossiers de certification qui sont exclus de l'accrochage")
     * @Rest\QueryParam(name="cdcFile", requirements=@Assert\Type("string"), nullable=true, description="Permet de filtrer les dossiers de certification exportés sur un fichier XML lié à l'accrochage")
     * @Rest\QueryParam(name="messageState", requirements=@Assert\Type("string"), nullable=true, description="Permet de n'obtenir que les dossiers liés à l'état d'envoi d'un message considéré - par défaut tous les dossiers sont retournés. Valeurs possibles : 'sent', 'notSent', 'notSentUnauthorized', 'notSentEnforcedConditions', 'failed', 'scheduled'.")
     * @Rest\QueryParam(name="messageTemplate", requirements=@Assert\Type("string"), nullable=true, description="Permet de n'obtenir que les dossiers qui ont un message issu des modèles choisis - par défaut aucun filtre.")
     * @Rest\QueryParam(name="survey", requirements=@Assert\Type("string"), nullable=true, description="Permet de n'obtenir que les dossiers pour lequels un questionnaire doit être répondu ou a été répondu - par défaut aucun filtre. Valeurs possibles: 'initialExperienceStartDate', 'sixMonthExperienceStartDate', 'longTermExperienceStartDate', 'initialExperienceAnsweredDate', 'sixMonthExperienceAnsweredDate', 'longTermExperienceAnsweredDate'.")
     * @Rest\QueryParam(name="tags", requirements=@Assert\Type("string"), nullable=true, description="Permet d'effectuer une recherche sur le champ 'tags'.")
     * @Rest\QueryParam(name="metadata", requirements=@Assert\Type("string"), nullable=true, description="Permet d'effectuer une une valeur d'un 'metadata' format : cle:valeur.")
     * @Rest\QueryParam(name="query", requirements=@Assert\Type("string"), nullable=true, description="Permet d'effectuer une recherche libre sur les champs 'nom du candidat', 'prénom du candidat', 'email du candidat', 'tags', 'commentaire', 'id du dossier de certification' et 'phoneNumber'.")
     * @Rest\QueryParam(name="period", requirements=@Assert\Choice({"nextYear", "previousYear", "currentYear", "rollingYear", "rollingYearFuture", "nextMonth", "previousMonth", "currentMonth", "rollingMonth", "rollingMonthFuture", "nextWeek", "previousWeek", "currentWeek", "rollingWeek", "rollingWeekFuture", "tomorrow", "today", "yesterday", "wedofInvoice", "wedofQuota", "custom"}), nullable=true, description="Filtre les dossiers de certification selon la période choisie. Valeurs possibles : 'nextYear', 'previousYear', 'currentYear', 'rollingYear', 'rollingYearFuture', 'nextMonth', 'previousMonth', 'currentMonth', 'rollingMonth', 'rollingMonthFuture', 'nextWeek', 'previousWeek', 'currentWeek', 'rollingWeek', 'rollingWeekFuture', 'tomorrow', 'today', 'yesterday', 'custom'.")
     * @Rest\QueryParam(name="since", requirements=@Assert\AtLeastOneOf({@Assert\DateTime(format="Y-m-d\TH:i:sO"), @Assert\Date()}), nullable=true, description="Filtre les dossiers dont le 'filterOnStateDate' démarre à la date choisie. Date au format ISO-8601 2021-08-05T09:53:54Z ou Date au format AAAA-MM-JJ, 2021-08-05")
     * @Rest\QueryParam(name="until", requirements=@Assert\AtLeastOneOf({@Assert\DateTime(format="Y-m-d\TH:i:sO"), @Assert\Date()}), nullable=true, description="Filtre les dossiers dont le 'filterOnStateDate' termine à la date choisie. Date au format ISO-8601 2021-08-05T09:53:54Z ou Date au format AAAA-MM-JJ, 2021-08-05")
     * @Rest\QueryParam(name="filterOnStateDate", requirements=@Assert\Choice({"stateLastUpdate", "updatedOn", "toTakeDate", "toRetakeDate", "toControlDate", "failedDate", "successDate", "toRegisterDate", "registeredDate", "refusedDate", "abortedDate","notProcessedRegistrationFolderStateDate", "validatedRegistrationFolderStateDate", "acceptedRegistrationFolderStateDate", "inTrainingRegistrationFolderStateDate", "terminatedRegistrationFolderStateDate", "serviceDoneDeclaredRegistrationFolderStateDate", "serviceDoneValidatedRegistrationFolderStateDate", "billedRegistrationFolderStateDate", "refusedByAttendeeRegistrationFolderStateDate", "refusedByOrganismRegistrationFolderStateDate", "canceledByAttendeeRegistrationFolderStateDate", "canceledByOrganismRegistrationFolderStateDate", "canceledByAttendeeNotRealizedRegistrationFolderStateDate", "rejectedWithoutTitulaireSuiteRegistrationFolderStateDate", "sessionStartDateRegistrationFolderDate", "sessionEndDateRegistrationFolderDate", "wedofInvoice", "examinationDate", "examinationEndDate", "enrollmentDate"}), default="stateLastUpdate", nullable=true, description="Filtre entre deux dates sur l'un des critères suivants : 'stateLastUpdate', 'updatedOn', 'toTakeDate', 'toRetakeDate', 'toControlDate', 'failedDate', 'successDate', 'toRegisterDate', 'registeredDate', 'refusedDate', 'abortedDate', 'notProcessedRegistrationFolderStateDate', 'validatedRegistrationFolderStateDate', 'acceptedRegistrationFolderStateDate', 'inTrainingRegistrationFolderStateDate', 'terminatedRegistrationFolderStateDate', 'serviceDoneDeclaredRegistrationFolderStateDate', 'serviceDoneValidatedRegistrationFolderStateDate', 'billedRegistrationFolderStateDate', 'refusedByAttendeeRegistrationFolderStateDate', 'refusedByOrganismRegistrationFolderStateDate', 'canceledByAttendeeRegistrationFolderStateDate', 'canceledByOrganismRegistrationFolderStateDate', 'canceledByAttendeeNotRealizedRegistrationFolderStateDate', 'rejectedWithoutTitulaireSuiteRegistrationFolderStateDate', 'wedofInvoice', 'examinationDate', 'examinationEndDate', 'enrollmentDate' - par défaut stateLastUpdate.")
     * @Rest\QueryParam(name="order", requirements=@Assert\Choice({"asc", "desc"}), default="desc", description="Tri les résultats par ordre ascendant ou descendant - par défaut descendant.")
     * @Rest\QueryParam(name="sort", requirements=@Assert\Choice({"stateLastUpdate", "id", "successDate"}), default="stateLastUpdate", description="Tri les résultats sur un critère. Valeurs possibles: 'stateLastUpdate' (date du dernier changement d'état), 'id' (id de base de donnée) - par défaut 'stateLastUpdate'.")
     * @Rest\QueryParam(name="format", requirements=@Assert\Type("string"), default="json", description="Permet d'obtenir une liste des dossiers de certification au format json ou csv. Valeurs possibles : 'json', 'csv'")
     * @Rest\QueryParam(name="csvColumns", requirements=@Assert\Type("string"), nullable=true, description="Permet de choisir les colonnes souhaitées pour l'export des dossiers de formation au format csv. Valeurs possibles :'CERTIFICATION', 'OBTENTION', 'ORGANISME', 'ORGANISME_SIRET', 'NUMERO_DOSSIER', 'SEXE', 'ESPACE_CANDIDAT', 'CANDIDAT', 'EMAIL', 'TELEPHONE', 'STATUT', 'STATUT_ACCROCHAGE', 'DATE_INSCRIPTION', 'EXAMINATION_PLACE', 'EXAMINATION_DATE', 'EXPIRATION_DATE', 'COMMENTAIRE', 'PREUVE_NUMERIQUE', 'RESULTAT', 'OPTION', 'EXAMINATION_TYPE', 'EXAMINATION_CENTER_CODE_POSTAL', 'NIVEAU_LANGUE_EUROPEEN', 'MENTION', 'VERBATIM', 'MODALITE_ACCESS', 'MODALITE_ACCESS_VAE', 'TYPE', 'DOSSIER_COMPLET', 'STATUT_EXPORTABILITE', 'DATE_DE_NAISSANCE', 'CODE_INSEE_VILLE_NAISSANCE', 'VILLE_NAISSANCE', 'CODE_PAYS_NAISSANCE', 'PAYS_NAISSANCE'. ")
     * @Rest\QueryParam(name="columnId", requirements=@Assert\Type("string"), nullable=true)
     * @Rest\QueryParam(name="limit", requirements="\d+", default="100", description="Nombre d'éléments retourné par requête - par défaut 100.")
     * @Rest\QueryParam(name="page", requirements="\d+", default="1", description="Numéro de page de la requête - par défaut la première.")
     *
     * @ApiDoc\Areas({"certificationFolders", "default"})
     * @OA\Get (
     *     summary="Liste les dossiers de certification selon des critères.",
     *     description="Récupère l'ensemble des dossiers de certification en fonction du siret et du certifInfo. NOTA : les paramètres sont cumulatifs et deux paramètres incompatibles renverront un résultat vide. Via OAuth2, cet appel nécessite le scope 'certificationFolder:read'."
     * )
     * @OA\Response(
     *     response=200,
     *     description="Un tableau de dossiers au format JSON ou CSV selon le paramètre 'format' ",
     *     @OA\JsonContent(
     *          type="array",
     *          @OA\Items(ref="#/components/schemas/CertificationFolder")
     *     )
     * )
     * @OA\Parameter (name="order", in="query", @OA\Schema (ref="#/components/schemas/Order"))
     *
     * @param CertificationFolderService $certificationFolderService
     * @param CertificationService $certificationService
     * @param AccessService $accessService
     * @param PaginatorInterface $paginator
     * @param ParamFetcherInterface $paramFetcher
     * @param Request $request
     * @param EntityManagerInterface $entityManager
     * @param SkillService $skillService
     * @return Response
     * @throws \PhpOffice\PhpSpreadsheet\Exception
     * @throws \PhpOffice\PhpSpreadsheet\Reader\Exception
     * @throws Exception
     */
    public function list(CertificationFolderService $certificationFolderService, CertificationService $certificationService, AccessService $accessService, PaginatorInterface $paginator, ParamFetcherInterface $paramFetcher, Request $request, EntityManagerInterface $entityManager, SkillService $skillService): Response
    {
        /* @var $user User */
        $user = $this->getUser();
        $parameters = $paramFetcher->all(true);

        $parameters = $this->validateListParameters($user, $parameters, $certificationService, $accessService, $skillService);

        $format = null;
        if ($parameters['format'] === 'csv' || str_contains($request->headers->get('Accept'), 'csv')) {
            $format = 'csv';
        } else if ($parameters['format'] === 'xlsx' || str_contains($request->headers->get('Accept'), 'xlsx')) {
            $format = 'xlsx';
        }

        if (isset($format)) {
            set_time_limit(300);
            $page = 1;
            $limit = 100;
            $nbPages = 1; // initialized to 1 for first page, then updated according to dynamic data
            $entityManager->getConnection()->getConfiguration()->setSQLLogger(); // for perf in dev, maybe in prod ?

            if ($format === 'xlsx') {
                $reader = new XlsxReader();
                $templateFileName = $this->getParameter('kernel.project_dir') . '/data/wedof-modele-import-dossiers-certification.xlsx';
                $spreadsheet = $reader->load($templateFileName);
                $worksheet = $spreadsheet->getActiveSheet();
                $rowIndex = 2;

                while ($page <= $nbPages) {
                    $data = $paginator->paginate($certificationFolderService->listReturnQueryBuilder($user->getMainOrganism(), $parameters), $page, $limit);
                    /* @var $certificationFolder CertificationFolder */
                    foreach ($data->getItems() as $certificationFolder) {
                        $row = $this->getSpreadsheetRowFromCertificationFolder($certificationFolder);
                        $columnIndex = 1;
                        foreach ($row as $columnName => $cellValue) {
                            $coordinates = [$columnIndex, $rowIndex];
                            if (in_array($columnName, ['idCpf', 'organismSiret', 'phoneNumber'])) {
                                $richText = new RichText();
                                $richText->createText($cellValue);
                                $cellValue = $richText;
                            }
                            $worksheet->setCellValue($coordinates, $cellValue);
                            if ($columnName === 'externalId') {
                                $worksheet->getCell($coordinates)->getHyperlink()->setUrl($certificationFolder->getPermalink());
                            } else if ($columnName === 'digitalProofLink' && $cellValue) {
                                $worksheet->getCell($coordinates)->getHyperlink()->setUrl($cellValue);
                            }
                            $columnIndex++;
                        }
                        $rowIndex++;
                    }
                    if ($page === 1) {
                        $nbPages = intdiv($data->getTotalItemCount(), $limit) + 1;
                    }
                    $page++;
                    $entityManager->clear();
                }

                $writer = new XlsxWriter($spreadsheet);
                return Tools::getExcelResponse($writer, 'certification_folders');
            } else if ($format === 'csv') {
                $tmpFile = null;

                while ($page <= $nbPages) {
                    $data = $paginator->paginate($certificationFolderService->listReturnQueryBuilder($user->getMainOrganism(), $parameters), $page, $limit);
                    $isFirstPage = $page === 1;
                    $availableColumns = ['CERTIFICATION', 'OBTENTION', 'ORGANISME', 'ORGANISME_SIRET', 'NUMERO_DOSSIER', 'SEXE', 'ESPACE_CANDIDAT', 'CANDIDAT', 'EMAIL', 'TELEPHONE', 'STATUT', 'STATUT_ACCROCHAGE', 'DATE_INSCRIPTION',
                        'EXAMINATION_TYPE', 'EXAMINATION_PLACE', 'EXAMINATION_CENTER_CODE_POSTAL', 'EXAMINATION_DATE', 'EXPIRATION_DATE', 'COMMENTAIRE', 'PREUVE_NUMERIQUE', 'RESULTAT', 'OPTION', 'NIVEAU_LANGUE_EUROPEEN', 'MENTION', 'VERBATIM',
                        'MODALITE_ACCESS', 'MODALITE_ACCESS_VAE', 'TYPE', 'DATE_DE_NAISSANCE', 'CODE_INSEE_VILLE_NAISSANCE', 'VILLE_NAISSANCE', 'CODE_PAYS_NAISSANCE', 'PAYS_NAISSANCE', 'DOSSIER_COMPLET', 'STATUT_EXPORTABILITE'];
                    $tmpFile = Tools::convertDataToCSVFile($data, $availableColumns, $parameters['csvColumns'] ?? null, $tmpFile, $isFirstPage);
                    if ($isFirstPage) {
                        $nbPages = intdiv($data->getTotalItemCount(), $limit) + 1;
                    }
                    $page++;
                    $entityManager->clear();
                }

                return Tools::getCsvResponse($tmpFile, 'certification_folders');
            } else {
                throw new LogicException('Format non pris en charge');
            }
        } else {
            $data = $paginator->paginate($certificationFolderService->listReturnQueryBuilder($user->getMainOrganism(), $parameters), intval($parameters['page']), intval($parameters['limit']));
            $view = $this->view($data->getItems(), 200);
            $view->setHeader("x-total-count", $data->getTotalItemCount());
            $view->setHeader("x-current-page", $data->getCurrentPageNumber());
            $view->setHeader("x-item-per-page", $data->getItemNumberPerPage());
            return $this->handleView($view);
        }
    }

    /**
     * @Rest\Post ("/api/certificationFolders")
     * @Security("is_granted('ROLE_USER') or is_granted('ROLE_OAUTH2_CERTIFICATIONFOLDER:READ')", message="not allowed")
     * @Rest\View(StatusCode = 201)
     *
     * @ApiDoc\Areas({"certificationFolders", "default"})
     * @OA\Post (
     *     summary="Créer un dossier de certification",
     *     description="Permet de créer un nouveau dossier de certification. Via OAuth2, cet appel nécessite le scope 'certificationfolder:write'"
     * )
     * @OA\Response(
     *     response=201,
     *     description="Un json contenant les informations du nouveau dossier de certification créé",
     *     @OA\MediaType(mediaType="application/json",
     *         @OA\Schema(ref="#/components/schemas/CertificationFolder")
     *     )
     * )
     *
     * @OA\RequestBody(
     *     @OA\MediaType(mediaType="application/json",
     *         @OA\Schema(ref="#/components/schemas/CertificationFolderCreateBody")
     *     )
     * )
     *
     * @param AttendeeService $attendeeService
     * @param OrganismService $organismService
     * @param CertificationPartnerService $certificationPartnerService
     * @param CertificationService $certificationService
     * @param Request $request
     * @param CertificationFolderService $certificationFolderService
     * @param SkillService $skillService
     * @return CertificationFolder|View
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws Throwable
     */
    public function create(AttendeeService $attendeeService, OrganismService $organismService, CertificationPartnerService $certificationPartnerService, CertificationService $certificationService, Request $request, CertificationFolderService $certificationFolderService, SkillService $skillService)
    {
        /** @var User $user */
        $user = $this->getUser();
        $organism = $user->getMainOrganism();
        $body = json_decode($request->getContent(), true);
        if (!empty($body['enrollmentDate'])) {
            if (strtotime($body['enrollmentDate'])) {
                $body['enrollmentDate'] = (new DateTime($body['enrollmentDate']))->setTimezone(new DateTimeZone('Europe/Paris'));
            }
        }
        $violations = $this->validateCreateBody($body);
        if (count($violations)) {
            return $this->view($violations, Response::HTTP_BAD_REQUEST);
        }
        $certification = $certificationService->getByCertifInfo($body['certifInfo']);
        if (!$certification) {
            throw new WedofNotFoundHttpException("La certification associée au certifInfo " . $body['certifInfo'] . " n'a pas été trouvée.");
        }
        if (!$this->isGranted(CertificationVoter::VIEW, $certification)) { // Useless ?
            throw new WedofBadRequestHttpException("Vous n'êtes pas autorisé à créer un dossier de certification sur cette certification.");
        }
        $attendee = $attendeeService->getById($body['attendeeId']);
        if (!$attendee) {
            throw new WedofNotFoundHttpException("Le candidat associé à l'id " . $body['attendeeId'] . " n'a pas été trouvé.");
        }
        $isCertifier = $certification->isCertifier($organism);
        $certifier = $isCertifier ? $organism : null;
        if (isset($body['partner'])) {
            $partner = $organismService->getBySiret($body['partner']);
            if (!$partner) {
                throw new WedofNotFoundHttpException("L'organisme de formation associé avec le siret " . $body['partner'] . " n'a pas été trouvé.");
            }
            $certificationPartner = $certificationPartnerService->getByCertificationAndPartner($certification, $partner, false);
            if ($isCertifier) {
                if ($partner !== $organism) {
                    if (!$certificationPartner || !in_array($certificationPartner->getState(), [CertificationPartnerStates::ACTIVE(), CertificationPartnerStates::SUSPENDED(), CertificationPartnerStates::REVOKED()])) {
                        throw new WedofBadRequestHttpException("En tant que certificateur, vous ne pouvez pas définir comme organisme de formation d'un dossier de certification un organisme qui n'a jamais été partenaire");
                    }
                    if ($certificationPartner->getCertifier() !== $organism) {
                        throw new WedofBadRequestHttpException("En tant que certificateur, vous ne pouvez pas définir comme organisme de formation d'un dossier de certification un organisme déjà géré par un co-certificateur");
                    }
                } else if (!$organism->isTrainingOrganism()) {
                    throw new WedofBadRequestHttpException("En tant que certificateur, vous ne pouvez pas vous définir comme organisme de formation d'un dossier de certification sans être vous-même organisme de formation");
                }
            } else {
                if ($partner !== $organism) {
                    throw new WedofBadRequestHttpException("En tant que partenaire, vous ne pouvez pas créer un dossier de certification sur un autre organisme que le votre");
                }
                if (!$certificationPartner || !in_array($certificationPartner->getState(), [CertificationPartnerStates::ACTIVE(), CertificationPartnerStates::SUSPENDED(), CertificationPartnerStates::REVOKED()])) {
                    throw new WedofBadRequestHttpException("Vous ne pouvez pas créer un dossier de certification sur une certification dont vous n'avez jamais été partenaire");
                }
            }
        } else {
            if (!$isCertifier) {
                throw new WedofBadRequestHttpException("Seul le certificateur peut créer un dossier de certification sans organisme partenaire");
            }
            $partner = null;
        }
        if (!empty($body['skillSets'])) {
            if (!$certification->isAllowPartialSkillSets()) {
                throw new WedofBadRequestHttpException('Erreur, les skillSets ne peuvent être déclarés que pour une certification dont l\'enseignement peut être divisé par blocs de compétences.');
            }
            $body['skillSets'] = $this->getSkillSets($body['skillSets'], $certification, $user->getMainOrganism(), $skillService, $certificationPartner ?? null);
        }
        if (isset($body['accessModality']) && $body['accessModality'] === CertificationFolderAccessModality::VAE()->getValue() && !isset($body['accessModalityVae'])) {
            throw new WedofBadRequestHttpException("Erreur, vous devez préciser le type de VAE en renseignant 'accessModalityVae'.");
        }
        if (isset($body['accessModality']) && $body['accessModality'] !== CertificationFolderAccessModality::VAE()->getValue() && isset($body['accessModalityVae'])) {
            throw new WedofBadRequestHttpException("Erreur, vous ne pouvez pas préciser 'accessModalityVae' si 'accessModality' n'est pas de type VAE.");
        }
        $result = $certificationFolderService->create($certification, $attendee, $partner, $body, null, $user, $certifier);
        if ($result === false) { // This checks subscription & certifierAccess
            throw new WedofBadRequestHttpException("Erreur, le dossier n'a pas pu être créé (soit le certificateur n'a pas l'abonnement nécessaire, soit vous n'avez pas partagé vos données avec lui");
        }
        return $result;
    }

    /**
     * @Rest\Route("/app/certificationFolders/{externalId}", name="app_certificationfolder_update", methods={"POST", "PUT"})
     * @Rest\Put("/api/certificationFolders/{externalId}", name="update")
     * @IsGranted(CertificationFolderVoter::EDIT, subject="certificationFolder",  message="not allowed")
     * @Rest\View(StatusCode = 200)
     *
     * @ApiDoc\Areas({"certificationFolders", "default"})
     * @OA\Put (
     *     summary="Met à jour le dossier de certification.",
     *     description="Permet de mettre à jour le dossier de certification. Via OAuth2, cet appel nécessite le scope 'certificationFolder:write'."
     * )
     * @OA\Response(
     *     response=200,
     *     description="Un json contenant les informations du dossier de certification",
     *     @OA\MediaType(mediaType="application/json",
     *         @OA\Schema(ref="#/components/schemas/CertificationFolder")
     *     )
     * )
     * @OA\Parameter(
     *     name="externalId",
     *     in="path",
     *     description="externalId du dossier",
     *     @OA\Schema(type="string")
     * )
     * @OA\RequestBody(
     *     @OA\MediaType(mediaType="application/json",
     *         @OA\Schema(ref="#/components/schemas/CertificationFolderUpdateBody")
     *     )
     * )
     *
     * @param CertificationFolder $certificationFolder
     * @param CertificationFolderService $certificationFolderService
     * @param OrganismService $organismService
     * @param CertificationPartnerService $certificationPartnerService
     * @param SkillService $skillService
     * @return CertificationFolder|View
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws Throwable
     */
    public function update(CertificationFolder $certificationFolder, CertificationFolderService $certificationFolderService, OrganismService $organismService, CertificationPartnerService $certificationPartnerService, SkillService $skillService)
    {
        $body = $this->getData();
        $certificationPartner = null;
        if (!empty($body)) {
            /* @var $user User */
            $user = $this->getUser();
            $organism = $user->getMainOrganism();
            if (!empty($body['examinationDate'])) {
                if (strtotime($body['examinationDate'])) {
                    $body['examinationDate'] = (new DateTime($body['examinationDate']))->setTimezone(new DateTimeZone("UTC"));
                }
            }
            if (!empty($body['examinationEndDate'])) {
                if (strtotime($body['examinationEndDate'])) {
                    $body['examinationEndDate'] = (new DateTime($body['examinationEndDate']))->setTimezone(new DateTimeZone("UTC"));
                }
                if (empty($body['examinationDate']) && !$certificationFolder->getExaminationDate()) {
                    throw new WedofBadRequestHttpException("Erreur, veuillez remplir une date de début de passage d'examen (examinationDate) afin de modifier la date de fin de passage d'examen (examinationEndDate). ");
                }
            }
            if (!empty($body['enrollmentDate'])) {
                if (strtotime($body['enrollmentDate'])) {
                    $body['enrollmentDate'] = (new DateTime($body['enrollmentDate']))->setTimezone(new DateTimeZone('Europe/Paris'));
                    $body['enrollmentDate']->setTime(0, 0);
                }
            }
            if (!empty($body['amountHt']) && is_numeric($body['amountHt'])) {
                $body['amountHt'] = (float)($body['amountHt']);
            }

            if (!empty($body['cdcTechnicalId'])) {
                if ($organism !== $certificationFolder->getCertifier()) {
                    throw new WedofBadRequestHttpException("Erreur, seul le certificateur du dossier de certification peut modifier le 'cdcTechnicalId'.");
                }
                if ($certificationFolder->getCdcTechnicalId() && $certificationFolder->getCdcState() !== CertificationFolderCdcStates::NOT_EXPORTED()->getValue()) {
                    throw new WedofBadRequestHttpException("Erreur, vous ne pouvez pas modifier le 'cdcTechnicalId' car le dossier à déjà été soumis à l'accrochage.");
                }
                $certificationFoldersWithCdcTechnicalId = $certificationFolderService->listByCertificationAndTechnicalId($certificationFolder->getCertification(), $body['cdcTechnicalId']);
                if ($certificationFoldersWithCdcTechnicalId->count() !== 0) {
                    throw new WedofBadRequestHttpException("Erreur, le 'cdcTechnicalId' existe déjà sur un dossier de certification");
                }
                if ((string)$certificationFolder->getId() === $body['cdcTechnicalId']) {
                    throw new WedofBadRequestHttpException("Le dossier sera accroché avec l'id technique du dossier, vous n'avez pas besoin de le renseigner");
                }
            }

            if (array_key_exists('metadata', $body)) {
                if (is_string($body['metadata'])) {
                    $body['metadata'] = json_decode($body['metadata'], true);
                    if (json_last_error() !== JSON_ERROR_NONE) {
                        throw new WedofBadRequestHttpException("Erreur, le champ metadata doit être un JSON valide");
                    }
                }

                if (!is_array($body['metadata']) && $body['metadata'] !== null) {
                    throw new WedofBadRequestHttpException("Erreur, le champ metadata doit être un JSON valide");
                }
            }

            $violations = $this->validateUpdateBody($body);
            if (count($violations)) {
                return $this->view($violations, Response::HTTP_BAD_REQUEST);
            }

            $certification = $certificationFolder->getCertification();
            if (in_array($certification->getType(), [CertificationTypes::INTERNAL()->getValue(), CertificationTypes::PREVENTION()->getValue()]) && isset($body['cdcExcluded'])) {
                unset($body['cdcExcluded']);
            }

            if (array_key_exists('partner', $body)) {
                $oldPartner = $certificationFolder->getPartner();
                $oldPartnerSiret = $oldPartner ? $oldPartner->getSiret() : null;
                $partnerSiret = $body['partner'];
                if ($partnerSiret !== $oldPartnerSiret) {
                    if ($organism !== $certificationFolder->getCertifier()) {
                        throw new WedofBadRequestHttpException("Seul le certificateur du dossier de certification peut modifier l'organisme partenaire.");
                    }
                    if ($certificationFolder->getRegistrationFolder()) {
                        throw new WedofBadRequestHttpException("L'organisme partenaire ne peut pas être modifié car le dossier de certification est associé à un dossier de formation");
                    }
                    $partner = null;
                    if ($partnerSiret) {
                        $partner = $organismService->getBySiret($partnerSiret);
                        if (!$partner) {
                            throw new WedofBadRequestHttpException("L'organisme partenaire associé au siret " . $partnerSiret . " n'a pas été trouvé.");
                        }
                        if ($partner !== $organism) {
                            $certificationPartner = $certificationPartnerService->getByCertificationAndPartner($certification, $partner, false);
                            if (!$certificationPartner || !in_array($certificationPartner->getState(), [CertificationPartnerStates::ACTIVE(), CertificationPartnerStates::SUSPENDED(), CertificationPartnerStates::REVOKED()])) {
                                throw new WedofBadRequestHttpException("En tant que certificateur, vous ne pouvez pas définir comme organisme de formation d'un dossier de certification un organisme qui n'a jamais été partenaire");
                            }
                            if ($certificationPartner->getCertifier() !== $organism) {
                                throw new WedofBadRequestHttpException("En tant que certificateur, vous ne pouvez pas définir comme organisme de formation d'un dossier de certification un organisme déjà géré par un co-certificateur");
                            }
                        } else if (!$organism->isTrainingOrganism()) {
                            throw new WedofBadRequestHttpException("En tant que certificateur, vous ne pouvez pas vous définir comme organisme de formation d'un dossier de certification sans être vous-même organisme de formation");
                        }
                    }
                    $body['partner'] = $partner;
                } else {
                    unset($body['partner']);
                }
            }

            if (!empty($body['skillSets'])) {
                if (!$certification->isAllowPartialSkillSets()) {
                    throw new WedofBadRequestHttpException('Erreur, les skillSets ne peuvent être déclarés que pour une certification dont l\'enseignement peut être divisé par blocs de compétences.');
                }
                $body['skillSets'] = $this->getSkillSets($body['skillSets'], $certification, $user->getMainOrganism(), $skillService, $certificationPartner);
            }

            if (!empty($body['addedTags']) || !empty($body['removedTags'])) {
                $tags = $body['tags'] ?? Tools::tagsToArray($certificationFolder);
                if (!empty($body['addedTags'])) {
                    $tags = array_merge($tags, array_map('strtolower', $body['addedTags']));
                }
                if (!empty($body['removedTags'])) {
                    $tags = array_diff($tags, array_map('strtolower', $body['removedTags']));
                }
                $body['tags'] = $tags;
            }

            if (array_key_exists('badgeAssertion', $body) && isset($body['badgeAssertion'])) {
                if ($body['badgeAssertion'] === $certificationFolder->getBadgeAssertion()) {
                    unset($body['badgeAssertion']);
                } else {
                    $isValidUrl = filter_var($body['badgeAssertion'], FILTER_VALIDATE_URL);
                    if (!$isValidUrl) {
                        throw new WedofBadRequestHttpException("Erreur, le champ 'badgeAssertion' n'est pas une URL valide.");
                    }
                    if (!$certificationFolderService->verifyBadgeAssertion($body['badgeAssertion'])) {
                        throw new WedofBadRequestHttpException("Erreur, le lien vers le badge de la certification n'est pas une assertion ou ne contient pas l'image du badge ou est expiré.");
                    }
                }
            }

            $certificationFolder = $certificationFolderService->update($certificationFolder, $body, $organism, $user);
        }
        return $certificationFolder;
    }

    /**
     * @Rest\Post("/app/certificationFolders/updateBatch", defaults={"processingMethod"="batch"}, requirements={"processingMethod"="batch"})
     * @Rest\Post("/api/certificationFolders/updateAsync", defaults={"processingMethod"="async"}, requirements={"processingMethod"="async"})
     * @Rest\View(StatusCode = 200)
     * @IsGranted("ROLE_USER", message="not allowed")
     *
     * @ApiDoc\Areas({"certificationFolders", "default"})
     * @OA\Post (
     *     summary="Modifie le prix d'un lot de dossiers.",
     *     description="Procéder à la modification du prix de plusieurs dossiers. Via OAuth2, cet appel nécessite le scope 'certificationFolder:write'."
     * )
     * @OA\Response(
     *     response=200,
     *     description="Un json contenant les informations des dossiers qui sont programmés pour être modifiés et ceux qui ne le seront pas",
     *     @OA\MediaType(mediaType="application/json",
     *         @OA\Schema(ref="#/components/schemas/UpdateCertificationFoldersAsyncResult")
     *     )
     * )
     * @OA\RequestBody(
     *     @OA\MediaType(mediaType="application/json",
     *         @OA\Schema(ref="#/components/schemas/UpdateCertificationFoldersAsync")
     *     )
     * )
     *
     * @param string $processingMethod
     * @param CertificationFolderService $certificationFolderService
     * @param AccessService $accessService
     * @param Request $request
     * @return View|Response
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws Throwable
     */
    public function updateAsync(string $processingMethod, CertificationFolderService $certificationFolderService, AccessService $accessService, Request $request)
    {
        /* @var $user User */
        $user = $this->getUser();

        $body = json_decode($request->getContent(), true);
        foreach ($body['certificationFolders'] as &$certificationFolderData) {
            $certificationFolderData['amountHt'] = (float)($certificationFolderData['amountHt']);
        }

        $violations = $this->validateUpdatePriceAsyncBody($body);
        if (count($violations)) {
            return $this->view($violations, Response::HTTP_BAD_REQUEST);
        }
        $data = ["result" => ["success" => [], "failed" => []]];
        unset($certificationFolderData);

        foreach ($body['certificationFolders'] as $certificationFolderData) {
            $id = $certificationFolderData['id'];
            $certificationFolder = $certificationFolderService->getById($id);
            if ($certificationFolder && $accessService->hasCertificationFolderCertifierEdit($user, $certificationFolder)) {
                $certificationFolderUpdated = null;
                if ($processingMethod === ProcessingMethod::ASYNC()->getValue()) {
                    $certificationFolderUpdated = $certificationFolderService->updateAsync($certificationFolder, $certificationFolderData['amountHt']); // TODO set organism param
                } else if ($processingMethod === ProcessingMethod::BATCH()->getValue()) {
                    $certificationFolderUpdated = $certificationFolderService->update($certificationFolder, ['amountHt' => $certificationFolderData['amountHt']]); // TODO set organism param
                }
                if ($certificationFolderUpdated->getAmountHt() === $certificationFolderData['amountHt']) {
                    $data["result"]["success"][] = [
                        "id" => $id,
                        "amountHt" => $certificationFolderData['amountHt']
                    ];
                } else {
                    $data["result"]["failed"][] = ["id" => $id];
                }
            } else {
                $data["result"]["failed"][] = ["id" => $id];
            }
        }
        $view = $this->view($data, 200);
        return $this->handleView($view);
    }

    /**
     * @Rest\Post("/api/certificationFolders/{externalId}/toRegister", name="toRegister")
     * @IsGranted(CertificationFolderVoter::EDIT, subject="certificationFolder",  message="not allowed")
     * @Rest\View(StatusCode = 200)
     *
     * @ApiDoc\Areas({"certificationFolders", "default"})
     * @OA\Post (
     *     operationId="toRegister",
     *     summary="Déclare que le candidat a été enregistré pour passer l'examen.",
     *     description="Déclare que le candidat lié au dossier a été enregistré pour passer l'examen de certification. Via OAuth2, cet appel nécessite le scope 'certificationFolder:write'."
     * )
     * @OA\Response(
     *     response=200,
     *     description="Un json contenant les informations du dossier de certification",
     *     @OA\MediaType(mediaType="application/json",
     *         @OA\Schema(ref="#/components/schemas/CertificationFolder")
     *     )
     * )
     * @OA\Parameter(
     *     name="externalId",
     *     in="path",
     *     description="externalId du dossier",
     *     @OA\Schema(type="string")
     * )
     *
     * @param CertificationFolder $certificationFolder
     * @param CertificationFolderService $certificationFolderService
     * @return CertificationFolder
     * @throws NonUniqueResultException
     * @throws Throwable
     */
    public function toRegister(CertificationFolder $certificationFolder, CertificationFolderService $certificationFolderService): CertificationFolder
    {
        /* @var $user User */
        $user = $this->getUser();

        return $certificationFolderService->examToRegister($certificationFolder, $user);
    }

    /**
     * @Rest\Post("/api/certificationFolders/{externalId}/refuse", name="refuse")
     * @IsGranted(CertificationFolderVoter::EDIT, subject="certificationFolder",  message="not allowed")
     * @Rest\View(StatusCode = 200)
     *
     * @ApiDoc\Areas({"certificationFolders", "default"})
     * @OA\Post (
     *     operationId="refuse",
     *     summary="Le certificateur refuse le dossier de certification du candidat.",
     *     description="Le certificateur déclare ne pas accepter de faire passer l'examen au candidat. Via OAuth2, cet appel nécessite le scope 'certificationFolder:write'."
     * )
     * @OA\Response(
     *     response=200,
     *     description="Un json contenant les informations du dossier de certification refusé",
     *     @OA\MediaType(mediaType="application/json",
     *         @OA\Schema(ref="#/components/schemas/CertificationFolder")
     *     )
     * )
     *
     * @OA\Parameter(
     *     name="externalId",
     *     in="path",
     *     description="externalId du dossier",
     *     @OA\Schema(type="string")
     * )
     *
     *
     * @OA\RequestBody(
     *     @OA\MediaType(mediaType="application/json",
     *         @OA\Schema(
     *             @OA\Property(property="comment", type="string", description="Ajoute un commentaire aux commentaires déjà présents sur le dossier - facultatif.")
     *         )
     *     )
     * )
     *
     * @param CertificationFolder $certificationFolder
     * @param CertificationFolderService $certificationFolderService
     * @param Request $request
     * @return CertificationFolder|View
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws Throwable
     */
    public function refuse(CertificationFolder $certificationFolder, CertificationFolderService $certificationFolderService, Request $request)
    {
        /* @var $user User */
        $user = $this->getUser();
        $body = json_decode($request->getContent(), true);
        $body = $body ?? [];

        $violations = $this->validateRefuseBody($body);
        if (count($violations)) {
            return $this->view($violations, Response::HTTP_BAD_REQUEST);
        }
        return $certificationFolderService->examRefused($certificationFolder, $user, $body);
    }

    /**
     * @Rest\Post("/api/certificationFolders/{externalId}/register", name="register")
     * @IsGranted(CertificationFolderVoter::EDIT, subject="certificationFolder",  message="not allowed")
     * @Rest\View(StatusCode = 200)
     *
     * @ApiDoc\Areas({"certificationFolders", "default"})
     * @OA\Post (
     *     operationId="register",
     *     summary="Déclare que le candidat a été enregistré pour passer l'examen.",
     *     description="Déclare que le candidat lié au dossier a été enregistré pour passer l'examen de certification. Via OAuth2, cet appel nécessite le scope 'certificationFolder:write'."
     * )
     * @OA\Response(
     *     response=200,
     *     description="Un json contenant les informations du dossier de certification enregistré",
     *     @OA\MediaType(mediaType="application/json",
     *         @OA\Schema(ref="#/components/schemas/CertificationFolder")
     *     )
     * )
     *
     * @OA\Parameter(
     *     name="externalId",
     *     in="path",
     *     description="externalId du dossier",
     *     @OA\Schema(type="string")
     * )
     *
     * @OA\RequestBody(
     *     @OA\MediaType(mediaType="application/json",
     *         @OA\Schema(
     *             @OA\Property(property="examinationDate", type="datetime", description="Date de passage de l'examen - facultatif."),
     *             @OA\Property(property="examinationEndDate", type="datetime", description="Date de fin de passage de l'examen - facultatif."),
     *             @OA\Property(property="examinationPlace", type="string", description="Lieu de passage de l'examen - facultatif."),
     *             @OA\Property(property="examinationType", type="string", description="Type de passage de l'examen : A_DISTANCE, EN_PRESENTIEL, MIXTE - facultatif."),
     *             @OA\Property(property="enrollmentDate", type="datetime", description="Date d'inscription à la certification - facultatif."),
     *             @OA\Property(property="comment", type="string", description="Ajoute un commentaire aux commentaires déjà présents sur le dossier - facultatif.")
     *         )
     *     )
     * )
     *
     * @param CertificationFolder $certificationFolder
     * @param CertificationFolderService $certificationFolderService
     * @param Request $request
     * @return CertificationFolder|View
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws Throwable
     */
    public function registered(CertificationFolder $certificationFolder, CertificationFolderService $certificationFolderService, Request $request)
    {
        /* @var $user User */
        $user = $this->getUser();
        $body = json_decode($request->getContent(), true);
        $body = $body ?? [];

        if (!empty($body['examinationDate'])) {
            if (strtotime($body['examinationDate'])) {
                $body['examinationDate'] = (new DateTime($body['examinationDate']))->setTimezone(new DateTimeZone('UTC'));
            }
        }
        if (!empty($body['examinationEndDate'])) {
            if (strtotime($body['examinationEndDate'])) {
                $body['examinationEndDate'] = (new DateTime($body['examinationEndDate']))->setTimezone(new DateTimeZone('UTC'));
            }
            if (empty($body['examinationDate']) && !$certificationFolder->getExaminationDate()) {
                throw new WedofBadRequestHttpException("Erreur, veuillez remplir une date de début de passage d'examen (examinationDate) afin de modifier la date de fin de passage d'examen (examinationEndDate). ");
            }
        }

        if (!empty($body['enrollmentDate'])) {
            if (strtotime($body['enrollmentDate'])) {
                $body['enrollmentDate'] = (new DateTime($body['enrollmentDate']))->setTimezone(new DateTimeZone('Europe/Paris'));
            }
        }

        $violations = $this->validateRegisteredBody($body);
        if (count($violations)) {
            return $this->view($violations, Response::HTTP_BAD_REQUEST);
        }

        return $certificationFolderService->examRegistered($certificationFolder, $user, $body);
    }

    /**
     * @Rest\Post("/api/certificationFolders/{externalId}/take", name="take")
     * @IsGranted(CertificationFolderVoter::EDIT, subject="certificationFolder",  message="not allowed")
     * @Rest\View(StatusCode = 200)
     *
     * @ApiDoc\Areas({"certificationFolders", "default"})
     * @OA\Post (
     *     operationId="take",
     *     summary="Déclare que le candidat est prêt à passer l'examen.",
     *     description="Déclare que le candidat lié au dossier est prêt à passer l'examen de certification. Via OAuth2, cet appel nécessite le scope 'certificationFolder:write'."
     * )
     * @OA\Response(
     *     response=200,
     *     description="Un json contenant les informations du dossier de certification à passer.",
     *     @OA\MediaType(mediaType="application/json",
     *         @OA\Schema(ref="#/components/schemas/CertificationFolder")
     *     )
     * )
     *
     * @OA\Parameter(
     *     name="externalId",
     *     in="path",
     *     description="externalId du dossier",
     *     @OA\Schema(type="string")
     * )
     *
     * @OA\RequestBody(
     *     @OA\MediaType(mediaType="application/json",
     *         @OA\Schema(
     *             @OA\Property(property="examinationDate", type="datetime", description="Date de passage de l'examen - facultatif."),
     *             @OA\Property(property="examinationEndDate", type="datetime", description="Date de fin de passage de l'examen - facultatif."),
     *             @OA\Property(property="examinationPlace", type="string", description="Lieu de passage de l'examen - facultatif."),
     *             @OA\Property(property="examinationType", type="string", description="Type de passage de l'examen : A_DISTANCE, EN_PRESENTIEL, MIXTE - facultatif."),
     *             @OA\Property(property="enrollmentDate", type="datetime", description="Date d'inscription à la certification - facultatif."),
     *             @OA\Property(property="comment", type="string", description="Ajoute un commentaire aux commentaires déjà présents sur le dossier - facultatif."),
     *             @OA\Property(property="tiersTemps", type="boolean", description="Indique si le candidat a besoin d'un tiers temps - facultatif (par défault false).")
     *         )
     *     )
     * )
     *
     * @param CertificationFolder $certificationFolder
     * @param CertificationFolderService $certificationFolderService
     * @param Request $request
     * @return CertificationFolder|View
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws Throwable
     */
    public function toTake(CertificationFolder $certificationFolder, CertificationFolderService $certificationFolderService, Request $request)
    {
        /* @var $user User */
        $user = $this->getUser();
        $body = json_decode($request->getContent(), true);
        $body = $body ?? [];

        if (!empty($body['examinationDate'])) {
            if (strtotime($body['examinationDate'])) {
                $body['examinationDate'] = (new DateTime($body['examinationDate']))->setTimezone(new DateTimeZone('UTC'));
            }
        }
        if (!empty($body['examinationEndDate'])) {
            if (strtotime($body['examinationEndDate'])) {
                $body['examinationEndDate'] = (new DateTime($body['examinationEndDate']))->setTimezone(new DateTimeZone('UTC'));
            }
            if (!empty($body['examinationDate']) || $certificationFolder->getExaminationDate()) {
                $examinationStartDate = !empty($body['examinationDate']) ? $body['examinationDate'] : $certificationFolder->getExaminationDate();
                if ($examinationStartDate > $body['examinationEndDate']) {
                    throw new WedofBadRequestHttpException("Erreur, la date de début de passage d'examen (examinationDate) ne peut être ultèrieure à la date de fin de passage d'examen (examinationEndDate). ");
                }
            } else {
                throw new WedofBadRequestHttpException("Erreur, veuillez remplir une date de début de passage d'examen (examinationDate) afin de modifier la date de fin de passage d'examen (examinationEndDate). ");
            }
        }
        if (!empty($body['enrollmentDate'])) {
            if (strtotime($body['enrollmentDate'])) {
                $body['enrollmentDate'] = (new DateTime($body['enrollmentDate']))->setTimezone(new DateTimeZone('Europe/Paris'));
            }
        }

        $violations = $this->validateToTakeBody($body);
        if (count($violations)) {
            return $this->view($violations, Response::HTTP_BAD_REQUEST);
        }

        return $certificationFolderService->examToTake($certificationFolder, $user, $body);
    }

    /**
     * @Rest\Post("/api/certificationFolders/{externalId}/control", name="control")
     * @IsGranted(CertificationFolderVoter::EDIT, subject="certificationFolder",  message="not allowed")
     * @Rest\View(StatusCode = 200)
     *
     * @ApiDoc\Areas({"certificationFolders", "default"})
     * @OA\Post (
     *     operationId="control",
     *     summary="Demande la vérification de l'examen du candidat.",
     *     description="Demande au certificateur de statuer sur le résultat de l'examen du candidat. Via OAuth2, cet appel nécessite le scope 'certificationFolder:write'."
     * )
     * @OA\Response(
     *     response=200,
     *     description="Un json contenant les informations du dossier de certification à contrôler",
     *     @OA\MediaType(mediaType="application/json",
     *         @OA\Schema(ref="#/components/schemas/CertificationFolder")
     *     )
     * )
     *
     * @OA\Parameter(
     *     name="externalId",
     *     in="path",
     *     description="externalId du dossier",
     *     @OA\Schema(type="string")
     * )
     *
     * @OA\RequestBody(
     *     @OA\MediaType(mediaType="application/json",
     *         @OA\Schema(
     *             @OA\Property(property="examinationDate", type="datetime", description="Date de passage de l'examen - obligatoire si non renseigné lors des étapes 'register' ou 'take'."),
     *             @OA\Property(property="examinationEndDate", type="datetime", description="Date de fin de passage de l'examen - facultatif."),
     *             @OA\Property(property="examinationPlace", type="string", description="Lieu de passage de l'examen - facultatif."),
     *             @OA\Property(property="examinationType", type="string", description="Lieu de passage de l'examen - obligatoire."),
     *             @OA\Property(property="enrollmentDate", type="datetime", description="Date d'inscription à la certification - facultatif."),
     *             @OA\Property(property="comment", type="string", description="Ajoute un commentaire aux commentaires déjà présents sur le dossier - facultatif.")
     *         )
     *     )
     * )
     *
     * @param CertificationFolder $certificationFolder
     * @param CertificationFolderService $certificationFolderService
     * @param Request $request
     * @return CertificationFolder|View
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws Throwable
     */
    public function toControl(CertificationFolder $certificationFolder, CertificationFolderService $certificationFolderService, Request $request)
    {
        /* @var $user User */
        $user = $this->getUser();
        $body = json_decode($request->getContent(), true);
        $body = $body ?? [];

        if (!empty($body['examinationDate'])) {
            if (strtotime($body['examinationDate'])) {
                $body['examinationDate'] = (new DateTime($body['examinationDate']))->setTimezone(new DateTimeZone('UTC'));
            }
        }
        if (!empty($body['examinationEndDate'])) {
            if (strtotime($body['examinationEndDate'])) {
                $body['examinationEndDate'] = (new DateTime($body['examinationEndDate']))->setTimezone(new DateTimeZone('UTC'));
            }
            if (empty($body['examinationDate']) && !$certificationFolder->getExaminationDate()) {
                throw new WedofBadRequestHttpException("Erreur, veuillez remplir une date de début de passage d'examen (examinationDate) afin de modifier la date de fin de passage d'examen (examinationEndDate). ");
            }
        }
        if (!empty($body['enrollmentDate'])) {
            if (strtotime($body['enrollmentDate'])) {
                $body['enrollmentDate'] = (new DateTime($body['enrollmentDate']))->setTimezone(new DateTimeZone('Europe/Paris'));
            }
        }
        $violations = $this->validateToControlBody($body);
        if (count($violations)) {
            return $this->view($violations, Response::HTTP_BAD_REQUEST);
        }

        return $certificationFolderService->examToControl($certificationFolder, $user, $body);
    }

    /**
     * @Rest\Post("/api/certificationFolders/{externalId}/retake", name="retake")
     * @IsGranted(CertificationFolderVoter::EDIT, subject="certificationFolder",  message="not allowed")
     * @Rest\View(StatusCode = 200)
     *
     * @ApiDoc\Areas({"certificationFolders", "default"})
     * @OA\Post (
     *     operationId="retake",
     *     summary="Déclare que le candidat est prêt à repasser l'examen.",
     *     description="Déclare que le candidat lié au dossier est prêt à repasser l'examen de certification. Via OAuth2, cet appel nécessite le scope 'certificationFolder:write'."
     * )
     * @OA\Response(
     *     response=200,
     *     description="Un json contenant les informations du dossier de certification",
     *     @OA\MediaType(mediaType="application/json",
     *         @OA\Schema(ref="#/components/schemas/CertificationFolder")
     *     )
     * )
     *
     * @OA\Parameter(
     *     name="externalId",
     *     in="path",
     *     description="externalId du dossier",
     *     @OA\Schema(type="string")
     * )
     *
     * @OA\RequestBody(
     *     @OA\MediaType(mediaType="application/json",
     *         @OA\Schema(
     *             @OA\Property(property="detailedResult", type="string", description="Détail du résultat de l'examen - facultatif."),
     *             @OA\Property(property="europeanLanguageLevel", type="string", description="Nomenclature européeenne pour les certifications de langues : C2, C1, B2, B1, A2, A1, INSUFFISANT."),
     *             @OA\Property(property="examinationDate", type="datetime", description="Date de passage de l'examen - facultatif."),
     *             @OA\Property(property="examinationEndDate", type="datetime", description="Date de fin de passage de l'examen - facultatif."),
     *             @OA\Property(property="examinationPlace", type="string", description="Lieu de passage de l'examen - facultatif."),
     *             @OA\Property(property="examinationType", type="string", description="Type de passage de l'examen : A_DISTANCE, EN_PRESENTIEL, MIXTE - facultatif."),
     *             @OA\Property(property="comment", type="string", description="Ajoute un commentaire aux commentaires déjà présents sur le dossier - facultatif.")
     *         )
     *     )
     * )
     *
     * @param CertificationFolder $certificationFolder
     * @param CertificationFolderService $certificationFolderService
     * @param Request $request
     * @return CertificationFolder|View
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws Throwable
     */
    public function toRetake(CertificationFolder $certificationFolder, CertificationFolderService $certificationFolderService, Request $request)
    {
        /* @var $user User */
        $user = $this->getUser();
        $body = json_decode($request->getContent(), true);
        $body = $body ?? [];

        if (!empty($body['examinationDate'])) {
            if (strtotime($body['examinationDate'])) {
                $body['examinationDate'] = (new DateTime($body['examinationDate']))->setTimezone(new DateTimeZone('UTC'));
            }
        }
        if (!empty($body['examinationEndDate'])) {
            if (strtotime($body['examinationEndDate'])) {
                $body['examinationEndDate'] = (new DateTime($body['examinationEndDate']))->setTimezone(new DateTimeZone('UTC'));
            }
            if (empty($body['examinationDate']) && !$certificationFolder->getExaminationDate()) {
                throw new WedofBadRequestHttpException("Erreur, veuillez remplir une date de début de passage d'examen (examinationDate) afin de modifier la date de fin de passage d'examen (examinationEndDate). ");
            }
        }
        $violations = $this->validateToRetakeBody($body);
        if (count($violations)) {
            return $this->view($violations, Response::HTTP_BAD_REQUEST);
        }

        return $certificationFolderService->examToRetake($certificationFolder, $user, $body);
    }

    /**
     * @Rest\Post("/api/certificationFolders/{externalId}/fail", name="fail")
     * @IsGranted(CertificationFolderVoter::EDIT, subject="certificationFolder",  message="not allowed")
     * @Rest\View(StatusCode = 200)
     *
     * @ApiDoc\Areas({"certificationFolders", "default"})
     * @OA\Post (
     *     operationId="fail",
     *     summary="Déclare que le candidat a échoué à l'examen.",
     *     description="Déclare que le candidat lié au dossier a échoué à l'examen de certification. Via OAuth2, cet appel nécessite le scope 'certificationFolder:write'."
     * )
     * @OA\Response(
     *     response=200,
     *     description="Un json contenant les informations du dossier de certification",
     *     @OA\MediaType(mediaType="application/json",
     *         @OA\Schema(ref="#/components/schemas/CertificationFolder")
     *     )
     * )
     *
     * @OA\Parameter(
     *     name="externalId",
     *     in="path",
     *     description="externalId du dossier",
     *     @OA\Schema(type="string")
     * )
     *
     * @OA\RequestBody(
     *     @OA\MediaType(mediaType="application/json",
     *         @OA\Schema(
     *             @OA\Property(property="detailedResult", type="string", description="Détail du résultat de l'examen - facultatif."),
     *             @OA\Property(property="comment", type="string", description="Ajoute un commentaire aux commentaires déjà présents sur le dossier - facultatif."),
     *             @OA\Property(property="europeanLanguageLevel", type="string", description="Nomenclature européeenne pour les certifications de langues : C2, C1, B2, B1, A2, A1, INSUFFISANT.")
     *         )
     *     )
     * )
     *
     * @param CertificationFolder $certificationFolder
     * @param CertificationFolderService $certificationFolderService
     * @param Request $request
     * @return CertificationFolder|View
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws Throwable
     */
    public function fail(CertificationFolder $certificationFolder, CertificationFolderService $certificationFolderService, Request $request)
    {
        /* @var $user User */
        $user = $this->getUser();
        $body = json_decode($request->getContent(), true);
        $body = $body ?? [];

        $violations = $this->validateFailBody($body);
        if (count($violations)) {
            return $this->view($violations, Response::HTTP_BAD_REQUEST);
        }

        return $certificationFolderService->examFailed($certificationFolder, $user, $body);
    }

    /**
     * @Rest\Post("/api/certificationFolders/{externalId}/success", name="success")
     * @IsGranted(CertificationFolderVoter::EDIT, subject="certificationFolder",  message="not allowed")
     * @Rest\View(StatusCode = 200)
     *
     * @ApiDoc\Areas({"certificationFolders", "default"})
     * @OA\Post (
     *     operationId="success",
     *     summary="Déclare que le candidat a réussi l'examen.",
     *     description="Déclare que le candidat lié au dossier a réussi l'examen de certification. Via OAuth2, cet appel nécessite le scope 'certificationFolder:write'."
     * )
     * @OA\Response(
     *     response=200,
     *     description="Un json contenant les informations du dossier de certification",
     *     @OA\MediaType(mediaType="application/json",
     *         @OA\Schema(ref="#/components/schemas/CertificationFolder")
     *     )
     * )
     *
     * @OA\Parameter(
     *     name="externalId",
     *     in="path",
     *     description="externalId du dossier",
     *     @OA\Schema(type="string")
     * )
     *
     * @OA\RequestBody(
     *     @OA\MediaType(mediaType="application/json",
     *         @OA\Schema(
     *             @OA\Property(property="detailedResult", type="string", description="Détail du résultat de l'examen - facultatif."),
     *             @OA\Property(property="issueDate", type="datetime", description="Date d'obtention de la certification - obligatoire."),
     *             @OA\Property(property="digitalProofLink", type="string", description="Lien vers la preuve numérique de l'obtention de la certification - facultatif."),
     *             @OA\Property(property="comment", type="string", description="Ajoute un commentaire aux commentaires déjà présents sur le dossier - facultatif."),
     *             @OA\Property(property="gradePass", type="string", description="Ajoute une mention au dossier de certification : SANS_MENTION, MENTION_ASSEZ_BIEN, MENTION_BIEN, MENTION_TRES_BIEN, MENTION_TRES_BIEN_AVEC_FELICITATIONS_DU_JURY - facultatif."),
     *             @OA\Property(property="europeanLanguageLevel", type="string", description="Nomenclature européeenne pour les certifications de langues : C2, C1, B2, B1, A2, A1, INSUFFISANT."),
     *             @OA\Property(property="badgeAssertion", type="string", description="Lien vers le badge de la certification - facultatif.")
     *         )
     *     )
     * )
     *
     * @param CertificationFolder $certificationFolder
     * @param CertificationFolderService $certificationFolderService
     * @return CertificationFolder|View
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws Throwable
     */
    public function success(CertificationFolder $certificationFolder, CertificationFolderService $certificationFolderService)
    {
        /* @var $user User */
        $user = $this->getUser();
        $body = $this->getData();

        if (empty($body['issueDate'])) {
            throw  new WedofBadRequestHttpException("Erreur, la date de délivrance (issueDate) est obligatoire");
        }
        if (strtotime($body['issueDate'])) {
            $body['issueDate'] = (new DateTime($body['issueDate']))->setTimezone(new DateTimeZone('Europe/Paris'));
        }
        $violations = $this->validateSuccessBody($body);
        if (count($violations)) {
            return $this->view($violations, Response::HTTP_BAD_REQUEST);
        }

        if (!empty($body['badgeAssertion'])) {
            $isValidUrl = filter_var($body['badgeAssertion'], FILTER_VALIDATE_URL);
            if (!$isValidUrl) {
                throw new WedofBadRequestHttpException("Erreur, le champ 'badgeAssertion' n'est pas une URL valide.");
            }
            if (!$certificationFolderService->verifyBadgeAssertion($body['badgeAssertion'])) {
                throw new WedofBadRequestHttpException("Erreur, le lien vers le badge de la certification n'est pas une assertion ou ne contient pas l'image du badge ou est expiré.");
            }
        } else {
            unset($body['badgeAssertion']);  // we don't have to save value if it is : ""
        }

        return $certificationFolderService->examSuccess($certificationFolder, $user, $body);
    }

    /**
     * @Rest\Post("/api/certificationFolders/{externalId}/abort", name="abort")
     * @IsGranted(CertificationFolderVoter::EDIT, subject="certificationFolder",  message="not allowed")
     * @Rest\View(StatusCode = 200)
     *
     * @ApiDoc\Areas({"certificationFolders", "default"})
     * @OA\Post (
     *     operationId="abort",
     *     summary="Déclare que le candidat abandonne l'examen.",
     *     description="Déclare que le candidat lié au dossier a abandonné l'examen de certification. Via OAuth2, cet appel nécessite le scope 'certificationFolder:write'."
     * )
     * @OA\Response(
     *     response=200,
     *     description="Un json contenant les informations du dossier de certification",
     *     @OA\MediaType(mediaType="application/json",
     *         @OA\Schema(ref="#/components/schemas/CertificationFolder")
     *     )
     * )
     *
     * @OA\Parameter(
     *     name="externalId",
     *     in="path",
     *     description="externalId du dossier",
     *     @OA\Schema(type="string")
     * )
     *
     * @OA\RequestBody(
     *     @OA\MediaType(mediaType="application/json",
     *         @OA\Schema(
     *             @OA\Property(property="comment", type="string", description="Ajoute un commentaire aux commentaires déjà présents sur le dossier - facultatif.")
     *         )
     *     )
     * )
     *
     * @param CertificationFolder $certificationFolder
     * @param CertificationFolderService $certificationFolderService
     * @param Request $request
     * @return CertificationFolder|View
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws Throwable
     */
    public function abort(CertificationFolder $certificationFolder, CertificationFolderService $certificationFolderService, Request $request)
    {
        /* @var $user User */
        $user = $this->getUser();
        $body = json_decode($request->getContent(), true);
        $body = $body ?? [];

        $violations = $this->validateAbortBody($body);
        if (count($violations)) {
            return $this->view($violations, Response::HTTP_BAD_REQUEST);
        }

        return $certificationFolderService->examAborted($certificationFolder, $user, $body);
    }

    /**
     * @Rest\Post("/app/certificationFolders/createFromRegistrationFolders/{siret}", requirements={"siret"="\d{14}"})
     * @Rest\View(StatusCode = 200)
     * @Security("is_granted('ROLE_ADMIN')", message="not allowed")
     *
     * @param CertificationFolderService $certificationFolderService
     * @param string $siret
     * @param OrganismService $organismService
     * @return Response
     * @throws Throwable
     * @throws WedofSubscriptionException
     */
    public function createFromRegistrationFolders(CertificationFolderService $certificationFolderService, string $siret, OrganismService $organismService): Response
    {
        // Méthode admin pour recréer tous les CFs manquants sur un certificateur donné en paramètre
        // Cela permet de rattraper un décalage dans les données historiques du fait que la création RF / CF n'est pas transactionnelle et peut foirer en cours de route
        $certifier = $organismService->getBySiret($siret);
        if (!$certifier) {
            throw new WedofBadRequestHttpException("Erreur, l'organisme avec le siret " . $siret . " n'existe pas.");
        }
        if (!$certifier->getSubscription()->isAllowCertifiers()) {
            throw new WedofSubscriptionException("Erreur, l'abonnement de l'organisme ne permet pas la creation automatique des dossiers de certification.");
        }
        $certificationFolderService->createFromRegistrationFolders($certifier);
        return new Response('Les tâches RabbitMQ ont été lancées', 200);
    }

    /**
     * @Rest\Post("/app/certificationFolders/{externalId}/refreshFromRegistrationFolder")
     * @Rest\View(StatusCode = 200)
     * @Security("is_granted('ROLE_ADMIN')", message="not allowed")
     *
     * @param CertificationFolder $certificationFolder
     * @param CertificationFolderService $certificationFolderService
     * @return Response
     * @throws Throwable
     */
    public function refreshFromRegistrationFolder(CertificationFolder $certificationFolder, CertificationFolderService $certificationFolderService): Response
    {
        $registrationFolder = $certificationFolder->getRegistrationFolder();
        if ($registrationFolder) {
            $certificationFolderService->updateFromRegistrationFolder($certificationFolder, $registrationFolder);
            return new Response('Rafraîchissement bien lancé', 200);
        } else {
            return new Response('RF pas trouvé', 404);
        }
    }

    /**
     * @Rest\Post("/api/certificationFolders/updateFromSpreadsheet/files")
     * @Rest\View(StatusCode = 200)
     * @IsGranted("ROLE_USER", message="not allowed")
     *
     * @param Request $request
     * @param CertificationFolderService $certificationFolderService
     * @param RegistrationFolderService $registrationFolderService
     * @return JsonResponse
     *
     * @throws Throwable
     * @throws WedofSubscriptionException
     */
    public function updateFromSpreadsheet(Request $request, CertificationFolderService $certificationFolderService, RegistrationFolderService $registrationFolderService): JsonResponse
    {
        set_time_limit(300); // by default it's 30 second
        $raiseEvents = filter_var($request->get('raiseEvents', false), FILTER_VALIDATE_BOOLEAN);
        if (!$raiseEvents) {
            $_SERVER['NO_DISPATCH_WEDOF_EVENTS'] = true;
        }
        /* @var $user User */
        $user = $this->getUser();
        $organism = $user->getMainOrganism();
        $subscription = $organism->getSubscription();
        if (!$subscription->isAllowCertifiers()) {
            throw new WedofSubscriptionException("Erreur, cette fonctionnalité est limitée aux abonnements Certificateur");
        }
        /** @var UploadedFile $file */
        $file = !empty($request->files->all()) ? $request->files->get('file') : $request->get('file');
        $fileContent = $file->getContent();
        if (!$fileContent) {
            throw new WedofBadRequestHttpException("Erreur, le fichier est vide.");
        }
        $fileExtension = $file->getClientOriginalExtension();
        $CSV_DELIMITER = ';';

        if (in_array($fileExtension, ['xls', "xlsx"])) {
            $reader = $fileExtension === 'xls' ? new Xls() : new XlsxReader();
            $worksheetList = $reader->listWorksheetNames($file->getPathname());
            $reader->setLoadSheetsOnly($worksheetList[0]);
            $spreadsheet = $reader->load($file->getPathname());
            self::applyFormatOnDateColumnsXlsx($spreadsheet->getSheet(0));
            $writer = new CsvWriter($spreadsheet);
            $writer->setDelimiter($CSV_DELIMITER);
            $writer->save($file->getPathname());
        } else if ($fileExtension != 'csv') {
            throw new WedofBadRequestHttpException("Erreur, le format de fichier soumis n'est pas supporté : " . $fileExtension);
        }

        $results = [
            'ok' => [],
            'ko' => []
        ];

        if (($filePointer = fopen($file->getPathname(), "r")) !== false) {
            // remove byte order mark (BOM)
            $headers = preg_replace('/[\x00-\x1F\x80-\xFF]/', '', fgetcsv($filePointer, 0, $CSV_DELIMITER));
            if ($headers === false) {
                throw new WedofBadRequestHttpException("Erreur, impossible de lire la première ligne");
            }
            $headersMapping = array_flip($headers);
            while (($row = fgetcsv($filePointer, 0, $CSV_DELIMITER)) !== false && join('', $row) !== '') {
                try {
                    $certificationFolderExternalId = $this->getValueFromSpreadSheetColumn($row, $headersMapping, 'externalId');
                    $certificationFolderId = $this->getValueFromSpreadSheetColumn($row, $headersMapping, 'id');
                    $registrationFolderExternalId = $this->getValueFromSpreadSheetColumn($row, $headersMapping, 'idCPF');
                    if ($certificationFolderExternalId) {
                        $certificationFolder = $certificationFolderService->getByExternalId($certificationFolderExternalId);
                        if (!$certificationFolder) {
                            throw new Exception("Erreur, il n'y a pas de dossier de certification associé à l'externalId " . $certificationFolderExternalId);
                        }
                        if (!$certificationFolder->getCertification()->isCertifier($organism)) {
                            throw new Exception("Erreur, vous n'êtes pas certificateur du dossier de certification associé à l'externalId " . $certificationFolderExternalId);
                        }
                    } else if ($certificationFolderId) {
                        $certificationFolder = $certificationFolderService->getById($certificationFolderId);
                        if (!$certificationFolder) {
                            throw new Exception("Erreur, il n'y a pas de dossier de certification associé à l'id " . $certificationFolderId);
                        }
                        if (!$certificationFolder->getCertification()->isCertifier($organism)) {
                            throw new Exception("Erreur, vous n'êtes pas certificateur du dossier de certification associé à l'id " . $certificationFolderId);
                        }
                    } else if ($registrationFolderExternalId) {
                        $registrationFolder = $registrationFolderService->getByExternalId($registrationFolderExternalId);
                        if (!$registrationFolder) {
                            throw new Exception("Erreur, aucun dossier de formation n'a été trouvé pour le numéro " . $registrationFolderExternalId);
                        }
                        $certificationFolder = $registrationFolder->getCertificationFolder();
                        if (!$certificationFolder) {
                            throw new Exception("Erreur, il n'y a pas de dossier de certification associé au dossier de formation numéro " . $registrationFolderExternalId);
                        }
                        if (!$certificationFolder->getCertification()->isCertifier($organism)) {
                            throw new Exception("Erreur, vous n'êtes pas certificateur du dossier de certification associé au dossier de formation numéro " . $registrationFolderExternalId);
                        }
                    } else {
                        throw new Exception("Erreur, l'id, l'externalId ou l'idCpf est obligatoire pour identifier le dossier");
                    }

                    $targetStateString = $this->getValueFromSpreadSheetColumn($row, $headersMapping, 'state');
                    if (!$targetStateString) {
                        throw new Exception("Erreur, l'état du dossier de certification est obligatoire");
                    }
                    $targetState = CertificationFolderStates::fromFrString($targetStateString);
                    if (!$targetState) {
                        throw new Exception("Erreur, l'état suivant est inconnu " . $targetStateString);
                    }
                    $certificationFolderData = $this->getDataFromSpreadsheetRow($row, $headersMapping);
                    // Don't overrride fields if currently exported or processedOk (we could also throw error ?)
                    $preserveExistingData = in_array($certificationFolder->getCdcState(), [CertificationFolderCdcStates::EXPORTED(), CertificationFolderCdcStates::PROCESSED_OK()]);
                    $options = ['preserveExistingData' => $preserveExistingData];
                    $certificationFolder = $certificationFolderService->forceUpdate($certificationFolder, $certificationFolderData, $targetState, $user, $options);
                    if ($certificationFolder->getState() === $targetState->getValue()) {
                        $results['ok'][] = $certificationFolder->getExternalId();
                    } else {
                        throw new Exception("Erreur inconnue, le dossier n'est pas à l'état cible " . $targetState->getValue() . ' comme demandé mais à l\'état ' . $certificationFolder->getState());
                    }
                } catch (Throwable $t) {
                    $koEntry = [];
                    $koEntry[isset($certificationFolder) ? $certificationFolder->getExternalId() : ''] = $t->getMessage() . (isset($certificationFolder) ? ' - Etat atteint : ' . $certificationFolder->getState() : '');
                    $results['ko'][] = $koEntry;
                }
            }
            fclose($filePointer);
        }
        return new JsonResponse($results);
    }

    /**
     * @Rest\Post("/api/certificationFolders/{externalId}/files")
     * @Rest\Post("/app/attendees/certificationFolders/{externalId}/files")
     * @Security("is_granted('edit', certificationFolder) or is_granted('attendeeEdit', certificationFolder)", message="not allowed")
     * @Rest\View(StatusCode = 200)
     *
     * @ApiDoc\Areas({"certificationFolders", "default"})
     *
     * @OA\Post (
     *      summary="Envoyer un fichier.",
     *      description="Permet d'envoyer un fichier pour un dossier. Via OAuth2, cet appel nécessite le scope 'certificationFolder:write'."
     *  )
     * @OA\Response(
     *      response=200,
     *      description="Un json contenant la liste des documents au format : ",
     *      @OA\MediaType(mediaType="application/json",
     *          @OA\Schema(ref="#/components/schemas/UploadFileResult")
     *      )
     *  )
     * @OA\Parameter(
     *     name="externalId",
     *     in="path",
     *     description="externalId du dossier",
     *     @OA\Schema(type="string")
     * )
     * @OA\RequestBody(
     *      @OA\MediaType(mediaType="multipart/form-data",
     *          @OA\Schema(ref="#/components/schemas/UploadFile")
     *      )
     *  )
     *
     * @param CertificationFolder $certificationFolder
     * @param Request $request
     * @param CertificationFolderFileService $certificationFolderFileService
     * @return CertificationFolderFile[]|Collection
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws Throwable
     */
    public function uploadFile(CertificationFolder $certificationFolder, Request $request, CertificationFolderFileService $certificationFolderFileService): Collection
    {
        $typeId = $request->get('typeId');
        $title = $request->get('title');
        $url = $request->get('fileToDownload');
        if ($url) {
            $isValidUrl = filter_var($url, FILTER_VALIDATE_URL);
            if (!$isValidUrl) {
                throw new WedofBadRequestHttpException("Erreur, le champ 'fileToDownload' n'est pas une URL valide.");
            }
            $fullFileName = pathinfo($url, PATHINFO_FILENAME) . '.' . pathinfo($url, PATHINFO_EXTENSION);
            $newFile = sys_get_temp_dir() . '/' . $fullFileName;
            copy($url, $newFile);
            $mimeType = mime_content_type($newFile);
            $file = new UploadedFile($newFile, $fullFileName, $mimeType, null, true);
        } else {
            $file = !empty($request->files->all()) ? $request->files->get('file') : $request->get('file');
        }

        $certification = $certificationFolder->getCertification();
        $fileType = $this->getFileType($certification, $typeId);
        $fileExtensions = array_key_exists('accept', $fileType) ? explode(',', $fileType['accept']) : ['.*'];
        if (!in_array('.*', $fileExtensions)) {
            if (is_file($file)) {
                $mimeTypes = Tools::fileExtensionsToMimeTypes($fileExtensions);
                $violations = $this->validateFile($file, $mimeTypes);
                if (count($violations)) {
                    throw new WedofBadRequestHttpException("Un document de type " . implode($fileExtensions) . " est attendu.");
                }
            } else if (!in_array('link', $fileExtensions)) {
                throw new WedofBadRequestHttpException("Un document de type " . implode($fileExtensions) . " est attendu.");
            }
        }
        if (!empty($fileType) && !empty($file)) {
            $user = $this->getUser();
            if (!($user instanceof User)) {
                $user = null;
            }
            $roles = $this->getRolesForAuthenticatedUser($certificationFolder, $user);
            if ($roles['owner'] || $roles['attendee'] || $roles['partner']) { //we double check in the service but we need same information later in the method
                $isGenerated = $this->isGranted('ROLE_ADMIN') && !empty($fileType['generated']);
                $certificationFolderFileService->create($file, intval($typeId), $certificationFolder, $isGenerated, $isGenerated ? null : $user, $roles['attendee'], $title);
            } else {
                throw new WedofBadRequestHttpException("Erreur, vous ne pouvez pas déposer ce fichier.");
            }
            $certificationFolderFiles = $certificationFolder->getFiles();
            $filteredCertificationFolderFiles = new ArrayCollection();
            foreach ($certificationFolderFiles as $certificationFolderFile) {
                $fileType = $this->getFileType($certification, $certificationFolderFile->getTypeId());
                if (!empty($fileType)) {
                    if ($roles['attendee'] && $fileType['allowVisibilityAttendee']) { //Attendee visibility
                        $filteredCertificationFolderFiles->add($certificationFolderFile);
                    } else if (!$roles['attendee'] && $roles['partner'] && $fileType['allowVisibilityPartner']) { //Partner visibility
                        $filteredCertificationFolderFiles->add($certificationFolderFile);
                    } else if ($roles['owner']) { //Default (Owner / Certifier)
                        $filteredCertificationFolderFiles->add($certificationFolderFile);
                    }
                }
            }
            return $filteredCertificationFolderFiles;
        } else {
            throw new WedofBadRequestHttpException("Erreur, les attributs 'typeId' et 'file' sont obligatoires");
        }
    }


    /**
     * @Rest\Get("/api/certificationFolders/{externalId}/files/{certificationFolderFileId}")
     * @Rest\Get("/app/attendees/certificationFolders/{externalId}/files/{certificationFolderFileId}")
     * @Security("is_granted('view', certificationFolder) or is_granted('attendeeView', certificationFolder)", message="not allowed")
     * @Rest\View(StatusCode = 200)
     *
     * @ApiDoc\Areas({"certificationFolders", "default"})
     * @OA\Get (
     *       summary="Télécharger un document",
     *       description="Télécharger le document d'un dossier de certification à l'aide de son externalId et de l'id du document."
     *   )
     * @OA\Response(
     *       response=200,
     *       description="Document à télécharger.",
     *   )
     * @OA\Parameter(
     *      name="externalId",
     *      in="path",
     *      description="externalId du dossier",
     *      @OA\Schema(type="string")
     *  )
     * @OA\Parameter(
     *       name="certificationFolderFileId",
     *       in="path",
     *       description="id du document",
     *       @OA\Schema(type="string")
     *   )
     *
     * @param Request $request
     * @param CertificationFolder $certificationFolder
     * @param CertificationFolderFileService $certificationFolderFileService
     * @param CertificationFolderFileRepository $repository
     * @return array|string[]|StreamedResponse
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws Throwable
     * @throws WedofConnectionException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws ErrorException
     * @throws ClientExceptionInterface
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws \Symfony\Contracts\HttpClient\Exception\TransportExceptionInterface
     */
    public function downloadFile(Request $request, CertificationFolder $certificationFolder, CertificationFolderFileService $certificationFolderFileService, CertificationFolderFileRepository $repository)
    {
        $certificationFolderFileId = $request->get('certificationFolderFileId');
        $certificationFolderFile = $repository->findOneBy(['certificationFolder' => $certificationFolder, 'id' => $certificationFolderFileId]);
        if (!$certificationFolderFile) {
            throw new WedofNotFoundHttpException("Aucun fichier d'id $certificationFolderFileId n'existe pour le dossier.");
        }
        $fileTypes = $certificationFolder->getCertification()->getCertificationFolderFileTypes($certificationFolder);
        $fileTypeIndex = array_search($certificationFolderFile->getTypeId(), array_column($fileTypes, 'id'));
        if ($fileTypeIndex !== false) {
            $fileType = $fileTypes[$fileTypeIndex];
        } else {
            $fileType = array_merge(Certification::getDefaultCertificationFolderFileTypeRightsForAttendee(), Certification::getDefaultCertificationFolderFileTypeRightsForPartner());
        }
        $user = $this->getUser();
        if (!($user instanceof User)) {
            $user = null;
        }
        $roles = $this->getRolesForAuthenticatedUser($certificationFolder, $user);
        if ($roles['owner'] //owner / certifier
            || ($fileType['allowVisibilityAttendee'] && $roles['attendee']) //attendee
            || ($fileType['allowVisibilityPartner'] && $roles['partner'])) { //partner
            return $certificationFolderFileService->download($certificationFolderFile, $this->getUser());
        } else {
            throw new WedofNotFoundHttpException();
        }
    }

    /**
     * @Rest\Delete("/api/certificationFolders/{externalId}/files/{certificationFolderFileId}")
     * @Rest\Delete("/app/attendees/certificationFolders/{externalId}/files/{certificationFolderFileId}")
     * @Security("is_granted('edit', certificationFolder) or is_granted('attendeeEdit', certificationFolder) or is_granted('ROLE_ADMIN')", message="not allowed")
     * @Rest\View(StatusCode = 204)
     *
     * @param CertificationFolder $certificationFolder
     * @param CertificationFolderFileRepository $repository
     * @param Request $request
     * @param CertificationFolderFileService $certificationFolderFileService
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws Throwable
     */
    public function deleteFile(CertificationFolder $certificationFolder, CertificationFolderFileRepository $repository, Request $request, CertificationFolderFileService $certificationFolderFileService)
    {
        $certificationFolderFileId = $request->get('certificationFolderFileId');
        $certificationFolderFile = $repository->findOneBy(['certificationFolder' => $certificationFolder, 'id' => $certificationFolderFileId]);
        if ($certificationFolderFile) {
            $user = $this->getUser();
            if (!($user instanceof User)) {
                $user = null;
            }
            $roles = $this->getRolesForAuthenticatedUser($certificationFolder, $user);
            $fileState = $certificationFolderFile->getState();
            if ($roles['owner'] || (($roles['partner'] || $roles['attendee']) && $fileState !== FileStates::VALID()->getValue())) {
                if ($this->isGranted('ROLE_ADMIN') ||
                    ($certificationFolderFile->getTypeId() !== Certification::CERTIFICATE_FILE_TYPE_ID ||
                        ($certificationFolderFile->getTypeId() === Certification::CERTIFICATE_FILE_TYPE_ID && $certificationFolder->getState() !== CertificationFolderStates::SUCCESS()->getValue())
                    ) || !$certificationFolder->getCertification()->isAllowGenerateCertificate()) {
                    $bypassChecksAndActivity = $this->isGranted('ROLE_ADMIN');
                    $certificationFolderFileService->delete($certificationFolderFile, $user, $bypassChecksAndActivity);
                } else {
                    throw new WedofBadRequestHttpException("Il n'est pas possible de supprimer un parchemin généré automatiquement.");
                }
            } else {
                throw new WedofBadRequestHttpException("Erreur, vous n'avez pas les droits pour supprimer le fichier");
            }
        } else {
            throw new WedofNotFoundHttpException();
        }
    }

    /**
     * @Rest\Put("/api/certificationFolders/{externalId}/files/{certificationFolderFileId}")
     * @IsGranted(CertificationFolderVoter::EDIT, subject="certificationFolder", message="not allowed")
     * @Rest\View(StatusCode = 200)
     *
     * @param CertificationFolder $certificationFolder
     * @param CertificationFolderFileRepository $repository
     * @param Request $request
     * @param CertificationFolderFileService $certificationFolderFileService
     * @return CertificationFolderFile|View
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws Throwable
     */
    public function updateFile(CertificationFolder $certificationFolder, CertificationFolderFileRepository $repository, Request $request, CertificationFolderFileService $certificationFolderFileService)
    {
        $certificationFolderFileId = $request->get('certificationFolderFileId');
        $certificationFolderFile = $repository->findOneBy(['certificationFolder' => $certificationFolder, 'id' => $certificationFolderFileId]);
        if ($certificationFolderFile) {
            /* @var $user User */
            $user = $this->getUser();
            $roles = $this->getRolesForAuthenticatedUser($certificationFolder, $user);
            if ($roles['owner']) {
                $body = json_decode($request->getContent(), true);
                $violations = $this->validateUpdateFileBody($body);
                if (count($violations)) {
                    return $this->view($violations, Response::HTTP_BAD_REQUEST);
                }
                return $certificationFolderFileService->updateState($certificationFolderFile, $body, $user);
            } else {
                throw new WedofBadRequestHttpException("Erreur, vous n'avez pas les droits pour modifier l'état du document");
            }
        } else {
            throw new WedofBadRequestHttpException("Erreur, le document n'a pas été trouvé");
        }
    }

    /**
     * @Rest\Get("/api/certificationFolders/{externalId}/files")
     * @Security("is_granted('view', certificationFolder) or is_granted('attendeeView', certificationFolder)", message="not allowed")
     * @Rest\View(StatusCode = 200)
     *
     * @ApiDoc\Areas({"certificationFolders", "default"})
     * @OA\Get (
     *       summary="Liste des documents",
     *       description="La liste retourne les documents attendus et déjà renseignés."
     *   )
     * @OA\Response(
     *       response=200,
     *       description="Un json contenant les informations des documents du dossier de certification",
     *       @OA\MediaType(mediaType="application/json",
     *             @OA\Schema(ref="#/components/schemas/CertificationRegistrationFolderFile")
     *           )
     *   )
     * @OA\Parameter(
     *     name="externalId",
     *     in="path",
     *     description="externalId du dossier",
     *     @OA\Schema(type="string")
     * )
     * @param CertificationFolder $certificationFolder
     * @return array
     */
    public function listAllCertificationFoldersFile(CertificationFolder $certificationFolder): array
    {
        $user = $this->getUser();
        if (!($user instanceof User)) {
            $user = null;
        }

        $roles = $this->getRolesForAuthenticatedUser($certificationFolder, $user);
        $certificationFolderFileTypes = $certificationFolder->getCertification()->getCertificationFolderFileTypes($certificationFolder);

        if (($roles['owner'] || $roles['attendee'] || $roles['partner']) && $certificationFolderFileTypes) {
            $certificationFolderFiles = $certificationFolder->getFiles();
            $listAllCertificationFolderFile = [];
            foreach ($certificationFolderFileTypes as $certificationFolderFileType) {
                $found = false;
                if ($roles['owner'] || ($certificationFolderFileType['allowVisibilityAttendee'] && $roles['attendee']) || ($certificationFolderFileType['allowVisibilityPartner'] && $roles['partner'])) {
                    foreach ($certificationFolderFiles as $certificationFolderFile) {
                        if ($certificationFolderFileType['id'] == $certificationFolderFile->getTypeId()) {
                            $found = true;
                            $listAllCertificationFolderFile[] = [
                                "name" => $certificationFolderFileType['name'],
                                "fileName" => $certificationFolderFile->getFileName(),
                                "typeId" => $certificationFolderFileType['id'],
                                "state" => $certificationFolderFile->getState(),
                                "link" => $certificationFolderFile->getLink() ?? $certificationFolderFile->getFilePath() ?? null,
                                "requiredToState" => $certificationFolderFileType['toState'] ?? false,
                                "required" => $certificationFolderFileType['toState'] != null,
                                "allowUpload" => $roles['owner'] || $certificationFolderFile->getState() !== FileStates::VALID()->getValue() &&
                                    (($certificationFolderFileType['allowUploadAttendee'] && $roles['attendee']) || ($certificationFolderFileType['allowVisibilityPartner'] && $roles['partner'])),
                                "type" => $certificationFolderFileType['accept'],
                                "signedState" => $certificationFolderFile->getSignedState()
                            ];
                        }
                    }
                    $FREE_FILE_TYPE_NAME = "Document libre";
                    if (!$found || $certificationFolderFileType['name'] === $FREE_FILE_TYPE_NAME) {
                        if ($roles['owner']) {
                            $canUpload = true;
                        } else {
                            $canUpload = $roles['attendee'] ? $certificationFolderFileType['allowUploadAttendee'] : $certificationFolderFileType['allowUploadPartner'];
                        }
                        $listAllCertificationFolderFile[] = [
                            "name" => $certificationFolderFileType['name'],
                            "typeId" => $certificationFolderFileType['id'],
                            "state" => FileStates::toFrStringActivity(FileStates::NOT_SUBMITTED()->getValue()),
                            "requiredToState" => $certificationFolderFileType['toState'] ?? false,
                            "required" => $certificationFolderFileType['toState'] != null,
                            "allowUpload" => $canUpload,
                            "type" => $certificationFolderFileType['accept']
                        ];
                    }
                }
            }
            foreach ($certificationFolderFiles as $certificationFolderFile) {
                $registrationFileTypeIds = array_column($certificationFolderFileTypes, 'id');
                $typeIdExist = in_array($certificationFolderFile->getTypeId(), $registrationFileTypeIds);
                if (!$typeIdExist) {
                    $listAllCertificationFolderFile[] = [
                        "name" => 'Aucun',
                        "fileName" => $certificationFolderFile->getFileName(),
                        "typeId" => $certificationFolderFile->getTypeId(),
                        "state" => $certificationFolderFile->getState(),
                        "link" => $certificationFolderFile->getLink() ?? $certificationFolderFile->getFilePath() ?? null,
                        "requiredToState" => false,
                        "required" => false,
                        "allowUpload" => !(!$roles['owner'] && $certificationFolderFile->getState() === FileStates::VALID()->getValue()),
                        "signedState" => $certificationFolderFile->getSignedState()
                    ];
                }
            }
            return $listAllCertificationFolderFile;
        } else {
            throw new WedofNotFoundHttpException();
        }
    }

    /**
     * @Rest\Get("/app/certificationFolders/kanban/columnConfigs")
     * @Security("is_granted('ROLE_USER')", message="not allowed")
     * @Rest\View(StatusCode = 200)
     *
     * @param CertificationFolderRepository $certificationFolderRepository
     * @return array
     */
    public function listColumnConfigs(CertificationFolderRepository $certificationFolderRepository): array
    {
        return $certificationFolderRepository->listColumnConfigs();
    }

    /**
     * @Rest\Delete("/api/certificationFolders/{externalId}")
     * @Rest\View(StatusCode = 204)
     * @Security("is_granted('ROLE_ADMIN') or is_granted('ROLE_ALLOWED_TO_SWITCH') or is_granted('IS_IMPERSONATOR')", message="not allowed")
     * @param CertificationFolder $certificationFolder
     * @param CertificationFolderService $certificationFolderService
     * @throws ContainerExceptionInterface
     * @throws NoResultException
     * @throws NonUniqueResultException
     * @throws NotFoundExceptionInterface
     * @throws Throwable
     */
    public function delete(CertificationFolder $certificationFolder, CertificationFolderService $certificationFolderService)
    {
        $certificationFolderService->delete($certificationFolder);
    }

    /**
     * @Rest\Get("/app/certificationFolders/{externalId}/getActions")
     * @IsGranted(CertificationFolderVoter::VIEW, subject="certificationFolder",  message="not allowed")
     * @Rest\View(StatusCode = 200)
     * @param CertificationFolder $certificationFolder
     * @param CertificationFolderService $certificationFolderService
     * @return array
     */
    public function getActions(CertificationFolder $certificationFolder, CertificationFolderService $certificationFolderService): array
    {
        $user = $this->getUser();
        $context = new Context();
        if ($user instanceof Attendee) {
            $context->addGroup('attendee');
        } else if ($user instanceof User) {
            $context->addGroup('Default'); // Weirdly it's Default for Certifier & Partner alike
        } else {
            throw new LogicException("Classe du user inconnue.");
        }

        return $certificationFolderService->getActions($certificationFolder, $user);
    }

    /**
     * @Rest\Get("/app/certificationFolders/{externalId}/badgeAssertion")
     * @Security("is_granted('view', certificationFolder) or is_granted('attendeeView', certificationFolder)", message="not allowed")
     * @Rest\View(StatusCode = 200)
     * @param CertificationFolder $certificationFolder
     * @param CertificationFolderService $certificationFolderService
     * @return array
     * @throws ClientExceptionInterface
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws \Symfony\Contracts\HttpClient\Exception\TransportExceptionInterface
     */
    public function getBadgeAssertionData(CertificationFolder $certificationFolder, CertificationFolderService $certificationFolderService): ?array
    {
        $badgeAssertion = $certificationFolder->getBadgeAssertion();
        $badgeAssertionData = $badgeAssertion ? $certificationFolderService->verifyBadgeAssertion($badgeAssertion) : null;
        return $badgeAssertionData ? ['imageUrl' => $badgeAssertionData['image']['id'] ?? null] : null;
    }

    //----------------
    // METHODES PRIVES
    //----------------
    /**
     * @param array $body
     * @param bool $allowExtraFields
     * @return ConstraintViolationListInterface
     */
    private function validateCreateBody(array $body, bool $allowExtraFields = false): ConstraintViolationListInterface
    {
        $validator = Validation::createValidator();
        $constraints = new Assert\Collection([
            'allowExtraFields' => $allowExtraFields,
            'fields' => [
                'attendeeId' => new Assert\Required([new Assert\NotBlank(), new Assert\Type('integer'), new Assert\NotNull()]),
                'certifInfo' => new Assert\Required([new Assert\NotBlank(), new Assert\Type('string'), new Assert\NotNull(), new Assert\Length(['max' => 255])]),
                'partner' => new Assert\Optional([new Assert\Type('string'), new Assert\Length(14)]),
                'tags' => new Assert\Optional(new Assert\Type('array')),
                'type' => new Assert\Optional([new Assert\Choice([CertificationFolderType::CERTIFIE()->getValue(), CertificationFolderType::OF()->getValue(), CertificationFolderType::POLE_EMPLOI()->getValue(), CertificationFolderType::AUTRE()->getValue(), CertificationFolderType::EMPLOYEUR()->getValue()])]),
                'optionName' => new Assert\Optional([new Assert\Type('string'), new Assert\Length(['max' => 255])]),
                'accessModality' => new Assert\Optional([new Assert\Choice([CertificationFolderAccessModality::CANDIDAT_LIBRE()->getValue(),
                    CertificationFolderAccessModality::EQUIVALENCE()->getValue(), CertificationFolderAccessModality::VAE()->getValue(),
                    CertificationFolderAccessModality::FORMATION_CONTINUE_CONTRAT_DE_PROFESSIONNALISATION()->getValue(),
                    CertificationFolderAccessModality::FORMATION_CONTINUE_HORS_CONTRAT_DE_PROFESSIONNALISATION()->getValue(),
                    CertificationFolderAccessModality::FORMATION_INITIALE_APPRENTISSAGE()->getValue(),
                    CertificationFolderAccessModality::FORMATION_INITIALE_HORS_APPRENTISSAGE()->getValue()])]),
                'accessModalityVae' => new Assert\Optional([new Assert\Choice([CertificationFolderAccessModalityVae::CONGES_VAE()->getValue(), CertificationFolderAccessModalityVae::VAE_CLASSIQUE()->getValue()])]),
                'enrollmentDate' => new Assert\Optional([new Assert\Type('datetime')]),
                'skillSets' => new Assert\Optional([
                    new Assert\Type('array'),
                    new Assert\All([
                        new Assert\Type('integer')
                    ])
                ])
            ]
        ]);
        return $validator->validate($body, $constraints);
    }

    /**
     * @param array $body
     * @return ConstraintViolationListInterface
     */
    private function validateSuccessBody(array $body): ConstraintViolationListInterface
    {
        $validator = Validation::createValidator();
        $constraints = new Assert\Collection([
            'digitalProofLink' => new Assert\Optional([new Assert\Type('string'), new Assert\Length(['max' => 255])]),
            'issueDate' => new Assert\Required(new Assert\Type('datetime')),
            'detailedResult' => new Assert\Optional([new Assert\Type('string'), new Assert\Length(['max' => 255])]),
            'comment' => new Assert\Optional([new Assert\Type('string'), new Assert\Length(['max' => 5000])]),
            'gradePass' => new Assert\Optional([new Assert\Choice([
                CertificationFolderGradePass::SANS_MENTION()->getValue(),
                CertificationFolderGradePass::MENTION_ASSEZ_BIEN()->getValue(),
                CertificationFolderGradePass::MENTION_BIEN()->getValue(),
                CertificationFolderGradePass::MENTION_TRES_BIEN()->getValue(),
                CertificationFolderGradePass::MENTION_TRES_BIEN_AVEC_FELICITATIONS_DU_JURY()->getValue()
            ])]),
            'europeanLanguageLevel' => new Assert\Optional([new Assert\Choice([CertificationFolderEuropeanLanguageLevel::C2()->getValue(), CertificationFolderEuropeanLanguageLevel::C1()->getValue(), CertificationFolderEuropeanLanguageLevel::B2()->getValue(), CertificationFolderEuropeanLanguageLevel::B1()->getValue(), CertificationFolderEuropeanLanguageLevel::A2()->getValue(), CertificationFolderEuropeanLanguageLevel::A1()->getValue(), CertificationFolderEuropeanLanguageLevel::INSUFFISANT()->getValue()])]),
            'certificate' => new Assert\Optional(),
            'certificateId' => new Assert\Optional(new Assert\Type('string')),
            'badgeAssertion' => new Assert\Optional([new Assert\Url()])
        ]);
        return $validator->validate($body, $constraints);
    }

    /**
     * @param array $body
     * @return ConstraintViolationListInterface
     */
    private function validateFailBody(array $body): ConstraintViolationListInterface
    {
        $validator = Validation::createValidator();
        $constraints = new Assert\Collection([
            'detailedResult' => new Assert\Optional([new Assert\Type('string'), new Assert\Length(['max' => 255])]),
            'comment' => new Assert\Optional([new Assert\Type('string'), new Assert\Length(['max' => 5000])]),
            'europeanLanguageLevel' => new Assert\Optional([new Assert\Choice([CertificationFolderEuropeanLanguageLevel::C2()->getValue(), CertificationFolderEuropeanLanguageLevel::C1()->getValue(), CertificationFolderEuropeanLanguageLevel::B2()->getValue(), CertificationFolderEuropeanLanguageLevel::B1()->getValue(), CertificationFolderEuropeanLanguageLevel::A2()->getValue(), CertificationFolderEuropeanLanguageLevel::A1()->getValue(), CertificationFolderEuropeanLanguageLevel::INSUFFISANT()->getValue()])]),
        ]);
        return $validator->validate($body, $constraints);
    }

    /**
     * @param array $body
     * @return ConstraintViolationListInterface
     */
    private function validateToRetakeBody(array $body): ConstraintViolationListInterface
    {
        $validator = Validation::createValidator();
        $constraints = new Assert\Collection([
            'detailedResult' => new Assert\Optional([new Assert\Type('string'), new Assert\Length(['max' => 255])]),
            'europeanLanguageLevel' => new Assert\Optional([new Assert\Choice([CertificationFolderEuropeanLanguageLevel::C2()->getValue(), CertificationFolderEuropeanLanguageLevel::C1()->getValue(), CertificationFolderEuropeanLanguageLevel::B2()->getValue(), CertificationFolderEuropeanLanguageLevel::B1()->getValue(), CertificationFolderEuropeanLanguageLevel::A2()->getValue(), CertificationFolderEuropeanLanguageLevel::A1()->getValue(), CertificationFolderEuropeanLanguageLevel::INSUFFISANT()->getValue()])]),
            'examinationDate' => new Assert\Optional([new Assert\Type('datetime')]),
            'examinationEndDate' => new Assert\Optional([new Assert\Type('datetime')]),
            'examinationPlace' => new Assert\Optional([new Assert\Type('string'), new Assert\Length(['max' => 1000])]),
            'examinationType' => new Assert\Optional([new Assert\Choice([CertificationExaminationType::A_DISTANCE()->getValue(), CertificationExaminationType::EN_PRESENTIEL()->getValue(), CertificationExaminationType::MIXTE()->getValue()])]),
            'comment' => new Assert\Optional([new Assert\Type('string'), new Assert\Length(['max' => 5000])]),
            'tiersTemps' => new Assert\Optional(new Assert\Type('boolean'))
        ]);
        return $validator->validate($body, $constraints);
    }

    /**
     * @param array $body
     * @return ConstraintViolationListInterface
     */
    private function validateToControlBody(array $body): ConstraintViolationListInterface
    {
        $validator = Validation::createValidator();
        $constraints = new Assert\Collection([
            'examinationDate' => new Assert\Optional(new Assert\Type('datetime')),
            'examinationEndDate' => new Assert\Optional(new Assert\Type('datetime')),
            'examinationPlace' => new Assert\Optional([new Assert\Type('string'), new Assert\Length(['max' => 1000])]),
            'examinationType' => new Assert\Optional([new Assert\Choice([CertificationExaminationType::A_DISTANCE()->getValue(), CertificationExaminationType::EN_PRESENTIEL()->getValue(), CertificationExaminationType::MIXTE()->getValue()])]),
            'comment' => new Assert\Optional([new Assert\Type('string'), new Assert\Length(['max' => 5000])]),
            'enrollmentDate' => new Assert\Optional(new Assert\Type('datetime')),
        ]);
        return $validator->validate($body, $constraints);
    }

    /**
     * @param array $body
     * @return ConstraintViolationListInterface
     */
    private function validateToTakeBody(array $body): ConstraintViolationListInterface
    {
        $validator = Validation::createValidator();
        $constraints = new Assert\Collection([
            'examinationDate' => new Assert\Optional(new Assert\Type('datetime')),
            'enrollmentDate' => new Assert\Optional(new Assert\Type('datetime')),
            'examinationEndDate' => new Assert\Optional(new Assert\Type('datetime')),
            'examinationPlace' => new Assert\Optional([new Assert\Type('string'), new Assert\Length(['max' => 1000])]),
            'examinationType' => new Assert\Optional([new Assert\Choice([CertificationExaminationType::A_DISTANCE()->getValue(), CertificationExaminationType::EN_PRESENTIEL()->getValue(), CertificationExaminationType::MIXTE()->getValue()])]),
            'comment' => new Assert\Optional([new Assert\Type('string'), new Assert\Length(['max' => 5000])]),
            'tiersTemps' => new Assert\Optional(new Assert\Type('boolean'))
        ]);
        return $validator->validate($body, $constraints);
    }

    /**
     * @param array $body
     * @return ConstraintViolationListInterface
     */
    private function validateRegisteredBody(array $body): ConstraintViolationListInterface
    {
        $validator = Validation::createValidator();
        $constraints = new Assert\Collection([
            'examinationDate' => new Assert\Optional(new Assert\Type('datetime')),
            'enrollmentDate' => new Assert\Optional(new Assert\Type('datetime')),
            'examinationEndDate' => new Assert\Optional(new Assert\Type('datetime')),
            'examinationPlace' => new Assert\Optional([new Assert\Type('string'), new Assert\Length(['max' => 1000])]),
            'examinationType' => new Assert\Optional([new Assert\Choice([CertificationExaminationType::A_DISTANCE()->getValue(), CertificationExaminationType::EN_PRESENTIEL()->getValue(), CertificationExaminationType::MIXTE()->getValue()])]),
            'comment' => new Assert\Optional([new Assert\Type('string'), new Assert\Length(['max' => 5000])]),
        ]);
        return $validator->validate($body, $constraints);
    }

    /**
     * @param array $body
     * @return ConstraintViolationListInterface
     */
    private function validateRefuseBody(array $body): ConstraintViolationListInterface
    {
        $validator = Validation::createValidator();
        $constraints = new Assert\Collection([
            'comment' => new Assert\Optional([new Assert\Type('string'), new Assert\Length(['max' => 5000])]),
        ]);
        return $validator->validate($body, $constraints);
    }

    /**
     * @param array $body
     * @return ConstraintViolationListInterface
     */
    private function validateAbortBody(array $body): ConstraintViolationListInterface
    {
        $validator = Validation::createValidator();
        $constraints = new Assert\Collection([
            'comment' => new Assert\Optional([new Assert\Type('string'), new Assert\Length(['max' => 5000])]),
        ]);
        return $validator->validate($body, $constraints);
    }

    /**
     * @param array $body
     * @return ConstraintViolationListInterface
     */
    private function validateUpdateBody(array $body): ConstraintViolationListInterface
    {
        $validator = Validation::createValidator();
        $constraints = new Assert\Collection([
            'allowExtraFields' => true,
            'fields' => [
                'partner' => new Assert\Optional([new Assert\Type('string'), new Assert\Length(14)]),
                'examinationDate' => new Assert\Optional(new Assert\Type('datetime')),
                'examinationEndDate' => new Assert\Optional(new Assert\Type('datetime')),
                'examinationPlace' => new Assert\Optional([new Assert\Type('string'), new Assert\Length(['max' => 1000])]),
                'examinationType' => new Assert\Optional([new Assert\Choice([CertificationExaminationType::A_DISTANCE()->getValue(), CertificationExaminationType::EN_PRESENTIEL()->getValue(), CertificationExaminationType::MIXTE()->getValue()])]),
                'comment' => new Assert\Optional([new Assert\Type('string'), new Assert\Length(['max' => 5000])]),
                'verbatim' => new Assert\Optional([new Assert\Type('string'), new Assert\Length(['max' => 255])]),
                'optionName' => new Assert\Optional([new Assert\Type('string'), new Assert\Length(['max' => 255])]),
                'accessModality' => new Assert\Optional([new Assert\Choice([CertificationFolderAccessModality::CANDIDAT_LIBRE()->getValue(),
                    CertificationFolderAccessModality::EQUIVALENCE()->getValue(), CertificationFolderAccessModality::VAE()->getValue(),
                    CertificationFolderAccessModality::FORMATION_CONTINUE_CONTRAT_DE_PROFESSIONNALISATION()->getValue(),
                    CertificationFolderAccessModality::FORMATION_CONTINUE_HORS_CONTRAT_DE_PROFESSIONNALISATION()->getValue(),
                    CertificationFolderAccessModality::FORMATION_INITIALE_APPRENTISSAGE()->getValue(),
                    CertificationFolderAccessModality::FORMATION_INITIALE_HORS_APPRENTISSAGE()->getValue()])]),
                'accessModalityVae' => new Assert\Optional([new Assert\Choice([CertificationFolderAccessModalityVae::CONGES_VAE()->getValue(), CertificationFolderAccessModalityVae::VAE_CLASSIQUE()->getValue()])]),
                'enrollmentDate' => new Assert\Optional([new Assert\Type('datetime')]),
                'examinationCenterZipCode' => new Assert\Optional(new Assert\Type('string')),
                'tags' => new Assert\Optional(new Assert\Type('array')),
                'addedTags' => new Assert\Optional(new Assert\Type('array')),
                'removedTags' => new Assert\Optional(new Assert\Type('array')),
                'type' => new Assert\Optional([new Assert\Choice([CertificationFolderType::CERTIFIE()->getValue(), CertificationFolderType::OF()->getValue(), CertificationFolderType::POLE_EMPLOI()->getValue(), CertificationFolderType::AUTRE()->getValue(), CertificationFolderType::EMPLOYEUR()->getValue()])]),
                'amountHt' => new Assert\Optional(new Assert\Type('float')),
                'cdcExcluded' => new Assert\Optional([new Assert\NotNull(), new Assert\Type('bool')]),
                'certificate' => new Assert\Optional(),
                'certificateId' => new Assert\Optional(new Assert\Type('string')),
                'metadata' => new Assert\Optional(new Assert\Type('array')),
                'tiersTemps' => new Assert\Optional(new Assert\Type('boolean')),
                'skillSets' => new Assert\Optional([
                    new Assert\Type('array'),
                    new Assert\All([
                        new Assert\Type('integer')
                    ])
                ]),
                'badgeAssertion' => new Assert\Optional([new Assert\Url()]),
                'cdcTechnicalId' => new Assert\Optional([new Assert\Type('string'), new Assert\Length(['max' => 255])])
            ]
        ]);
        return $validator->validate($body, $constraints);
    }


    /**
     * @param array $data
     * @return ConstraintViolationListInterface
     */
    private function validateForceUpdateData(array $data): ConstraintViolationListInterface
    {
        $validator = Validation::createValidator();
        $constraints = new Assert\Collection([
            'allowExtraFields' => true,
            'fields' => [
                'amountHt' => new Assert\Optional(new Assert\Type('float')),
                'cdcExcluded' => new Assert\Optional([new Assert\Type('bool')]),
                'tags' => new Assert\Optional(new Assert\Type('array')),
                'examinationPlace' => new Assert\Optional([new Assert\Type('string'), new Assert\Length(['max' => 1000])]),
                'examinationType' => new Assert\Optional([new Assert\Choice([CertificationExaminationType::A_DISTANCE()->getValue(), CertificationExaminationType::EN_PRESENTIEL()->getValue(), CertificationExaminationType::MIXTE()->getValue()])]),
                'comment' => new Assert\Optional([new Assert\Type('string'), new Assert\Length(['max' => 5000])]),
                'examinationCenterZipCode' => new Assert\Optional(new Assert\Type('string')),
                'type' => new Assert\Optional([new Assert\Choice([CertificationFolderType::CERTIFIE()->getValue(), CertificationFolderType::OF()->getValue(), CertificationFolderType::POLE_EMPLOI()->getValue(), CertificationFolderType::AUTRE()->getValue(), CertificationFolderType::EMPLOYEUR()->getValue()])]),
                'optionName' => new Assert\Optional([new Assert\Type('string'), new Assert\Length(['max' => 255])]),
                'accessModality' => new Assert\Optional([new Assert\Choice([CertificationFolderAccessModality::CANDIDAT_LIBRE()->getValue(),
                    CertificationFolderAccessModality::EQUIVALENCE()->getValue(), CertificationFolderAccessModality::VAE()->getValue(),
                    CertificationFolderAccessModality::FORMATION_CONTINUE_CONTRAT_DE_PROFESSIONNALISATION()->getValue(),
                    CertificationFolderAccessModality::FORMATION_CONTINUE_HORS_CONTRAT_DE_PROFESSIONNALISATION()->getValue(),
                    CertificationFolderAccessModality::FORMATION_INITIALE_APPRENTISSAGE()->getValue(),
                    CertificationFolderAccessModality::FORMATION_INITIALE_HORS_APPRENTISSAGE()->getValue()])]),
                'accessModalityVae' => new Assert\Optional([new Assert\Choice([CertificationFolderAccessModalityVae::CONGES_VAE()->getValue(), CertificationFolderAccessModalityVae::VAE_CLASSIQUE()->getValue()])]),
                'gradePass' => new Assert\Optional([new Assert\Choice([
                    CertificationFolderGradePass::SANS_MENTION()->getValue(),
                    CertificationFolderGradePass::MENTION_ASSEZ_BIEN()->getValue(),
                    CertificationFolderGradePass::MENTION_BIEN()->getValue(),
                    CertificationFolderGradePass::MENTION_TRES_BIEN()->getValue(),
                    CertificationFolderGradePass::MENTION_TRES_BIEN_AVEC_FELICITATIONS_DU_JURY()->getValue()
                ])]),
                'detailedResult' => new Assert\Optional([new Assert\Type('string'), new Assert\Length(['max' => 255])]),
                'digitalProofLink' => new Assert\Optional([new Assert\Type('string'), new Assert\Length(['max' => 255])]),
                'europeanLanguageLevel' => new Assert\Optional([new Assert\Choice([CertificationFolderEuropeanLanguageLevel::C2()->getValue(), CertificationFolderEuropeanLanguageLevel::C1()->getValue(), CertificationFolderEuropeanLanguageLevel::B2()->getValue(), CertificationFolderEuropeanLanguageLevel::B1()->getValue(), CertificationFolderEuropeanLanguageLevel::A2()->getValue(), CertificationFolderEuropeanLanguageLevel::A1()->getValue(), CertificationFolderEuropeanLanguageLevel::INSUFFISANT()->getValue()]), new Assert\Length(['max' => 255])]),
                'issueDate' => new Assert\Optional(new Assert\Type('datetime')),
                'enrollmentDate' => new Assert\Optional(new Assert\Type('datetime')),
                'examinationDate' => new Assert\Optional(new Assert\Type('datetime')),
                'examinationEndDate' => new Assert\Optional(new Assert\Type('datetime')),
            ]
        ]);
        return $validator->validate($data, $constraints);
    }

    /**
     * @param User $user
     * @param array $parameters
     * @param CertificationService $certificationService
     * @param AccessService $accessService
     * @param SkillService $skillService
     * @return array
     * @throws Exception
     */
    private function validateListParameters(User $user, array $parameters, CertificationService $certificationService, AccessService $accessService, SkillService $skillService): array
    {
        $parameters['allowed'] = $accessService->hasCertificationFoldersView($user);

        $parameters['certification'] = null;
        if (isset($parameters['registrationFolderState'])) {
            $parameters['registrationFolderState'] = explode(",", $parameters['registrationFolderState']);
            foreach ($parameters['registrationFolderState'] as $registrationFolderState) {
                if (!in_array($registrationFolderState, RegistrationFolderStates::valuesStatesToString())) { // we don't want to include "all"
                    throw new WedofBadRequestHttpException("Erreur, les valeurs renvoyées 'registrationFolderState' doivent être : " . join(",", RegistrationFolderStates::valuesStatesToString()) . " . ");
                }
            }
        }
        if (isset($parameters['registrationFolderType'])) {
            $parameters['registrationFolderType'] = explode(",", $parameters['registrationFolderType']);
            foreach ($parameters['registrationFolderType'] as $registrationFolderType) {
                if (!in_array($registrationFolderType, DataProviders::valuesTypes())) { // we don't want to include "all"
                    throw new WedofBadRequestHttpException("Erreur, les valeurs renvoyées 'registrationFolderType' doivent être : " . join(",", RegistrationFolderStates::valuesStatesToString()) . " . ");
                }
            }
        }
        if (isset($parameters['state'])) {
            $parameters['state'] = explode(",", $parameters['state']);
            foreach ($parameters['state'] as $state) {
                if (!in_array($state, CertificationFolderStates::valuesStates())) {
                    throw new WedofBadRequestHttpException("Erreur sur les valeurs renvoyées 'state', elles doivent être : " . join(",", CertificationFolderStates::valuesStates()) . " . ");
                }
            }
        }
        if (isset($parameters['cdcState'])) {
            $parameters['cdcState'] = explode(",", $parameters['cdcState']);
            foreach ($parameters['cdcState'] as $cdcState) {
                if (!in_array($cdcState, CertificationFolderCdcStates::valuesStates())) {
                    throw new WedofBadRequestHttpException("Erreur sur les valeurs renvoyées 'cdcState', elles doivent être : " . join(",", CertificationFolderCdcStates::valuesStates()) . " . ");
                }
            }
        }
        if (isset($parameters['survey'])) {
            $parameters['survey'] = explode(",", $parameters['survey']);
        }
        if (isset($parameters['messageState'])) {
            $parameters['messageState'] = explode(",", $parameters['messageState']);
            foreach ($parameters['messageState'] as $messageState) {
                if (!in_array($messageState, MessageStates::valuesTypes())) {
                    throw new WedofBadRequestHttpException("Erreur sur les valeurs renvoyées 'messageState', elles doivent être : " . join(",", MessageStates::valuesTypes()) . ".");
                }
            }
        }
        if (isset($parameters['messageTemplate'])) {
            $parameters['messageTemplate'] = explode(",", $parameters['messageTemplate']);
        }
        if (isset($parameters['siret'])) {
            $parameters['sirets'] = explode(',', $parameters['siret']);
            if (in_array('all', $parameters['sirets'])) {
                $parameters['partners'] = null;
            } else {
                $parameters['partners'] = $parameters['sirets'];
            }
        } else {
            $parameters['partners'] = [$user->getMainOrganism()->getSiret()];
        }

        if (isset($parameters['certifInfo'])) {
            $parameters['certifInfos'] = explode(',', $parameters['certifInfo']);
            if (in_array('all', $parameters['certifInfos'])) {
                $parameters['certifications'] = null;
            } else if (in_array('certifier', $parameters['certifInfos'])) {
                $certificationParameters = ['organismType' => 'certifier'];
                $certifications = $certificationService->listReturnQueryBuilder($user->getMainOrganism(), $certificationParameters)->getQuery()->getResult();
                $parameters['certifications'] = array_map(function ($o) {
                    return $o->getCertifInfo();
                }, $certifications);
            } else {
                $parameters['certifications'] = $parameters['certifInfos'];
            }
        } else {
            $parameters['certifications'] = null;
        }

        if (isset($parameters['skillSets'])) {
            if (!$parameters['certifications'] || count($parameters['certifications']) !== 1) {
                throw new WedofBadRequestHttpException("Erreur, pour lister les blocs de compétences vous devez sélectionner une certification de type RNCP.");
            } else {
                $parameters['skillSets'] = explode(',', $parameters['skillSets']);
                $certification = $certificationService->getByCertifInfo($parameters['certifications'][0]);
                if ($certification->getType() !== CertificationTypes::RNCP()->getValue()) {
                    throw new WedofBadRequestHttpException("Erreur, pour lister les blocs de compétences vous devez sélectionner une certification de type RNCP.");
                }
                $skillSetsFromCertification = $certification->getSkillSets();
                if ($skillSetsFromCertification->count() === 0) {
                    throw new WedofBadRequestHttpException("Erreur, aucun bloc de compétences n'existe sur la certification");
                }
                if (in_array('all', $parameters['skillSets'])) {
                    $parameters['skillSets'] = null;
                } else {
                    foreach ($parameters['skillSets'] as $skillSet) {
                        if ($skillSet === 'all') {
                            $parameters['skillSets'] = null;
                        } else {
                            $skill = $skillService->getById($skillSet);
                            if (!$skill) {
                                throw new WedofBadRequestHttpException("Erreur, le bloc de compétence avec l'id " . $skillSet . " n'existe pas");
                            } else if (!$skillSetsFromCertification->contains($skill)) {
                                throw new WedofBadRequestHttpException("Erreur, le bloc de compétence avec l'id " . $skillSet . " n'existe pas sur la certification");
                            }
                        }
                    }
                    if (count($skillSetsFromCertification) === count($parameters['skillSets'])) {
                        $parameters['skillSets'] = null;
                    }
                }
            }
        }

        if (isset($parameters['period']) && $parameters['period'] != PeriodTypes::CUSTOM()->getValue()) {
            $getDates = Tools::getSinceAndUntilDates($parameters['period'], null, $user->getMainOrganism()->getSubscription());
            $parameters['since'] = $getDates['since'];
            $parameters['until'] = $getDates['until'];
        }

        if (isset($parameters['filterOnStateDate']) && $parameters['filterOnStateDate'] === 'wedofInvoice' && (!isset($parameters['since']) || !isset($parameters['until']))) {
            throw new WedofBadRequestHttpException("Erreur, vous devez renvoyer les valeurs 'since' et 'until' afin de pouvoir filtrer sur l'état 'wedofInvoice' ");
        }

        if (isset($parameters['metadata'])) {
            $parameters['metadata'] = explode(":", $parameters['metadata'], 2); //key=value if value contains : it shoulds work too but not if it is part of the key...
            if (sizeof($parameters['metadata']) > 2) {
                throw new WedofBadRequestHttpException("Erreur sur le format 'metadata', il doit être le suivant : cle:valeur");
            }
        }

        $this->cleanUpParameters($parameters);

        return $parameters;
    }

    /**
     * @param array $body
     * @return ConstraintViolationListInterface
     */
    private function validateUpdatePriceAsyncBody(array $body): ConstraintViolationListInterface
    {
        $validator = Validation::createValidator();
        $constraints = new Assert\Collection([
            "certificationFolders" => new Assert\Required([
                new Assert\Type('array'),
                new Assert\Count(['min' => 1]),
                new Assert\All([
                    new Assert\Collection([
                        'id' => new Assert\Required([new Assert\NotBlank(), new Assert\Type('integer')]),
                        'amountHt' => new Assert\Required(new Assert\Type('float')),
                    ])
                ])
            ])
        ]);
        return $validator->validate($body, $constraints);
    }

    /**
     * @param array $body
     * @return ConstraintViolationListInterface
     */
    private function validateUpdateFileBody(array $body): ConstraintViolationListInterface
    {
        $validation = Validation::createValidator();
        $constraints = new Assert\Collection([
            'state' => new Assert\Optional([
                new Assert\Choice([
                    FileStates::REFUSED()->getValue(),
                    FileStates::VALID()->getValue(),
                    FileStates::TO_REVIEW()->getValue()
                ])
            ]),
            'comment' => new Assert\Optional([new Assert\Type('string'), new Assert\Length(['max' => 1000])])
        ]);
        return $validation->validate($body, $constraints);
    }

    /**
     * @param $file
     * @param array $mimeTypes
     * @return ConstraintViolationListInterface
     */
    private function validateFile($file, array $mimeTypes): ConstraintViolationListInterface
    {
        if ($file->getMimeType() !== "application/octet-stream") {
            $validation = Validation::createValidator();
            $constraints = new Assert\File(['mimeTypes' => $mimeTypes]);
            $violations = $validation->validate($file, $constraints);
        } else {
            $violations = new ConstraintViolationList();
            if (!in_array($file->getClientMimeType(), $mimeTypes)) {
                $violations->add(new ConstraintViolation("ClientMimeType not found in mimeTypes.", null, [], $file, null, $file->getClientMimeType()));
            }
        }
        return $violations;
    }

    /**
     * @param $row
     * @param $headersMapping
     * @param string $columnName
     * @param string $columnType
     * @return array|bool|DateTime|false|float|null
     * @throws Exception
     */
    private function getValueFromSpreadSheetColumn($row, $headersMapping, string $columnName, string $columnType = 'string')
    {
        $value = null;
        if (isset($headersMapping[$columnName]) && isset($row[$headersMapping[$columnName]])) {
            $rawValue = $row[$headersMapping[$columnName]];
            $value = $rawValue !== '' ? $rawValue : null;
        }
        if (isset($value)) {
            switch ($columnType) {
                case 'date': // Support both 2022-03-21 and 21/03/2022
                    try {
                        $date = Tools::createDateFromString($value);
                    } catch (Exception $exception) {
                        throw new Exception("Erreur, le champ " . $columnName . " doit être au format date année - mois - jour, par ex . 2022 - 03 - 21");
                    }
                    $value = $date->setTime(0, 0);
                    break;
                case 'bool':
                    if (!in_array($value, ['Oui', 'Non'])) {
                        throw new Exception("Erreur, le champ " . $columnName . " doit être vide ou renseigné à Oui ou Non");
                    }
                    $value = $value === 'Oui';
                    break;
                case 'array':
                    $value = array_map('trim', explode(',', $value));
                    break;
                case 'float':
                    $value = (float)$value;
                    break;
            }
        }
        return $value;
    }

    /**
     * @param $row
     * @param $headersMapping
     * @return array
     * @throws Exception
     */
    private function getDataFromSpreadsheetRow($row, $headersMapping): array
    {
        // The order does not matter, column index is found dynamically
        $columns = self::getXlsxColumnDefinition();
        $certificationFolderData = [];
        foreach ($columns as $propertyName => $column) {
            $certificationFolderData[$propertyName] = $this->getValueFromSpreadSheetColumn($row, $headersMapping, $column['columnName'], $column['type']);
        }
        $violations = $this->validateForceUpdateData($certificationFolderData);
        if (count($violations)) {
            $violationMessages = [];
            foreach ($violations as $violation) {
                $violationMessages[] = $violation->getPropertyPath() . ': ' . $violation->getMessage();
            }
            throw new Exception('Erreur: format de données incorrect ' . join(', ', $violationMessages));
        }
        return $certificationFolderData;
    }

    /**
     * @param CertificationFolder $certificationFolder
     * @return array
     */
    private function getSpreadsheetRowFromCertificationFolder(CertificationFolder $certificationFolder): array
    {
        $attendee = $certificationFolder->getAttendee();
        $registrationFolder = $certificationFolder->getRegistrationFolder();
        return [
            'id' => $certificationFolder->getId(),
            'externalId' => $certificationFolder->getExternalId(),
            'idCpf' => $registrationFolder && $registrationFolder->getType() === DataProviders::CPF()->getValue() ? $registrationFolder->getExternalId() : '',
            'state' => CertificationFolderStates::toFrString($certificationFolder->getState()),
            'cdcState' => CertificationFolderCdcStates::toFrString($certificationFolder->getCdcState()),
            'firstName' => $attendee->getFirstName(),
            'lastName' => $attendee->getLastName(),
            'phoneNumber' => $attendee->getPhoneNumber(),
            'email' => $attendee->getEmail(),
            'attendeeLink' => $certificationFolder->getAttendeeLink(),
            'addToPassportLink' => $certificationFolder->getAddToPassportLink(),
            'organism' => $certificationFolder->getPartner() ? $certificationFolder->getPartner()->getName() : '',
            'organismSiret' => $certificationFolder->getPartner() ? $certificationFolder->getPartner()->getSiret() : '',
            'certification' => $certificationFolder->getCertification()->getExternalId() ?? '',
            'enrollmentDate' => $certificationFolder->getEnrollmentDate() ? $certificationFolder->getEnrollmentDate()->format('d/m/Y') : '',
            'examinationDate' => $certificationFolder->getExaminationDate() ? $certificationFolder->getExaminationDate()->format('d/m/Y') : '',
            'examinationEndDate' => $certificationFolder->getExaminationEndDate() ? $certificationFolder->getExaminationEndDate()->format('d/m/Y') : '',
            'examinationType' => $certificationFolder->getExaminationType() ?? '',
            'examinationPlace' => $certificationFolder->getExaminationPlace() ?? '',
            'examinationCenterZipCode' => $certificationFolder->getExaminationCenterZipCode() ?? '',
            'type' => $certificationFolder->getType() ?? '',
            'accessModality' => $certificationFolder->getAccessModality() ?? '',
            'accessModalityVae' => $certificationFolder->getAccessModalityVae() ?? '',
            'optionName' => $certificationFolder->getOptionName() ?? '',
            'cdcTechnicalId' => $certificationFolder->getCdcTechnicalId(),
            'cdcExcluded' => $certificationFolder->isCdcExcluded() ? 'Oui' : 'Non',
            'amountHt' => $certificationFolder->getAmountHt() != null ? $certificationFolder->getAmountHt() : '',
            'comment' => $certificationFolder->getComment() ?? '',
            'tags' => $certificationFolder->getTagsText(),
            'detailedResult' => $certificationFolder->getDetailedResult() ?? '',
            'issueDate' => $certificationFolder->getIssueDate() ? $certificationFolder->getIssueDate()->format('d/m/Y') : '',
            'digitalProofLink' => $certificationFolder->getDigitalProofLink() ?? '',
            'gradePass' => $certificationFolder->getGradePass() ?? '',
            'europeanLanguageLevel' => $certificationFolder->getEuropeanLanguageLevel() ?? '',
        ];
    }

    /**
     * @throws \PhpOffice\PhpSpreadsheet\Exception
     */
    private static function applyFormatOnDateColumnsXlsx(Worksheet $workSheet)
    {
        $columnDates = array_filter(array_map(function ($column) {
            return $column['type'] == 'date' ? $column['columnName'] : null;
        }, self::getXlsxColumnDefinition()), function ($column) {
            return $column != null;
        });
        $lastRow = $workSheet->getHighestDataRow();
        $lastColumn = $workSheet->getHighestDataColumn();
        $lastColumn++; //next of the last xD
        for ($column = 'A'; $column != $lastColumn; $column++) {
            $value = $workSheet->getCell($column . "1")->getValue();
            if (in_array($value, $columnDates)) {
                $workSheet->getStyle($column . "2:$column$lastRow")->applyFromArray([
                    'numberFormat' => [
                        'formatCode' => NumberFormat::FORMAT_DATE_DDMMYYYY
                    ]
                ]);
            }
        }
    }

    /**
     * @return array
     */
    private static function getXlsxColumnDefinition(): array
    {
        return [
            'enrollmentDate' => ['columnName' => 'enrollmentDate', 'type' => 'date'],
            'examinationDate' => ['columnName' => 'examinationDate', 'type' => 'date'],
            'examinationEndDate' => ['columnName' => 'examinationEndDate', 'type' => 'date'],
            'examinationType' => ['columnName' => 'examinationType', 'type' => 'string'],
            'examinationPlace' => ['columnName' => 'examinationPlace', 'type' => 'string'],
            'examinationCenterZipCode' => ['columnName' => 'examinationCenterZipCode', 'type' => 'string'],
            'type' => ['columnName' => 'type', 'type' => 'string'],
            'accessModality' => ['columnName' => 'accessModality', 'type' => 'string'],
            'accessModalityVae' => ['columnName' => 'accessModalityVae', 'type' => 'string'],
            'optionName' => ['columnName' => 'optionName', 'type' => 'string'],
            'cdcTechnicalId' => ['columnName' => 'cdcTechnicalId', 'type' => 'string'],
            'cdcExcluded' => ['columnName' => 'cdcExcluded', 'type' => 'bool'],
            'amountHt' => ['columnName' => 'amountHt', 'type' => 'float'],
            'comment' => ['columnName' => 'comment', 'type' => 'string'],
            'tags' => ['columnName' => 'tags', 'type' => 'array'],
            // Success or Failed
            'detailedResult' => ['columnName' => 'detailedResult', 'type' => 'string'],
            // Success
            'issueDate' => ['columnName' => 'issueDate', 'type' => 'date'],
            'digitalProofLink' => ['columnName' => 'digitalProofLink', 'type' => 'string'],
            'gradePass' => ['columnName' => 'gradePass', 'type' => 'string'],
            'europeanLanguageLevel' => ['columnName' => 'europeanLanguageLevel', 'type' => 'string']
        ];
    }

    /**
     * @param array $parameters
     * @return void
     */
    private function cleanUpParameters(array $parameters): void
    {
        unset($parameters['siret']);
        unset($parameters['sirets']);
        unset($parameters['certifInfo']);
        unset($parameters['certifInfos']);
    }


    /**
     * @param CertificationFolder $certificationFolder
     * @param User|null $user
     * @return array
     */
    private function getRolesForAuthenticatedUser(CertificationFolder $certificationFolder, User $user = null): array
    {
        if ($this->isGranted('ROLE_ADMIN')) {
            return [
                'attendee' => false,
                'partner' => false,
                'owner' => true
            ];
        }
        return [
            'attendee' => !$user && $this->isGranted(CertificationFolderVoter::ATTENDEE_EDIT, $certificationFolder),
            'partner' => $user && $user->getMainOrganism() !== $certificationFolder->getCertifier(),
            'owner' => $user && $user->getMainOrganism() === $certificationFolder->getCertifier()
        ];
    }

    /**
     * @param Certification $certification
     * @param $typeId
     * @return array|null
     */
    private function getFileType(Certification $certification, $typeId): ?array
    {
        $fileTypes = $certification->getCertificationFolderFileTypes();
        $fileTypeIndex = array_search($typeId, array_column($fileTypes, 'id'));
        if ($fileTypeIndex !== false) {
            $fileType = $fileTypes[$fileTypeIndex];
        }
        return $fileType ?? array_merge(Certification::getDefaultCertificationFolderFileTypeRightsForAttendee(), Certification::getDefaultCertificationFolderFileTypeRightsForPartner());
    }

    /**
     * @param array $data
     * @return array
     * @throws Exception
     */
    private function prepareJwtData(array $data): array
    {
        $data["certificationFolder"] = (array)$data["certificationFolder"];
        $data["certification"] = (array)$data["certification"];
        $data["attendee"] = (array)$data["attendee"];
        $data["certificationPartner"] = isset($data["certificationPartner"]) ? (array)$data["certificationPartner"] : null;
        $cfParisDateProperties = ['enrollmentDate', 'issueDate'];
        foreach ($cfParisDateProperties as $cfDateProperty) {
            if (!empty($data["certificationFolder"][$cfDateProperty])) {
                try {
                    $date = Tools::createDateFromString($data["certificationFolder"][$cfDateProperty]);
                    $data["certificationFolder"][$cfDateProperty] = $date->setTimezone(new DateTimeZone('Europe/Paris'));
                } catch (DateTimeException $e) {
                    unset($data["certificationFolder"][$cfDateProperty]);
                }
            }
        }
        $cfUtcDateProperties = ['examinationDate', 'examinationEndDate'];
        foreach ($cfUtcDateProperties as $cfDateProperty) {
            if (!empty($data["certificationFolder"][$cfDateProperty])) {
                try {
                    $date = Tools::createDateFromString($data["certificationFolder"][$cfDateProperty]);
                    $data["certificationFolder"][$cfDateProperty] = $date->setTimezone(new DateTimeZone('UTC'));
                } catch (DateTimeException $e) {
                    unset($data["certificationFolder"][$cfDateProperty]);
                }
            }
        }
        $attendeeDateProperties = ['dateOfBirth'];
        foreach ($attendeeDateProperties as $attendeeDateProperty) {
            if (!empty($data["attendee"][$attendeeDateProperty])) {
                try {
                    $date = Tools::createDateFromString($data["attendee"][$attendeeDateProperty]);
                    $data["attendee"][$attendeeDateProperty] = $date->setTimezone(new DateTimeZone('Europe/Paris'));
                } catch (DateTimeException $e) {
                    unset($data["attendee"][$attendeeDateProperty]);
                }
            }
        }
        if (!empty($data["certificationFolder"]["metadata"])) {
            $data["certificationFolder"]["metadata"] = json_decode(json_encode($data["certificationFolder"]["metadata"]), true);
        }
        if (!empty($data["registrationFolder"])) {
            $data["registrationFolder"] = (array)$data['registrationFolder'];
        }
        return $data;
    }

    /**
     * @param array $skillSets
     * @param Certification $certification
     * @param Organism $currentOrganism
     * @param SkillService $skillService
     * @param CertificationPartner|null $certificationPartner
     * @return array
     */
    private function getSkillSets(array $skillSets, Certification $certification, Organism $currentOrganism, SkillService $skillService, CertificationPartner $certificationPartner = null): array
    {
        $skills = [];
        foreach ($skillSets as $skillSet) {
            $skill = $skillService->getById($skillSet);
            if (!$skill) {
                throw new WedofBadRequestHttpException("Le skillSet d'ID " . $skillSet . " n'existe pas.");
            }
            if ($skill->getType() !== CertificationSkillType::SKILL_SET()->getValue()) {
                throw new WedofBadRequestHttpException("Le skillSet d'ID " . $skillSet . " n'est pas de type 'skillSet'.");
            }
            if ($skill->getCertification() !== $certification) {
                throw new WedofBadRequestHttpException("La certification liée au skillSet d'ID " . $skillSet . " ne correspond pas à la certification du partenariat.");
            }
            if ($certificationPartner) {
                if ($certificationPartner->getPartner() !== $currentOrganism && $certificationPartner->getCertifier() !== $currentOrganism) {
                    throw new WedofBadRequestHttpException("Vous devez être le partenaire ou le certificateur de ce partenariat pour déclarer des skillSets.");
                }
                if ($certificationPartner->getSkillSets()->count() > 0 && !$certificationPartner->getSkillSets()->contains($skill)) {
                    throw new WedofBadRequestHttpException("Le skillSet d'ID " . $skillSet . " n'existe pas sur le partenariat.");
                }
            }
            $skills[] = $skill;
        }
        return $skills;
    }
}
