{"common": {"help-center": {"title": "Bienvenue sur le Centre d'Aide Wedof", "common": {"return": "Retour vers le Centre d'Aide", "return-on-page": "Retour vers {{title}}", "title": "Guides & Ressources"}, "faqs": {"title": "FAQs", "description": "Foire aux questions", "faqCategories": [{"id": "webhook_faq", "slug": "webhook", "title": "Webhook", "faqs": [{"id": "api", "categoryId": "subscription", "question": "J'ai l'abonnement API qui inclus les webhooks pourtant je ne peux pas en créer en allant dans \"Mes applications\"."}]}, {"id": "api_faq", "slug": "api", "title": "API", "faqs": [{"id": "validation", "categoryId": "registration-folder", "question": "Je veux valider un dossier de formation par le endpoint **/validate** mais j'obtiens une erreur."}, {"id": "modify", "categoryId": "registration-folder", "question": "Je n'arrive pas à faire fonctionner le endpoint PUT / modifier des Registration Folders."}]}]}, "guides": {"title": "Guides", "description": "Articles et ressources pour vous accompagner", "guideCategories": [{"slug": "api-webhooks", "title": "API & Webhooks", "guides": [{"href": "/api/doc/", "title": "Référence API Rest", "subtitle": "Accédez aux fonctionnalités d'EDOF au travers de l'API REST"}, {"slug": "webhooks-principes", "title": "Principes des Webhooks", "subtitle": "Réagissez automatiquement à des évènements Wedof et EDOF"}, {"slug": "webhooks-creer-endpoint", "title": "<PERSON><PERSON><PERSON> un endpoint de Webhook", "subtitle": "Traiter la réception d'événements via les Webhooks"}, {"slug": "webhooks-ajouter", "title": "Ajouter un Webhook dans Wedof", "subtitle": "Ajou<PERSON>z et configurez le Webhook côté Wedof"}]}, {"slug": "applications", "title": "Applications", "guides": [{"slug": "salesforce", "title": "Salesforce", "subtitle": "Configurer l'intégration de Salesforce"}, {"slug": "digiforma", "title": "Digiforma", "subtitle": "Configurer l'intégration de Digiforma"}, {"slug": "activepieces", "title": "Activepieces", "subtitle": "Configurer vos automatisations via Activepieces"}, {"slug": "workflow", "title": "Processus Métiers Automatisés", "subtitle": "Configurer vos automatisations via Processus Métiers Automatisés"}, {"slug": "message-templates", "title": "Messages et notifications", "subtitle": "Programmer l'envoi de messages et de notifications"}, {"slug": "documents", "title": "Génération de documents", "subtitle": "Générer automatiquement vos documents"}, {"slug": "dictionnaire", "title": "Dictionnaire", "subtitle": "Dictionnaire des variables"}]}, {"slug": "dossier-formation", "title": "Dossiers de formation", "guides": [{"slug": "creer-dossier", "title": "C<PERSON>er un nouveau dossier hors CPF", "subtitle": "Comment créer un dossier hors CPF"}, {"slug": "cycle-de-vie", "title": "Cycle de vie", "subtitle": "Cycle de vie des dossiers de formation"}, {"slug": "export", "title": "Export de dossiers", "subtitle": "Exporter les dossiers de formation au format .csv"}]}, {"slug": "dossier-certification", "title": "Dossiers de certification", "guides": [{"slug": "creer-dossier", "title": "<PERSON><PERSON>er un nouveau dossier", "subtitle": "Comment créer un dossier"}, {"slug": "creer-dossier-api", "title": "C<PERSON>er un apprenant et un dossier par l'API", "subtitle": "Comment créer un apprenant et un dossier par l'API"}, {"slug": "export", "title": "Export de dossiers", "subtitle": "Exporter les dossiers de certification au format .csv"}, {"slug": "passeport", "title": "Bouton du Passeport de Compétences", "subtitle": "Configurer l'intégration du bouton d'ajout d'informations au passeport de compétences"}]}, {"slug": "certificateurs", "title": "Organisme Certificateur", "guides": [{"slug": "creer-compte-certifpro", "title": "Créer un compte déposant CertifPro", "subtitle": "Créer un compte déposant CertifPro pour Wedof"}, {"slug": "demander-autorisation-partenaire", "title": "Demander la synchronisation automatique à un partenaire", "subtitle": "Synchroniser automatiquement les dossiers d'un partenaire pour une certification"}, {"slug": "gerer-partenariats", "title": "Gérer vos partenariats", "subtitle": "Traitez les demandes de partenariat et l'ajout sur France Compétences"}, {"slug": "cycle-vie-certification-apprenant", "title": "Gérer la certification des candidats", "subtitle": "Suivre le cycle de vie de la certification d'un candidat"}, {"slug": "maj-dossiers-certification", "title": "Mise à jour des dossiers", "subtitle": "Les différentes méthodes pour mettre à jour les dossiers de certification"}, {"slug": "maj-dossiers-certification-excel", "title": "Mise à jour depuis Excel", "subtitle": "Mettre à jour les dossiers de certification depuis un fichier Excel"}, {"slug": "import-dossiers-certification-excel", "title": "Import historique de dossiers de certification depuis Excel", "subtitle": "Importer votre historique de dossiers de certification depuis un fichier Excel"}, {"slug": "configurer-fichiers-dossier-certification", "title": "Configurer les documents liés aux dossiers de certification", "subtitle": "Déclare les documents attendus pour chaque état des dossiers de certification"}, {"slug": "accrochage", "title": "Accrochage CDC - Génération du XML", "subtitle": "Générer votre fichier XML à destination de la CDC grâce à Wedof"}, {"slug": "statut-accrochage", "title": "Accrochage CDC - Statut d'accrochage Wedof", "subtitle": "Comprendre le statut d'accrochage"}, {"slug": "correspondance-wedof-dictionnaire", "title": "Accrochage CDC - Correspondance Wedof <-> dictionnaire XML", "subtitle": "Comprendre comment les données de Wedof servent à remplir le fichier XML"}, {"slug": "facturation", "title": "Règles de facturation", "subtitle": "Comprendre les règles de facturation des dossiers de certification"}, {"slug": "parchemin", "title": "Génération de parchemin", "subtitle": "Générer automatiquement les parchemins pour vos apprenants"}, {"slug": "espace-apprenant", "title": "Espace Candidat", "subtitle": "Facilitez la transmission d'information avec le candidat"}, {"slug": "passeport-de-competences", "title": "Passeport de Compétences", "subtitle": "Ajouter la certification au Passeport de Compétences"}, {"slug": "suivi-insertion-pro", "title": "Suivi d'insertion professionnelle", "subtitle": "<PERSON><PERSON><PERSON> le suivi d'insertion professionnelle"}, {"slug": "facturation-partenaires", "title": "Facturation Partenaires", "subtitle": "<PERSON><PERSON><PERSON> les factures entre vos partenaires"}, {"slug": "audit", "title": "Auditer vos partenaires", "subtitle": "Générer votre audit pour vos partenaires"}, {"slug": "blocs-de-competences", "title": "Blocs de compétences", "subtitle": "<PERSON><PERSON>rer les formations limitées à certains blocs"}]}, {"slug": "organismes-formation", "title": "Organisme de formation", "guides": [{"slug": "autoriser-certificateur", "title": "Traiter une demande de synchronisation automatique des dossiers", "subtitle": "Autoriser ou refuser la synchronisation automatique des dossiers à un organisme certificateur pour une certification partenaire"}, {"slug": "cycle-vie-certification-apprenant", "title": "Gérer la certification des apprenants", "subtitle": "Suivre le cycle de vie de la certification d'un apprenant"}, {"slug": "gerer-partenariats", "title": "Gérer vos partenariats de certification", "subtitle": "Recherchez des certifications et effectuez vos demandes de partenariat"}, {"slug": "configurer-fichiers-dossier-formation", "title": "Configurer les documents liés aux dossiers de formation", "subtitle": "Déclare les documents attendus pour chaque état des dossiers de formation"}, {"slug": "espace-apprenant", "title": "Espace Apprenant", "subtitle": "Facilitez la transmission d'information avec l'apprenant"}, {"slug": "deleguer-habilitation", "title": "Déléguer à Wedof l'habilitation à accéder à EDOF", "subtitle": "Déléguer une habilitation à un tiers"}, {"slug": "supprimer-habilitation", "title": "Révoquer l'habilitation à accéder à EDOF", "subtitle": "Supprimer l'habilitation de Wedof"}, {"slug": "facturation-partenaires-of", "title": "Facturation Partenaires", "subtitle": "<PERSON><PERSON><PERSON> les factures entre vos partenaire"}, {"slug": "connexion-opco-cfa", "title": "Connexion OPCO CFA", "subtitle": "Synchronisez vos contrats d'apprentissage"}, {"slug": "gerer-catalogue-edof", "title": "Gérer votre catalogue EDOF", "subtitle": "Gérez votre catalogue de formation EDOF depuis Wedof"}]}, {"slug": "utilisateurs", "title": "Utilisateurs", "guides": [{"slug": "inscription", "title": "S'inscrire sur Wedof", "subtitle": "Les différentes d'étapes du formulaire d'inscription"}, {"slug": "inscription-sur-invitation", "title": "S'inscrire sur Wedof suite à invitation", "subtitle": "Déroulement de l'inscription dans le cas d'une invitation d'un certificateur"}, {"slug": "changement-siret", "title": "Compte Wedof suite à un changement de siret", "subtitle": "Modifier le siret dans Wedof"}, {"slug": "ajouter-utilisateur", "title": "Ajouter des utilisateurs", "subtitle": "Ajouter des utilisateurs à votre organisme"}, {"slug": "filtres", "title": "Créer des filtres", "subtitle": "Gérer vos filtres"}]}, {"slug": "apprenant-candidat", "title": "Apprenant / Candidat", "guides": [{"slug": "mon-espace", "title": "Mon espace", "subtitle": "Découvrez votre espace apprenant / candidat"}]}, {"slug": "propositions", "title": "Propositions commerciales", "guides": [{"slug": "creer-proposition", "title": "C<PERSON>er une proposition", "subtitle": "Comment créer une proposition pour augmenter ou réduire le tarif d'une action de formation"}]}, {"slug": "activities", "title": "Activités", "guides": [{"slug": "activity", "title": "Activités", "subtitle": "Activités liées au dossier de formation ou au dossier de certification"}]}]}, "support": {"title": "Support", "description": "<PERSON><PERSON><PERSON> contact avec l'équipe de support"}}, "invitation": {"title": "Autoriser la synchronisation automatique de vos dossiers", "subtitle": "Vous avez été invité par l'organisme **{{organism}}** à synchroniser automatiquement vos dossiers de formation concernant les certifications pour lesquelles il est certificateur.", "subtitleTerminate": "Vous avez autorisé l'organisme **{{organism}}** à synchroniser automatiquement vos dossiers de formation concernant les certifications pour lesquelles il est certificateur.", "content": {"title": "En poursuivant, vous acceptez de partager vos dossiers selon les conditions suivantes :", "titleTerminate": "Vous avez accepté de partager vos dossiers selon les conditions suivantes :", "condition1": "Uniquement les dossiers à partir de l'état **accepté**.", "condition2": "Seules les **données administratives** des dossiers sont consultables.", "condition3": "Uniquement les dossiers liés aux certifications pour lesquelles **{{organism}}** est certificateur sont partagés.", "condition4": "Uniquement le certificateur **{{organism}}** pourra consulter les dossiers.", "condition5": "Le certificateur **{{organism}}** doit respecter le Règlement Général sur la Protection des Données (RGPD) vis à vis des Données à Caractère Personnel auxquelles vous lui donnez accès, et notamment les traiter uniquement pour la ou les seule(s) finalité(s) qui fait/font l'objet de votre partenariat."}, "button": {"refuse": "Refuser", "accept": "Accepter", "authorize": "Autoriser", "terminate": "Révoquer", "reauthorize": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "signup": {"authorize": "Autoriser la synchronisation automatique des dossiers", "refuse": "Refuser la synchronisation automatique des dossiers"}}}, "actions": {"signOut": "Se déconnecter", "cancel": "Annuler", "noFunding": "Aucun financement", "no": "Non", "false": "Non", "create": "<PERSON><PERSON><PERSON>", "createAndAnswer": "C<PERSON>er et répondre", "createAndSend": "<PERSON><PERSON><PERSON> et envoyer", "answer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "copySuccess": "La valeur a été copié dans le presse-papier.", "submit": "Envoyer", "analysing": "Analyse en cours", "edit": "Modifier", "confirm": "Confirmer", "update": "Mettre à jour", "goBack": "<PERSON><PERSON><PERSON> {{pageName}}", "terminatedDisclaimer": "Attention, l'état <b>Sortie de Formation</b> ne suffit plus, vous devez effectuer une transition d'état vers le <b>Service fait déclaré</b>.", "sendNew": "Envoyer un nouveau message", "subscriptionNotAllowedSms": "Votre abonnement ne permet pas l'envoi de SMS", "updateFromId": "Importer une pièce d'identité", "completeNir": "Importer le numéro de Sécurité Sociale", "updateFromIdDisclaimer": "Attention, avant de mettre à jour les données, assurez vous que tous les champs ont été correctement remplis. Pensez à modifier ou remplir manuellement les données si besoin.", "updateByAttendee": "Je modifie mes données", "updateDocument": "Modifier le modèle", "updateOnline": "Modifier le modèle {{type}}", "startCreatingDocument": "Commencer l'édition", "updateAnd": "Mettre à jour et ", "delete": "<PERSON><PERSON><PERSON><PERSON>", "yes": "O<PERSON>", "true": "O<PERSON>", "select": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "openLink": "<PERSON>uv<PERSON>r le lien", "close": "<PERSON><PERSON><PERSON>", "add": "Ajouter", "documentation": "Voir la documentation", "validatedField": {"email": "L'email a été validé et ne peut pas être modifié", "phoneNumber": "Le numéro de téléphone a été validé et ne peut pas être modifié"}, "activate": "Activer", "other": "<PERSON><PERSON>", "error": "Une erreur est survenue.", "deactivate": "Désactiver", "noAction": "Aucune action disponible", "updateAndActivate": "Mettre à jour et Activer", "attendeeUpdatedSuccessfully": "L'apprenant {{email}} a bien été modifié", "registrationFolderUpdatedSuccessfully": "Le dossier de formation {{folderNumber}} a bien été modifié", "certificationFolderUpdatedSuccessfully": "Le dossier de certification {{folderNumber}} a bien été modifié", "proposalUpdatedSuccessfully": "La proposition {{<PERSON><PERSON><PERSON>ber}} a bien été modifiée", "proposalDeletedSuccessfully": "La proposition a bien été supprimée", "proposalDeletedSuccessfullyMultiple": "Les propositions ont bien été supprimées", "proposalGeneriqueCreatedSuccessfully": "Une proposition générique a bien été créée et est visible dans la vue tableau. Le kanban des propositions est réservé aux propositions individuelles.", "certificationUpdatedSuccessfully": "La certification {{certificationName}} a bien été modifiée", "certificationPartnerUpdatedSuccessfully": "Le partenariat a bien été modifié", "cdcFileUpdatedSuccessfully": "Votre fichier lié à l'accrochage a bien été pris en compte", "magicLinkSent": "Un lien magique vous a bien été envoyé à l'adresse email associée à votre dossier", "magicLinkError": "Une erreur est survenue lors de l'envoi de votre lien magique. Veuillez réessayer ultérieurement.", "leavePage": "Êtes vous sûr(e) de vouloir quitter la page ? Des modifications ont été apportées mais n'ont pas été sauvegardées", "export": {"exportCsvButton": "Exporter (.csv)", "exportExcelButton": "Exporter (.xlsx)", "importExcelButton": "Importer (.xlsx)", "allColumns": "Colonnes à exporter", "exportCsvRegistrationFolders": "Exporter vos dossiers de formation au format .csv", "exportCsvCertificationFolders": "Exporter vos dossiers de certification au format .csv", "limit": "Limite de dossiers renvoyés", "exportStarted": "Export en cours, cette opération peut prendre quelques minutes", "exportFinished": "Export terminé", "preview": "Un aperçu du fichier XML a été téléchargé", "actions": "Exporter", "exportAuditReport": "Exporter les derniers résultats"}, "comingSoon": "Cette fonctionnalité arrive bientôt, encore un peu de patience", "search": {"until": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "since": "De<PERSON><PERSON> le", "sinceDate": " depuis le {{date}}", "organism": "Organisme", "state": "État", "certification": " Certification", "myCertifications": "Mes certifications", "myPartners": "Mes partenaires actifs", "filterOnStateDate": "Basé sur la date de ", "survey": "Questionnaire de suivi d'insertion professionnelle", "messageState": "État du message", "messageTemplate": {"title": "Messages et Notifications", "tooltip": "Vous n'avez aucun modèle Messages et Notifications"}, "updateFilterOnStateDate": {"certificationFolderStateDate": {"title": "Dossier de certification", "toRegister": "Passage à À enregistrer", "refused": "Passage à Refusé", "registered": "Passage à Enregistré", "toTake": "<PERSON> à Prêt à passer", "examinationDate": "Début de l'examen", "examinationEndDate": "Fin de l'examen", "enrollmentDate": "Date d'inscription", "toControl": "Passage à À contrôler", "toRetake": "Passage à À repasser", "failed": "Passage à Échoué", "aborted": "Passage à Abandonné", "success": "Passage à Réussi"}, "registrationFolderStateDate": {"title": "Dossier de formation", "notProcessed": "Passage à À traiter", "validated": "Passage à Validé", "waitingAcceptation": "Passage à Validé (En cours d'instruction Pôle emploi)", "accepted": "Passage à Accepté", "sessionStartDate": "Date de début de session", "sessionEndDate": "Date de fin de session", "inTraining": "Passage à En formation", "terminated": "Passage à Sortie de formation", "serviceDoneDeclared": "Passage à Service fait déclaré", "serviceDoneValidated": "Passage à Service fait validé", "billed": "Passage à Facturé", "canceledByAttendee": "Passage à Annulé (par le titulaire)", "canceledByAttendeeNotRealized": "Passage à Annulation titulaire (non réalisé)", "canceledByOrganism": "Passage à Annulé (par l'organisme)", "canceledByFinancer": "Passage à Annulé (par le financeur)", "refusedByAttendee": "Passage à Refus titulaire", "refusedByOrganism": "Passage à Refusé (par l'organisme)", "refusedByFinancer": "Passage à Refusé (par le financeur)", "rejectedWithoutTitulaireSuite": "Passage à Annulé sans suite", "paymentScheduledDate": "Date prévisionnelle de paiement"}}, "registrationFolderState": "État du dossier de formation", "certificationFolderState": "État du dossier de certification", "certificationFolderCdcState": {"title": "État de l'accrochage", "tooltip": "Indique où en est le dossier de certification dans le processus d'accrochage auprès de la CDC"}, "cdcCompliant": {"title": "Données apprenant complètes", "tooltip": "Indique si le dossier de certification contient les données de l'apprenant obligatoires pour l'accrochage en cas d'obtention de la certification"}, "cdcToExport": {"title": "Inclus dans les prochains accrochages", "tooltip": "Indique si le dossier de certification devra être inclus dans les prochains exports pour l'accrochage (par défaut oui, sauf si déjà accroché avec succès)"}, "cdcExcluded": {"title": "Exclu de l'accrochage", "tooltip": "Indique si le dossier de certification est exclu de l'accrochage"}, "funding": "Financement", "billingState": "État de facturation", "billNumber": "Numéro de facture", "allStates": "Tous les états", "allConformities": "Toutes les conformités", "select": "Sélectionner {{choice}}", "includes": "Contient les mots", "deleteAllFilters": "Réinitialiser", "stateLastUpdate": "<PERSON><PERSON> changement d'état", "updatedOn": "Dernière mise à jour", "wedofInvoice": "Facturable par We<PERSON>f", "lastUpdate": "Dernière mise à jour", "noValuePlaceholder": "Valable pour toutes {{choice}}", "noResult": "Aucun résultat", "training": "Formation", "trainingAction": "Action de formation", "session": "Session", "proposalCode": "Code de proposition commerciale", "controlState": "État de contrôle", "completionRate": {"title": "Taux d'avancement", "days": "Nb de jours depuis la mise à jour de l'avancement", "unit": "Jours", "tooltip": "Retrouver les dossiers pour lesquels le taux d'avancement n'a pas été mis à jour depuis plus de X jours.", "0eq": "= 0%", "25lt": "25% <", "25gte80lte": "25% < XX% < 80%", "80gt": "> 80%", "100eq": "= 100%", "nullOr80lt": "= 0% ou 80% < ou = 80%", "onCertificationFolder": "Taux d'avancement"}, "wait": "Recherche en cours...", "period": {"title": "Période", "type": {"custom": "Personnalisée", "nextYear": "<PERSON><PERSON>", "previousYear": "<PERSON><PERSON>", "currentYear": "<PERSON><PERSON>", "rollingYear": "12 derniers mois", "rollingYearFuture": "12 prochains mois", "nextMonth": "<PERSON><PERSON> prochain", "previousMonth": "<PERSON><PERSON>", "currentMonth": "<PERSON><PERSON> coura<PERSON>", "rollingMonth": "30 derniers jours", "rollingMonthFuture": "30 prochains jours", "nextWeek": "Semaine prochaine", "previousWeek": "<PERSON><PERSON><PERSON>", "currentWeek": "<PERSON><PERSON><PERSON>", "rollingWeek": "7 derniers jours", "rollingWeekFuture": "7 prochains jours", "tomorrow": "<PERSON><PERSON><PERSON>", "today": "<PERSON><PERSON><PERSON>'hui", "yesterday": "<PERSON>er"}, "typeForCertificationFolders": {"wedofInvoice": "Période de facturation Wedof en cours", "wedofQuota": "Période de quota Wedof en cours"}}, "noEntries": "Aucun résultat trouvé"}, "permalink": "Copier le lien interne Wedof", "attendeeLink": "Copier le lien vers son Espace", "surveyLink": "Copier le lien vers le suivi de l'insertion professionnelle", "adminMagicLink": "Copier le lien magic vers l'espace apprenant / candidat (admin)", "addToPassportLink": "Copier le lien vers \"Ajouter à mon Passeport de Compétences\"", "copySuccessAttendee": "Le lien a été copié dans le presse-papier.", "file": {"label": {"logo": "Logo", "certificate": "<PERSON><PERSON><PERSON><PERSON>", "certificateTemplate": "<PERSON><PERSON><PERSON><PERSON> du parchemin ({{format}})"}, "choose": "<PERSON><PERSON> ({{format}})", "replace": "Remplacer ({{format}})", "noformat": {"choose": "<PERSON><PERSON> un {{file}}", "replace": "Remplacer le {{file}}"}, "remove": "Supprimer le {{file}}", "view": "Voir {{file}}", "preview": "Télécharger un aperçu", "generatingInProgress": "Le document est en cours de génération", "generate": "<PERSON><PERSON><PERSON><PERSON>", "regenerate": "<PERSON><PERSON><PERSON><PERSON>", "updateOnline": "Modification du modèle", "autoUpdate": "Les modifications seront automatiquement sauvegardées", "pendingCreation": "Le modèle est en cours de création, veuil<PERSON><PERSON> patienter"}, "picker": {"title": "<PERSON>sir un modèle", "select": "Choi<PERSON>"}, "signDocument": {"title": "Signature du document : {{name}}", "code": "Insérer le code reçu par mail afin de signer électroniquement le document {{name}}.", "emailSent": "Un email contenant un code vient de vous être envoyé", "documentGenerating": "Le document {{name}} est en cours de génération, merci de patienter.", "documentGeneratingSigning": "Le document {{name}} est en cours de signature, merci de patienter.", "validation": "Validation et signature en cours, merci de patienter.", "success": "Merci d'avoir signé le document, merci de conserver la copie de l'exemplaire que nous vous avons transmis par mail."}, "inactif": "Inactif", "duplicate": "<PERSON><PERSON><PERSON><PERSON>"}, "table": {"pagination": {"itemsPerPage": "<PERSON><PERSON><PERSON> par page :", "firstPage": "Première page", "previousPage": "<PERSON> p<PERSON>", "nextPage": "<PERSON> suivante", "lastPage": "Dernière page", "rangePageNoResult": "0 sur {{length}}", "rangePage": "{{start}} - {{end}} sur {{length}}"}, "filters": {"globalSearch": "<PERSON><PERSON><PERSON>", "searchbar": {"organism": "Rechercher un organisme par nom, numéro de siret ou tag"}, "no-data": "Aucune donnée n'existe pour le(s) filtre(s) sélectionné(s).", "advancedSearch": {"toolTip": "Afficher les options de recherche"}}, "selection": {"all": {"select": "<PERSON><PERSON><PERSON><PERSON><PERSON> tout", "deselect": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> tout"}, "row": {"select": "Sélectionner {{name}}", "deselect": "Désélectionner {{name}}"}, "current": "Se<PERSON>s les {{currentSelectionCount}} dossiers de cette page sont sélectionnés.", "currentAll": "Tous les {{currentSelectionCount}} dossiers sont sélectionnés.", "selectAll": "Sé<PERSON>ionner les {{total}} dossiers.", "selectAllForStateFolders": "Sélectionner des dossiers dans le même état pour afficher les actions", "selectAllForStateProposals": "Sélectionner des propositions dans le même état pour afficher les actions", "deselectAll": "Effacer la sélection.", "update": "Mettre à jour la sélection", "selectState": {"notProcessed": "<PERSON> traiter", "validated": "<PERSON><PERSON><PERSON>", "accepted": "Accepté", "inTraining": "En formation", "terminated": "Sortie de formation"}}}, "kanban": {"title": "Ka<PERSON><PERSON>", "card": {"no-dataFolder": "Aucun dossier", "no-dataProposal": "Aucune proposition"}}, "errors": {"max": "Le maximum est {{value}}", "min": "Le minimum est {{value}}", "password": {"verification": "Les deux mots de passe indiqués ne correspondent pas"}, "required": "Ce champ est obligatoire", "url": "Ce champ doit être une URL valide", "date": "Vérifier la cohérence des dates", "hours": "Vérifier la cohérence des heures de formation", "discountType": {"percent": "La variation en % doit être compris entre -100% et +15% du montant initial"}, "minlength": "<PERSON><PERSON> <PERSON> renseigner {{number}} chiffres", "timeWarning": "Attention cette opération peut prendre jusqu'à une minute. Veuillez ne pas recharger la page.", "initializing": "Vos dossiers en provenant de {{dataProviders}} sont en cours de synchronisation. Cette opération peut prendre plusieurs minutes", "wrongFormat": "Le modèle du fichier ne respecte pas le type de fichier attendu."}, "confirmation": {"action": {"title": "Confirmation"}, "deletion": {"title": "Suppression"}, "reinitialization": {"title": "Réinitialisation"}}, "token": {"requestCountTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON> consomm<PERSON>"}, "metadatas": {"title": "Donn<PERSON>", "create": "Ajouter une donnée personnalisée", "subtitle": "Données personnalisées associés à {{entity}}. Ces données sont utilisables via l'API et pourront être utilisées dans les Messages et Notifications.", "label": {"key": "Nom du champ", "value": "<PERSON><PERSON>"}, "placeholder": "Nom du champ", "showCreateText": "C<PERSON>er : ", "showSearchingText": "Rechercher le nom d'un champ", "confirmDeletion": "Êtes-vous sûr(e) de vouloir supprimer la donnée personnalisée {{value}} ?"}, "matching": {"contains": "Contient le texte", "containsAll": "Contient tous les mots", "notContains": "Ne contient pas le texte", "notContainsAny": "Ne contient aucun des mots", "matches": "Correspond à l'expression régulière", "eqs": "Est égal à", "neqs": "N'est pas égal à", "eq": "Est égal à", "neq": "N'est pas égal à", "lt": "Plus petit que", "lte": "Plus petit que ou égal à", "gt": "Plus grand que", "gte": "Plus grand que ou égal à", "isTrue": "O<PERSON>", "isFalse": "Non", "ai": "Valider par IA", "title": {"contains": "doit contenir le texte", "containsAll": "doit contenir tous les mots", "notContains": "ne doit pas contenir le texte", "notContainsAny": "ne doit contenir aucun des mots", "matches": "doit correspondre à l'expression régulière", "eqs": "doit être égal à", "neqs": "ne doit pas être égal à", "eq": "doit être égal à", "neq": "ne doit pas être égal à", "lt": "doit être inférieur à", "lte": "doit être inférieur ou égal à", "gt": "doit être supérieur à", "gte": "doit être supérieur ou égal à", "isTrue": "doit être ", "isFalse": "doit être ", "containsArray": "doit être ", "notContainsArray": "doit être ", "ai": "doit être validé par IA"}, "help": {"contains": "Le critère sera conforme si l'ensemble du texte est présent tel quel dans la donnée auditée (indépendamment de la casse)", "containsAll": "Utilisez la virgule pour séparer les mots. Le critère sera conforme si l'ensemble des mots sont présents dans la donnée auditée (indépendamment de la casse)", "notContains": "Le critère sera non conforme si l'ensemble du texte est présent tel quel dans la donnée auditée (indépendamment de la casse)", "notContainsAny": "Utilisez la virgule pour séparer les mots. Le critère sera non conforme si un ou plusieurs des mots sont présents dans la donnée auditée (indépendamment de la casse)", "matches": "Les expressions régulières (ou regex) sont un langage informatique permettant d'exprimer des contraintes complexes, réservé à des utilisateurs techniques. Par dé<PERSON><PERSON>, insensible à la casse."}}, "tags": {"label": "Tags", "placeholder": "Ajouter un tag", "create": "<PERSON><PERSON><PERSON> le tag ", "added": {"label": "Ajouter des tags", "placeholder": "Tags à ajouter"}, "removed": {"label": "Supprimer des tags", "placeholder": "Tags à supprimer"}}, "trialEnd": "Période de fin d'essai : "}, "public": {"funnel": {"merchant": {"connectedAs": "Connecté en tant que {{name}}", "proposal": {"states": {"expired": "Expirée", "draft": "Brouillon", "active": "Active", "viewed": "<PERSON><PERSON>", "accepted": "Acceptée", "refused": "<PERSON><PERSON><PERSON><PERSON>", "cancelled": "<PERSON><PERSON><PERSON>"}}, "create": "<PERSON><PERSON><PERSON>", "update": "Mettre à jour", "createAndActive": "<PERSON><PERSON><PERSON> et activer", "updateAndActive": "Mettre à jour et activer", "send": "Partager par email", "active": "Activer", "draft": "Mettre en brouillon", "refused": "<PERSON><PERSON><PERSON><PERSON><PERSON> comme refusée", "cancelled": "<PERSON><PERSON><PERSON><PERSON><PERSON> comme annulée", "phoneNumberTooltip": "<PERSON><PERSON><PERSON> le numéro de téléphone associé au compte Mon Compte Formation de l'apprenant", "emailTooltip": "Indiquez l'email associée au compte Mon Compte Formation de l'apprenant", "steps": {"quoteData": "Création de la proposition", "quoteStatus": "Suivi de la <PERSON>", "validateFolder": "Validation du dossier", "verificationFolder": "Vérification du dossier", "acceptFolder": "Acceptation du dossier", "acceptedFolder": "Inscription confirmée", "acceptedFolderTextNoSessionDate": "Bravo ! {{firstName}} {{lastName}} a confirmé son inscription à la formation {{title}} à {{totalIncl}}€ TTC", "acceptedFolderText": "Bravo ! {{firstName}} {{lastName}} a confirmé son inscription à la formation {{title}} du {{sessionStartDate}} au {{sessionEndDate}} à {{totalIncl}}€ TTC"}, "spinner": {"waitingFolder": "En attente de la création du dossier par {{firstName}} {{lastName}}", "waitingFolder-onlyEmail": "En attente de la création du dossier par {{email}}", "validatingFolder": "Validation du dossier n°{{externalId}}...", "waitingVerificationFolder": "En attente de la vérification du dossier n°{{externalId}}...", "waitingAcceptationFolder": "En attente de l'acceptation du dossier n°{{externalId}} par l'apprenant"}, "trainingActions": {"title": "Actions de formation", "placeholder": "Sélectionner une ou plusieurs actions de formation", "noTrainingAction": "Aucune action ne correspond à votre recherche"}, "sessionDates": {"title": "Modifier les dates de sessions", "yes": "O<PERSON>", "no": "Non", "sessionStartDate": "Date de début de session", "sessionEndDate": "Date de fin de session", "errors": {"sessionStartDate": "Vérifier la cohérence de votre date", "sessionEndDate": "Vérifier la cohérence de votre date"}}, "discountType": {"title": "Modifier le prix", "none": "Non", "percent": "Variation en %", "fixed": "Nouveau prix", "amount": "Variation en €", "decrease": "Réduction de ", "increase": "Augmentation de ", "errors": {"percent": "La variation en % doit être un % compris entre +15% et -100% du montant initial", "fixed": "Vérifier le montant (<= +15% par rapport au montant initial, montant >= 0€)", "amount": "La variation en € doit être un nombre et doit être comprise entre +15% et -100% du montant initial"}}, "notes": {"title": "Notes privées", "placeholder": "Description de la proposition, usage interne"}, "description": {"title": "Message public", "placeholder": "Ajouter un message qui sera affiché à l'apprenant"}}, "closed": "Proposition refusée ou expirée", "firstName": "Prénom", "lastName": "Nom", "phoneNumber": "Numéro de téléphone", "phoneNumberTooltip": "<PERSON><PERSON><PERSON> le numéro de téléphone associé à votre compte Mon Compte Formation", "email": "<PERSON><PERSON><PERSON> email", "emailTooltip": "Indiquez l'email associé à votre compte Mon Compte Formation", "code": "Code proposition", "trainingAction": "Choix de votre formation", "validate": "Valider vos coordonnées", "check": "Vérifier le code", "select": "Sélectionner cette formation", "gotToMCF": "Créer votre dossier avec Mon Compte Formation (CPF)", "waitingFolder": "En attente de la réception de votre dossier", "receivedFolder": "Demande d'inscription n°", "receivedFolderSuffix": "reçue", "processingFolder": "Validation de votre demande", "waitingValidation": "En attente de la vérification de votre dossier n°", "waitingAcceptationFolder": "En attente de votre confirmation", "accept": "Accepter votre dossier", "acceptedFolder": "Inscription confirmée - Dossier de formation n°", "errors": {"firstName": "<PERSON><PERSON><PERSON> votre prénom", "lastName": "<PERSON><PERSON><PERSON> votre nom", "email": "Indiquez une adresse mail valide", "phoneNumber": "Indiquez un numéro de téléphone valide", "code": "Veuillez renseigner un code de 5 à 15 caractères (a-zA-Z0-9_)", "trainingAction": "Formation invalide"}, "steps": {"attendee": "Vos co<PERSON>ées", "code": "Récupération de votre proposition", "trainingAction": "Selection de votre formation", "folder": "Création de votre dossier de formation", "process": "Validation de votre dossier", "accept": "Confirmation de votre inscription", "finished": "Votre inscription est confirmée", "verification": "Verification de votre dossier"}, "placeholder": {"noTrainingAction": "Aucune formation trouvée", "trainingAction": "Rechercher par code ou nom de la formation"}}, "passport": {"competences": {"titleSuccess": "Ajouter votre certification à votre Passeport de Compétences", "titlePendingSuccess": "Préparer les informations de votre Passeport de Compétences", "subtitle": "Découvrir à quoi sert le Passeport de Compétences", "success": {"notSuccess": "Vos informations sont enregistrées. Votre certification sera dans votre Passeport de Compétences en cas de succès à l'examen de certification", "processedOk": "Félicitations ! Votre certification est disponible dans votre Passeport de Compétences.", "exported": "Félicitations ! Votre certification est en cours d'ajout sur votre Passeport de Compétences.", "success": "Félicitations ! Votre certification sera bientôt disponible dans votre Passeport de Compétences."}}, "prevention": {"titleSuccess": "Ajouter votre formation et les compétences transférables à votre Passeport de Prévention", "titlePendingSuccess": "Préparer les informations de votre Passeport de Prévention", "subtitle": "Découvrir à quoi sert le Passeport de Prévention", "success": "Vos informations sont enregistrées. Votre formation sera disponible dans votre Passeport de Prévention"}, "connect": {"competences": "Découvrir mon Passeport de Compétences", "prevention": "Découvrir mon Passeport de Prévention"}, "form": {"label": "Votre n° de dossier de Certification", "button": {"id360": "L'Identité Numérique", "identificationDocument": "Utiliser votre carte d'identité"}, "explanationSuccess": "<p><b>Félicitations !</b> Vous avez obtenu la certification <b>{{certificationName}}</b>.</p><br/><p>Celle-ci est reconnue par l'État (France Compétences) et doit être ajoutée à votre <a href=\"https://competences.moncompteformation.gouv.fr/espace-public/\" target=\"_blank\" rel=\"noopener noreferrer\"><b>Passeport de Compétences</b></a>.</p><br/><p>Connectez vous à l'aide d'une pièce d'identité ou de votre Identité Numérique pour l'ajouter.</p>", "explanationPendingSuccess": "<p>Vous êtes en train de préparer le passage de la certification : <b>{{certificationName}}</b>.</p><br/><p>Anticipez votre réussite en complétant vos informations personnelles nécessaires à l'ajout de votre certification à votre <a href=\"https://competences.moncompteformation.gouv.fr/espace-public/\" rel=\"noopener noreferrer\" target=\"_blank\"><b>Passeport de Compétences</b></a>.</p><br/><p>Connectez vous à l'aide d'une pièce d'identité ou de votre Identité Numérique pour l'ajouter.</p>"}, "errors": {"unmatching": "<PERSON><PERSON><PERSON>, les données envoyées ne correspondent pas au candidat associé au dossier de certification", "restartForm": "Je recommence", "unmatchingForm": "<PERSON><PERSON><PERSON>, les données que vous avez saisies ne correspondent pas au candidat associé au dossier", "filesize": "Taille du document dépassée (max 4mo)", "internalError": "<PERSON><PERSON><PERSON>, identification incorrecte", "wrongData": "<PERSON><PERSON><PERSON>, donn<PERSON>", "id360Error": "Erreur de connexion avec id360"}, "confirmData": {"competences": {"descriptionSuccess": "Les données suivantes sont nécessaires afin d'ajouter la certification <b>{{certificationName}}</b> dans votre <a href=\"https://competences.moncompteformation.gouv.fr/espace-public/\" target=\"_blank\" rel=\"noopener noreferrer\"> Passeport de Compétences</a>", "descriptionPendingSuccess": "Les données suivantes seront nécessaires afin d'ajouter la certification <b>{{certificationName}}</b> dans votre <a href=\"https://competences.moncompteformation.gouv.fr/espace-public/\" target=\"_blank\" rel=\"noopener noreferrer\"> Passeport de Compétences</a> lors de son obtention"}, "prevention": {"descriptionSuccess": "Les données suivantes sont nécessaires afin d'ajouter la formation dans votre <a href=\"https://passeport-prevention.travail-emploi.gouv.fr\" target=\"_blank\" rel=\"noopener noreferrer\"> Passeport de Prévention</a>", "descriptionPendingSuccess": "Les données suivantes seront nécessaires afin d'ajouter la formation dans votre <a href=\"https://passeport-prevention.travail-emploi.gouv.fr\" target=\"_blank\" rel=\"noopener noreferrer\"> Passeport de Prévention</a> lors de son obtention"}, "confirmButton": "Valider les informations", "updateButton": "Modifier les informations", "nirMandatory": {"competences": "Le numéro de Sécurité Sociale est une donnée <b>obligatoire</b> afin d'ajouter la certification <b>{{certificationName}}</b> dans votre <a href=\"https://competences.moncompteformation.gouv.fr/espace-public/\" target=\"_blank\" rel=\"noopener noreferrer\"> Passeport de Compétences</a>", "prevention": "Le numéro de Sécurité Sociale est une donnée <b>obligatoire</b> afin d'ajouter votre formation dans votre <a href=\"https://passeport-prevention.travail-emploi.gouv.fr\" target=\"_blank\" rel=\"noopener noreferrer\"> Passeport de Prévention</a>"}, "form": {"firstName2": "2ème prénom", "firstName3": "3ème prénom", "title": {"civility": "", "contact": "Coordonnées", "lastNames": "Noms", "firstNames": "Prénoms", "birthday": "Naissance", "nir": "Numéro de sécurité sociale"}}}}, "reseller": {"wedofDescription": "Wedof aide les organismes de formation à faciliter l'usage de [MonCompteFormation.gouv.fr](https://www.moncompteformation.gouv.fr) et l'espace [EDOF](www.of.moncompteformation.gouv.fr) avec les fonctionnalités suivantes :\n*   **Intégrer** les données de [EDOF](www.of.moncompteformation.gouv.fr) dans vos outils CRM, LMS, de facturation / comptabilité de formation\n*   **Automatiser** les activités liées aux dossiers de formation, à votre catalogue d'actions de formation et aux paiements\n*   **Contrôler** la visibilité du catalogue", "resellerDescription": "{{reseller}} utilisera les services de Wedof pour collecter et mettre à jour les données de votre compte EDOF et ainsi gérer les dossiers de formation.", "title": "Synchronisation EDOF", "noResellerAssociated": "<PERSON><PERSON><PERSON>, l'organisme n'est pas associé au reseller", "form": {"msa": "J'accepte les Conditions Générales d'Utilisation", "rgpd": "J'accepte les Règles de confidentialité et de gestion des données personnelles", "button": "Commencer"}}}, "auth": {"sign-in": {"title": "Connexion", "titleAttendee": "Bienvenue dans votre Espace", "subtitle": {"no-account": "Pas encore de compte ?", "registration": "Inscription"}, "form": {"email": {"label": "<PERSON><PERSON><PERSON> email"}, "password": {"label": "Mot de passe"}, "remember-me": "Se souvenir de moi", "forgot-password": "Mot de passe oublié ?", "send-magic-link": "Ou recevoir un lien magique d'authentification", "submit": {"label": "Connexion"}}, "welcome": {"title": "<PERSON><PERSON><PERSON>", "subtitle": "Piloter vos activités sur EDOF", "description": "Wedof aide les organismes de formation et les organismes certificateurs à faciliter l'usage de [MonCompteFormation.gouv.fr](https://www.moncompteformation.gouv.fr) et l'espace [EDOF](www.of.moncompteformation.gouv.fr) avec les fonctionnalités suivantes :\n*   **Intégrer** les données de [EDOF](www.of.moncompteformation.gouv.fr) dans vos outils CRM, LMS, de facturation / comptabilité de formation\n*   **Automatiser** les activités liées aux dossiers de formation, à votre catalogue d'actions de formation et aux paiements\n*   **Contrôler** la visibilité du catalogue", "learn-more": "En savoir plus"}, "attendee": {"form": {"folder": {"label": "Votre n° de dossier {{domain}}"}}}}, "sign-up": {"insights": {"insights-1": "15 jours d'essai <b><PERSON><PERSON><PERSON></b>", "insights-2": "Sans carte bancaire", "insights-3": "Sans engagement"}, "title": {"text": "<PERSON><PERSON><PERSON> votre compte", "span": "<PERSON><PERSON><PERSON>"}, "image": {"src": "assets/images/logo/logo.svg", "alt": "logo"}, "partnership": {"text": "Création d'un compte utilisateur par le partenaire : <b>{{partnership}}</b>", "text2": "La gestion de ce compte sera délégué au partenaire. Wedof ne communiquera pas avec cet utilisateur sauf <b>en cas d'urgence</b>."}, "stepInvitation": {"title": "Autorisation"}, "step1": {"title": "Utilisa<PERSON>ur", "subtitle": "<PERSON><PERSON>ez votre compte utilisateur : indiquez vos informations personnelles", "labels": {"first-name": "Prénom", "last-name": "Nom", "email": "<PERSON><PERSON><PERSON> email", "phone": "Téléphone", "password": "Mot de passe", "password-verification": "Vérification mot de passe"}, "placeholders": {"first-name": "Prénom", "last-name": "Nom", "email": "<PERSON><PERSON><PERSON> email", "phone": "Numéro de téléphone", "password": "Mot de passe", "password-verification": "Répétez votre mot de passe"}, "button": {"back": "Précédent", "next": "Rechercher votre organisme"}, "errors": {"first-name": "<PERSON><PERSON><PERSON> votre prénom", "last-name": "<PERSON><PERSON><PERSON> votre nom", "email": "Indiquez une adresse mail valide", "phone": "Le numéro de téléphone n'est pas valide", "password": "<PERSON><PERSON><PERSON> votre mot de passe (6 caractères min.)", "user-exists": "Un utilisateur avec cette adresse électronique existe déjà."}}, "step2": {"title": "Organisme", "subtitleInvitation": "Informations concernant votre organisme", "subtitle": "Recherchez votre organisme avec votre numéro de SIRET", "labels": {"siret": "Numéro de SIRET", "siretTooltip": "Certificateur : <PERSON><PERSON><PERSON><PERSON> le SIRET sur lequel sont déclarées vos certifications sur France Compétences", "agreement": "Numéro de déclaration d'activité (si organisme de formation)", "organism": "Organisme", "address": "<PERSON><PERSON><PERSON>", "phone": "Téléphone", "city": "Ville", "postcode": "Code Postal"}, "placeholders": {"siret": "<PERSON><PERSON><PERSON><PERSON>", "agreement": "Numéro de déclaration d'activité", "organism": "Organisme", "address": "<PERSON><PERSON><PERSON>", "phone": "Téléphone", "city": "Ville", "postcode": "Code Postal"}, "checkboxes": {"is-certifier-organism": "Est un organisme certificateur", "is-training-organism": "Est un organisme de formation"}, "button": {"back": "Précédent", "next": "Finaliser mon inscription"}, "errors": {"siret": "<PERSON><PERSON>quez votre numéro SIRET à 14 chiffres", "agreement": "Le numéro d'activité doit contenir entre 10 et 11 chiffres", "organism-unknown": "Organisme inconnu", "user-exists-for-organism": "Il existe déjà un compte utilisateur principal inscrit sur Wedof pour cet organisme : <b>{{ownerEmail}}</b>.<br/>Vous pouvez soit demander à son propriétaire de vous ajouter sur l'organisme, soit <span class='underline cursor-pointer'>directement vous connecter</span> si vous avez les identifiants de ce compte."}, "resendInvitation": {"start": "Vous avez reçu récemment <b>une invitation à rejoindre Wedof en tant que partenaire</b> de l'un de nos organismes certificateurs. Si vous vous inscrivez dans le cadre de ce partenariat merci de continuer votre inscription à partir du <b>lien fourni dans votre email d'invitation</b> de votre certificateur. ", "link": "Recevoir à nouveau l'email d'invitation", "end": ". Dans tous les autres cas vous pouvez continuer et profiter d'une offre d'essai gratuite à l'issue de votre inscription.", "invitation-resent": "L'email d'invitation a été renvoyé à l'adresse <b>{{obfuscatedEmail}}</b>. <br>Redirection vers la page de connexion..."}}, "step3": {"title": "EDOF", "subtitle-invited": "Afin de nous permettre de synchroniser notre plateforme avec le site MonCompteFormation (EDOF), veuillez suivre cette procèdure pour déleguer une habilitation à Wedof. La délégation d'habilitation à Wedof permettra d'activer la synchronisation entre Wedof et EDOF sans que vous ne soyez obligé de fournir vos identifiants Mes Démarches à Wedof.", "subtitle": "Afin de nous permettre de synchroniser notre plateforme avec le site MonCompteFormation (EDOF), veuillez suivre cette procèdure pour déleguer une habilitation à Wedof. La délégation d'habilitation à Wedof permettra d'activer la synchronisation entre Wedof et EDOF sans que vous ne soyez obligé de fournir vos identifiants Mes Démarches à Wedof.", "image": {"src": "assets/images/sign-up/sign-up.png", "alt": "logo EDOF"}, "checkboxes": {"title": "Pour terminer votre inscription, veuillez cocher les cases suivantes :", "msa": {"text": "J'accepte les Conditions Générales d'Utilisation", "link": "Conditions générales"}, "rgpd": {"text": "J'accepte les Règles de confidentialité et de gestion des données personnelles", "link": "Règles de confidentialité"}}, "button": {"next": "Accéder à mon compte", "associate": "Déleguer une habilitation à Wedof", "associationDone": "Habilitation EDOF active"}, "errors": {"login-EDOF": "<PERSON><PERSON>quez votre identifiant EDOF (email)", "password-EDOF": "<PERSON><PERSON>quez votre mot de passe EDOF"}}}, "sign-out": {"title": "Vous avez été déconnecté !", "subtitle": "Vous serez redirigé dans", "redirect": "Vous êtes actuellement redirigé vers la page d'accueil", "link": {"text1": "Se diriger vers", "text2": "le formulaire de connexion"}}, "forgot-password": {"title": "Mot de passe oublié ?", "subtitle": "Renseignez le formulaire pour recevoir un nouveau mot de passe.", "email": "<PERSON><PERSON><PERSON> email", "reset-password-button": "Recevoir un nouveau mot de passe", "return-to-caption": "Retour vers", "return-to-button": "connexion", "password-sent": "Un nouveau mot de passe vous a été envoyé si l'adresse mail est bien associée à un compte Wedof.", "password-sent-fail": "Une erreur est survenue lors de l'envoi de votre nouveau mot de passe. Veuillez réessayer ultérieurement.", "errors": {"email-required": "Une adresse email est obligatoire.", "email-invalid": "<PERSON><PERSON><PERSON> une adresse email valide.", "email-unknown": "Aucun utilisateur associé à cette adresse email."}}, "send-magic-link": {"title": "Utiliser un lien magique", "subtitle": "Renseignez l'email de votre compte, nous vous enverrons un lien magique !", "email": "<PERSON><PERSON><PERSON> email", "send-magic-link-button": "Recevoir mon lien magique", "return-to-caption": "Retour vers", "return-to-button": "connexion", "magic-link-sent": "Un lien magique vous a été envoyé si l'adresse mail est bien associée à un compte Wedof.", "magic-link-fail": "Une erreur est survenue lors de l'envoi de votre lien magique. Veuillez réessayer ultérieurement.", "errors": {"email-required": "Une adresse email est obligatoire.", "email-invalid": "<PERSON><PERSON><PERSON> une adresse email valide."}}, "connection": {"activateSyncButton": "Activer la synchronisation", "activateHabilitateButton": "Habiliter <PERSON>dof", "reActivateSyncButton": "Réactiver la synchronisation", "activeSync": "Synchronisation active avec {{username}}", "noHabilitation": "Aucune habilitation", "genericError": "Une connexion présente un problème.", "existAtDataProvider": "Une habilitation est possible, mais celle-ci n'a pas été initialisée, a échoué ou a été révoquée.", "state": {"habilitation": {"active": "Synchronisation active avec une habilitation", "inactive": "Habiliter <PERSON>dof", "refreshing": "Habilitation en cours d'actualisation", "revoked": "Habilitation révoquée", "inProgress": "Habilitation en cours de traitement", "failed": "Habilitation en échec"}, "delegation": {"active": "Synchronisation active avec {{username}}", "inactive": "Activer la synchronisation", "refreshing": "En cours d'actualisation", "revoked": "Synchronisation désactivée", "inProgress": "Synchronisation en cours", "failed": "Synchronisation échouée avec {{username}}"}}, "header": {"logos": {"wedof": "logo wedof"}}, "form": {"email": {"label": "<PERSON><PERSON><PERSON> / Nom d'utilisateur ({{dataProvider}})", "placeholder": "<PERSON><PERSON><PERSON> / Nom d'utilisateur ({{dataProvider}})", "error": "<PERSON><PERSON><PERSON> votre identifiant {{dataProvider}}"}, "password": {"label": "Mot de passe ({{dataProvider}})", "placeholder": "Mot de passe ({{dataProvider}})", "error": "<PERSON><PERSON><PERSON> votre mot de passe {{dataProvider}}"}, "button": {"text": "Associer"}, "errors": {"wrong-login-password": "Mauvais identifiant / mot de passe. Vous avez oublié votre mot de passe {{dataProviderName}} ? <a href='{{dataProviderLink}}'>Cliquez ici pour le réinitialiser</a>", "reset-password": "Votre mot de passe {{dataProviderName}} a expiré. <a href='{{dataProviderLink}}'>Cliquez ici pour en définir un nouveau</a>", "no-organism-access": "Ce compte {{dataProvider}} n'est pas actif ou n'a pas accès à l'organisme {{organismeName}}", "other": "<PERSON><PERSON><PERSON>, la connexion n'a pas fonctionné, vérifiez les données fournies", "unknown-error": "Une erreur est survenue, veuillez réessayer plus tard."}, "habilitation": {"auto": "Créer une habilitation automatiquement"}, "delegation": {"auto": "Créer une délégation automatiquement"}}}, "cpf": {"name": "EDOF - Mon Compte Formation", "subtitle": "", "authorizations": {"organism-informations_read": {"title": "Lire les informations de votre organisme", "text": "Afficher vos <b>informations</b>", "limitedUsage": ""}, "payments_read": {"title": "Lire vos règlements vers la caisse des dépôts", "text": "Lister vos <b>informations de règlements</b>", "limitedUsage": "<b>Non utilisé</b> avec un compte partenaire"}, "trainings": {"title": "Lire/<PERSON><PERSON><PERSON>/Modifier/Supprimer des formations", "text": "Gérer vos <b>formations</b>", "limitedUsage": "Uniquement en <b>lecture</b> avec un compte partenaire"}, "folders": {"title": "Lire/<PERSON><PERSON>ter/Modifier des dossiers", "text": "<PERSON><PERSON><PERSON> vos <b>dossiers</b>", "limitedUsage": "Uniquement en <b>lecture</b> avec un compte partenaire"}, "training-actions": {"title": "Lire/<PERSON><PERSON><PERSON>/Modifier/Supprimer des actions de formation", "text": "Gérer vos <b>actions de formation</b>", "limitedUsage": "Uniquement en <b>lecture</b> avec un compte partenaire"}, "sessions": {"title": "Lire/<PERSON><PERSON><PERSON>/Modi<PERSON>r/Supprimer des sessions de formation", "text": "G<PERSON>rer vos <b>sessions de formations</b>", "limitedUsage": "Uniquement en <b>lecture</b> avec un compte partenaire"}}, "error": {"credentials": "Votre email / mot de passe est incorrect. (email : {{value1}})", "siret": "Votre compte ne permet pas d'habiliter Wedof pour l'organisme avec le n° siret : {{value1}} mais uniquement l'organisme avec le n° siret : {{value2}}. Veuillez suivre le guide en cliquant sur ce lien : <a href='/assistance/guides/organismes-formation/deleguer-habilitation' target='_blank'>Modifier son siret d'appartenance</a>.", "gestionnaire": "Votre compte ne dispose pas des droits gestionnaire pour la démarche EDOF. Ces droits sont nécessaire pour créer une habilitation pour Wedof. Merci d'utiliser un autre compte avec les droits gestionnaire sur la démarche EDOF ou de suivre le guide : <a href='/assistance/guides/organismes-formation/deleguer-habilitation' target='_blank'>Modifier son organisme d'appartenance</a>.", "other": "Une erreur est survenue merci de contacter l'équipe <PERSON>dof", "efp": "Procédez à une habilitation manuelle sur EFP Connect tel qu'expliqué dans notre documentation"}, "confirmHabilitationText": "<p class=\"mt-2 mb-2\">\nVous allez habiliter Wedof à la démarche EDOF. Après avoir accepté, Wedof aura accès au service en votre nom et vous recevrez par mail une confirmation de cette acceptation de la part du service mesdemarches.emploi.gouv.fr. Vous pourrez révoquer cette\ndélégation ultérieurement directement sur le portail mesdemarches.emploi.gouv.fr.\n</p>\n\n<p class=\"italic mb-5\">\nL'habilitation sera donnée à un compte dédié à Wedof au nom de \"<PERSON> (Wedof)\"\n</p>", "documentation": "Effectuer une habilitation manuelle"}, "opcoCfaAtlas": {"name": "OPCO Atlas (Apprentissage)", "subtitle": "", "authorizations": {"organism-informations_read": {"title": "Lire les informations de votre organisme", "text": "Afficher vos <b>informations</b>"}, "folders": {"title": "Accéder aux informations des dossiers de demandes de financement", "text": "Lire vos <b>dossiers</b>"}}, "error": {"other": "Une erreur est survenue merci de contacter l'équipe <PERSON>dof", "notfound": "<PERSON><PERSON><PERSON>, la clé d'API n'est pas reconnue par l'OPCO Atlas", "expired": "<PERSON><PERSON><PERSON>, la clé d'API a expiré"}, "documentation": "Où trouver ma clé API ?"}, "opcoCfaAfdas": {"name": "OPCO Afdas (Apprentissage)", "subtitle": "", "authorizations": {"organism-informations_read": {"title": "Lire les informations de votre organisme", "text": "Afficher vos <b>informations</b>"}, "folders": {"title": "Accéder aux informations des dossiers de demandes de financement", "text": "Lire vos <b>dossiers</b>"}}, "error": {"other": "Une erreur est survenue merci de contacter l'équipe <PERSON>dof", "notfound": "<PERSON><PERSON><PERSON>, la clé d'API n'est pas reconnue par l'OPCO Afdas", "expired": "<PERSON><PERSON><PERSON>, la clé d'API a expiré"}, "documentation": "Où trouver ma clé API ?"}, "opcoCfaEp": {"name": "OPCO EP (Apprentissage)", "subtitle": "", "authorizations": {"organism-informations_read": {"title": "Lire les informations de votre organisme", "text": "Afficher vos <b>informations</b>"}, "folders": {"title": "Accéder aux informations des dossiers de demandes de financement", "text": "Lire vos <b>dossiers</b>"}}, "error": {"other": "Une erreur est survenue merci de contacter l'équipe <PERSON>dof", "notfound": "<PERSON><PERSON><PERSON>, la clé d'API n'est pas reconnue par l'OPCO EP", "expired": "<PERSON><PERSON><PERSON>, la clé d'API a expiré"}, "documentation": "Où trouver ma clé API ?"}, "opcoCfaMobilites": {"name": "OPCO Mobilités (Apprentissage)", "subtitle": "", "authorizations": {"organism-informations_read": {"title": "Lire les informations de votre organisme", "text": "Afficher vos <b>informations</b>"}, "folders": {"title": "Accéder aux informations des dossiers de demandes de financement", "text": "Lire vos <b>dossiers</b>"}}, "error": {"other": "Une erreur est survenue merci de contacter l'équipe <PERSON>dof", "notfound": "<PERSON><PERSON><PERSON>, la clé d'API n'est pas reconnue par l'OPCO Mobilités", "expired": "<PERSON><PERSON><PERSON>, la clé d'API a expiré"}, "documentation": "Où trouver ma clé API ?"}, "opcoCfaAkto": {"name": "OPCO Akto (Apprentissage)", "subtitle": "", "authorizations": {"organism-informations_read": {"title": "Lire les informations de votre organisme", "text": "Afficher vos <b>informations</b>"}, "folders": {"title": "Accéder aux informations des dossiers de demandes de financement", "text": "Lire vos <b>dossiers</b>"}}, "error": {"other": "Une erreur est survenue merci de contacter l'équipe <PERSON>dof", "notfound": "<PERSON><PERSON><PERSON>, la clé d'API n'est pas reconnue par l'OPCO Akto", "expired": "<PERSON><PERSON><PERSON>, la clé d'API a expiré"}, "documentation": "Où trouver ma clé API ?"}, "opcoCfaOcapiat": {"name": "OPCO Ocapiat (Apprentissage)", "subtitle": "", "authorizations": {"organism-informations_read": {"title": "Lire les informations de votre organisme", "text": "Afficher vos <b>informations</b>"}, "folders": {"title": "Accéder aux informations des dossiers de demandes de financement", "text": "Lire vos <b>dossiers</b>"}}, "error": {"other": "Une erreur est survenue merci de contacter l'équipe <PERSON>dof", "notfound": "<PERSON><PERSON><PERSON>, la clé d'API n'est pas reconnue par l'OPCO Ocapiat", "expired": "<PERSON><PERSON><PERSON>, la clé d'API a expiré"}, "documentation": "Où trouver ma clé API ?"}, "opcoCfaUniformation": {"name": "OPCO Uniformation (Apprentissage)", "subtitle": "", "authorizations": {"organism-informations_read": {"title": "Lire les informations de votre organisme", "text": "Afficher vos <b>informations</b>"}, "folders": {"title": "Accéder aux informations des dossiers de demandes de financement", "text": "Lire vos <b>dossiers</b>"}}, "error": {"other": "Une erreur est survenue merci de contacter l'équipe <PERSON>dof", "notfound": "<PERSON><PERSON><PERSON>, la clé d'API n'est pas reconnue par l'OPCO Uniformation", "expired": "<PERSON><PERSON><PERSON>, la clé d'API a expiré"}, "documentation": "Où trouver ma clé API ?"}, "opcoCfa2i": {"name": "OPCO Opco2i (Apprentissage)", "subtitle": "", "authorizations": {"organism-informations_read": {"title": "Lire les informations de votre organisme", "text": "Afficher vos <b>informations</b>"}, "folders": {"title": "Accéder aux informations des dossiers de demandes de financement", "text": "Lire vos <b>dossiers</b>"}}, "error": {"other": "Une erreur est survenue merci de contacter l'équipe <PERSON>dof", "notfound": "<PERSON><PERSON><PERSON>, la clé d'API n'est pas reconnue par l'OPCO 2i", "expired": "<PERSON><PERSON><PERSON>, la clé d'API a expiré"}, "documentation": "Où trouver ma clé API ?"}, "opcoCfaConstructys": {"name": "OPCO Constructys (Apprentissage)", "subtitle": "", "authorizations": {"organism-informations_read": {"title": "Lire les informations de votre organisme", "text": "Afficher vos <b>informations</b>"}, "folders": {"title": "Accéder aux informations des dossiers de demandes de financement", "text": "Lire vos <b>dossiers</b>"}}, "error": {"other": "Une erreur est survenue merci de contacter l'équipe <PERSON>dof", "notfound": "<PERSON><PERSON><PERSON>, la clé d'API n'est pas reconnue par l'OPCO Constructys", "expired": "<PERSON><PERSON><PERSON>, la clé d'API a expiré"}, "documentation": "Où trouver ma clé API ?"}, "opcoCfaSante": {"name": "OPCO <PERSON> (Apprentissage)", "subtitle": "", "authorizations": {"organism-informations_read": {"title": "Lire les informations de votre organisme", "text": "Afficher vos <b>informations</b>"}, "folders": {"title": "Accéder aux informations des dossiers de demandes de financement", "text": "Lire vos <b>dossiers</b>"}}, "error": {"other": "Une erreur est survenue merci de contacter l'équipe <PERSON>dof", "notfound": "<PERSON><PERSON><PERSON>, la clé d'API n'est pas reconnue par l'OPCO Santé", "expired": "<PERSON><PERSON><PERSON>, la clé d'API a expiré"}, "documentation": "Où trouver ma clé API ?"}, "opcoCfaOpcommerce": {"name": "OPCO L'Opcommmerce (Apprentissage)", "subtitle": "", "authorizations": {"organism-informations_read": {"title": "Lire les informations de votre organisme", "text": "Afficher vos <b>informations</b>"}, "folders": {"title": "Accéder aux informations des dossiers de demandes de financement", "text": "Lire vos <b>dossiers</b>"}}, "error": {"other": "Une erreur est survenue merci de contacter l'équipe <PERSON>dof", "notfound": "<PERSON><PERSON><PERSON>, la clé d'API n'est pas reconnue par l'OPCO L'Opcommmerce", "expired": "<PERSON><PERSON><PERSON>, la clé d'API a expiré"}, "documentation": "Où trouver ma clé API ?"}, "kairosAif": {"name": "Kairos AIF", "subtitle": "", "authorizations": {"trainings": {"title": "Lire des formations", "text": "Gérer vos <b>formations</b>", "limitedUsage": "Uniquement en <b>lecture</b>"}, "folders": {"title": "Lire des dossiers", "text": "<PERSON><PERSON><PERSON> vos <b>dossiers</b>", "limitedUsage": "Uniquement en <b>lecture</b>"}, "training-actions": {"title": "Lire des actions de formation", "text": "Gérer vos <b>actions de formation</b>", "limitedUsage": "Uniquement en <b>lecture</b>"}, "sessions": {"title": "Lire des sessions de formation", "text": "G<PERSON>rer vos <b>sessions de formations</b>", "limitedUsage": "Uniquement en <b>lecture</b>"}}, "errors": {"credentials": "Votre email / mot de passe est incorrect. (email : {{value1}})", "other": "Une erreur est survenue merci de contacter l'équipe <PERSON>dof"}, "documentation": "Effectuer l'habilitation Kairos AIF"}, "franceCompetences": {"name": "CertifPro - France Compétences", "subtitle": "", "authorizations": {"managePartnership": {"title": "Ajouter/Modifier/Supprimer des partenariats", "text": "<PERSON><PERSON><PERSON> vos <b>partenariats</b>", "limitedUsage": ""}, "manageCertification": {"title": "Lire des certifications", "text": "Lister les données de vos <b>certifications</b>", "limitedUsage": ""}}, "error": {"forbidden": "Votre email / mot de passe est incorrect. (email : {{value1}}). <a href='https://certifpro.francecompetences.fr/password/reset' target='_blank'>Mot de passe oublié ?</a>", "locked": "Votre compte est actuellement bloqué, d<PERSON><PERSON><PERSON><PERSON> le sur <a href='https://certifpro.francecompetences.fr/password/reset' target='_blank'>CertifPro</a>", "habilitation": "Votre compte n'est associé à aucun organisme certificateur.", "siret": "Votre compte ne permet pas d'habiliter Wedof pour l'organisme avec le n° siret : {{value1}}.", "siret.closed": "Le n° siret de l'organisme n'est pas ouvert ({{value1}})", "gestionnaire": "Votre compte ne dispose pas des droits gestionnaire pour Certif Pro. Ces droits sont nécessaire pour créer une habilitation pour Wedof. Merci d'utiliser un autre compte avec les droits gestionnaire.", "other": "Une erreur est survenue merci de contacter l'équipe <PERSON>dof"}, "confirmHabilitationText": "<p class=\"mt-2 mb-2\">\nVous vous apprêtez à habiliter Wedof. Après avoir accepté, Wedof aura accès au service en votre nom. Vous pourrez révoquer cette\ndélégation ultérieurement directement sur le portail certifpro.francecompetences.fr.\n</p>\n\n<p class=\"italic mb-5\">\nL'habilitation sera donnée à un compte dédié à Wedof au nom de \"<PERSON> (Wedof)\"\n</p>"}, "cdcCertifiers": {"name": "Portail des responsables de diplômes et certifications (Accrochage)", "subtitle": "", "authorizations": {"depot_automated": {"title": "Déposer un fichier XML", "text": "Déposer un fichier <b>XML<b>", "limitedUsage": ""}, "organism-informations_read": {"title": "Lire les informations de votre organisme", "text": "Afficher vos <b>informations<b>", "limitedUsage": ""}, "read_processed_receipt": {"title": "Lire les accusés de réception", "text": "Lister les accusés de <b>réception<b>", "limitedUsage": ""}, "read_confirmation_receipt": {"title": "Lire les accusés de traitement", "text": "Lister les accusés de <b>traitement<b>", "limitedUsage": ""}}, "start": "Dé<PERSON>rer la procédure de délégation", "sign": "Signer la procédure de délégation", "delegationDocumentName": "Délégation d'accrochage"}, "passportPrevention": {"name": "Passeport de Prévention", "subtitle": "", "documentation": "Effectuer l'habilitation Passeport de Prévention"}, "cdc": {"logo": "logo caisse des dépôts", "open-button": "Gérer l'accrochage XML", "dialog": {"title": "Données d'identification du certificateur (CDC)", "delegationAccrochageTitle": "Accrochage automatique", "delegationAccrochageData": "La délégation pour l'accrochage de vos dossiers de certification est active depuis le <b>{{date}}</b>, merci de sélectionner parmi les certifications suivantes celles pour lesquelles vous souhaitez un accrochage automatique:", "contractId": {"label": "Numéro de contrat du certificateur attestant du rôle de certificateur auprès de la CDC", "tooltip": "Ce dernier est communiqué par la Caisse des Dépôts lors du processus d'accrochage et commence par \"MCFCER\".", "error": "Le numéro de contrat doit commencer par MCFCER suivi de 6 chiffres"}, "clientId": {"label": "Matricule (ou BCR) permettant d'identifier le certificateur au sein du SI CPF", "tooltip": "Ce dernier est communiqué par la Caisse des Dépôts lors du processus d'accrochage et comporte 8 caractères.", "error": "Le Matricule doit comporter 2 chiffres puis 3 lettres majuscules puis 3 chiffres"}, "delegationMandatoryInformations": "<PERSON><PERSON> que <PERSON><PERSON>f procède à l'accrochage automatique, assurez vous d'avoir saisi les informations obligatoires décrites dans la ", "delegationMandatoryInformationsExample": "comme le matricule, le numéro de contrat de certificateur ainsi que le mode d'obtention de la certification.", "certificationAccrochage": "L'accrochage automatique est actuellement <b>{{state}}</b> pour la certification. Configurer l'accrochage depuis votre profil.", "mandatoryInformations": "<PERSON><PERSON> <PERSON>rer les fichiers d'accrochage, assurez vous d'avoir saisi les informations obligatoires décrites dans la "}, "allowXmlAutomatically": "Activez l'accrochage automatique pour {{name}}", "delegationConfirmed": "Un email va vous être envoyé afin de mettre en place la procédure de délégation pour l'accrochage de vos dossiers de certification.", "exportNotAvailableCdcContract": "Veuillez renseigner l'identifiant et le numéro de contrat certificateur de la CDC dans l'espace Certificateur de votre profil.", "exportNotAvailableObtentionSystem": "Veuillez renseigner le mode d'obtention de la certification.", "exportForTrialCertifier": "Dans le cadre de votre offre d'essai, l'export XML est limité à 2 dossiers.", "export": "Générer l'accrochage XML de la certification", "cdcFilesExportedWaiting": "<PERSON><PERSON> avez <b>{{count}}</b> fichier(s) d'accrochage pour lesquels il manque l'accusé de traitement. Avant d'en générer un nouveau, <a target=\"_blank\" href=\"profil\">transmettez les accusés de traitement</a> dans l'encart \"Caisse des Dépôts\" ou abandonnez les dépôts correspondants.", "dialogAccrochage": {"title": "Déposer l'accusé de traitement", "ingestXml": "Import d'un fichier XML d'accrochage généré hors Wedof", "ingestXmlSend": "Sélectionner un fichier XML", "ingestXmlDone": "Le fichier d'accrochage a été traité (détails dans report-xml.json). Déposez maintenant l'accusé de traitement correspondant dans la liste ci-dessus.", "ingestSpreadsheet": "Mettre à jour les dossiers de certification depuis Excel", "reUploadXml": "Redéposer l'accusé de traitement", "delegate": "Activez l'accrochage automatique pour vos certifications", "cdcFilesTitle": "Fichiers d'accrochage généré man<PERSON>", "nofile": "Aucun fichier XML ne correspond au filtre appliqué", "createdOn": "<PERSON><PERSON><PERSON><PERSON> ", "submissionDate": "<PERSON><PERSON><PERSON><PERSON><PERSON> le ", "abortedOn": "Abandonné le ", "linkShowFoldersErrors": "Voir les dossiers associés avec erreur", "form": {"submissionDate": {"label": "Date de dé<PERSON><PERSON><PERSON> du fi<PERSON>er"}, "file": {"sendFile": "Envoyer l'accusé de traitement", "errorFile": "Me<PERSON>i de transmettre un fichier XML"}}, "menu": {"abort": "Abandonner le dépôt", "confirmAbort": "Êtes-vous sûr(e) de vouloir abandonner le fichier {{name}} ?"}}, "cdcFileStates": {"exported": "Exporté", "aborted": "Abandonné", "processed": "Traité"}, "errorRate": {"title": "Taux d'erreur de l'accrochage", "tooltip": "Début 2024, la CDC a recommandé un taux d'erreur inférieur à 8%", "wedof": {"rate": "Erreurs sur les passages de certification déposés avec Wedof", "details": "(sur {{totalCount}} passages)"}, "external": {"rate": "Erreurs sur les passages de certification déposés sans Wedof", "details": "(sur {{totalCount}} passages connus de <PERSON>)"}}}}, "private": {"application": {"title": "Mes applications", "items": {"actions": {"return": "Retour", "enable": "Activer", "view": "Voir", "settings": "Réglages", "try": "Essayer", "pendingEnable": "Activation en cours"}}, "descriptif": {"slack": "Ne manquer aucune notification en connectant Wedof à Slack", "zapier": "Automatiser la gestion de vos dossiers", "salesforce": "Recevez et manipuler vos dossiers de formation dans Salesforce", "ici-formation": "Exporter votre catalogue CPF pour l'importer sur ici Formation", "webhook": "Recevoir instantanément des évènements sur vos dossiers de formation et bien plus", "ringover": "Communiquer rapidement avec vos apprenants", "ms-teams": "Être notifié à tout moment de notifications avec Wedof et MsTeams", "mattermost": "Être informé à tout moment de notifications avec Wedof et Mattermost", "dendreo": "Solution de gestion performante pour centres de formation exigeants", "message-templates": "Programmer l'envoi d'emails et de SMS", "digiforma": "Communiquer avec Digiforma", "status": "Restez informé des incidents EDOF, France Compétences, Wedof et bien plus encore", "make": "Automatiser la gestion de vos dossiers", "activepieces": "Automatiser la gestion de vos dossiers", "n8n": "Automatiser la gestion de vos dossiers", "document": "Générer automatiquement vos documents", "signature": "Signature éléctronique de vos documents", "onlineformapro": "Synchroniser et suivre l'avancement de vos apprenants avec le LMS d'Onlineformapro", "workflow": "Créer des processus Métiers Automatisés ! Interconnectez vos outils internes et autres outils SaaS sans coder directement depuis Wedof.", "evoliz": "Factuer facilement"}, "common": {"title": "Applications", "subtitle": {"pendingDisable": "(Désactivation prévue le {{endDate}})", "trial": "(Période d'essai jusqu'au {{endDate}})"}, "actions": {"add": "Ajouter", "disable": "Désactiver", "back": "Retour", "submit": "Enregistrer", "cancelPendingDisable": "Annuler la désactivation"}}, "oauth": {"description": "Wedo<PERSON> est en train de se connecter à votre compte {{name}}..."}, "slack": {"form": {"channel": {"label": "Channel", "error": "Ce champ est obligatoire"}}}, "status": {"form": {"email": {"label": "Recevez des notifications pour les services sélectionnés", "error": "Ce champ est obligatoire"}}, "badge": "{{component}}<a href=\"https://status-new.wedof.fr/\" target=\"blank\"><span class=\"status-badge inline-flex items-center bg-{{status}}-100 text-{{status}}-800 text-xs font-medium px-2.5 py-0.5 rounded-full dark:bg-{{status}}-900 dark:text-{{status}}-300\"><span class=\"w-2 h-2 me-1 bg-{{status}}-500 rounded-full\"></span>{{statusText}}</span></a>"}, "digiforma": {"description": "Renseigner vos identifiants Digiforma", "link": "(activer le token API sur Digiforma)", "valid-credentials": "Connexion avec Digiforma opérationelle", "invalid-credentials": "La connexion avec Digiforma a échoué", "synchronization": {"status": {"error": "Status de la synchronisation avec Digiforma : inactive", "success": "Status de la synchronisation avec Digiforma : active"}}, "username": "Identifiant", "placeholder": {"username": "Votre identifiant Digiforma", "password": "Votre mot de passe Digiforma"}, "password": "Mot de passe", "process_invoice": "Activation de la facturation Digiforma", "default_configuration": "Configuration par défaut", "start-api-trial": "Aucun token d'API Digiforma disponible. Démarrez un mois d'essai de l'API Digiforma pour obtenir un accès", "no-api": "Aucun token d'API Digiforma disponible, veuillez souscrire à une offre avec l'API Digiforma"}, "onlineformapro": {"description": "Afin de pouvoir synchroniser l'avancement de vos apprenants merci de <PERSON>, l'url de votre serveur, vos identifiants et votre clé api Onlineformapro", "valid-credentials": "Connexion avec Onlineformapro opérationelle", "invalid-credentials": "La connexion avec Onlineformapro a échoué", "synchronization": {"status": {"error": "Status de la synchronisation avec Onlineformapro : inactive", "loading": "Status de la synchronisation avec Onlineformapro : en cours de rafraichissement", "success": "Status de la synchronisation avec Onlineformapro : active"}}, "username": "Identifiant / email", "baseUrl": "URL serveur Onlineformapro", "apiKey": "Clé d'API", "placeholder": {"username": "Votre identifiant / email Onlineformapro", "password": "Votre mot de passe Onlineformapro", "baseUrl": "Votre URL Onlineformapro", "apiKey": "Votre clé d'API Onlineformapro"}, "password": "Mot de passe"}, "zapier": {"form": {"name": {"label": "Name", "error": "Ce champ est obligatoire"}}}, "salesforce": {"form": {"domain": {"label": "Votre domaine Salesforce"}, "sendPayments": {"label": "Envoyer les données de paiements des dossiers de formation à Salesforce", "snack": "L'envoi des données de paiements a bien été mis à jour"}}, "installation": {"label": "Lien d'installation de la dernière version de l'application (V2.4)", "link": "https://login.salesforce.com/packaging/installPackage.apexp?p0=04tJ8000000D2Mm"}, "documentation": {"label": "Documentation et historique des versions", "link": "<a target='_blank' href='/assistance/guides/applications/salesforce'>Documentation</a>"}, "information": "Cette application connecte Wedof et Salesforce afin de vous faire gagner du temps et de la fiabilité dans le suivi commercial de vos dossiers de formation. Elle permet la mise à jour automatique des données depuis le Dossier de formation d'un apprenant vers Salesforce. ", "synchronization": {"status": {"error": "La synchronisation avec votre domaine est inactive", "success": "La synchronisation avec votre domaine {{domain}} est active"}}}, "iciFormation": {"title": "Paramétrer vos formations pour exporter votre catalogue de formation et l'importer sur Ici Formation en suivant ce ", "trainings": " formations", "actions": {"export": "Exporter", "settings": "Paramétrer mes "}, "form": {"title": "Paramétrer mes formations", "subtitle": "Pour pouvoir exporter votre catalogue, vous devez sélectionner un domaine et un sous-domaine pour chacune des formations que vous souhaitez rendre visible sur ", "subtitle2": "Ces domaines et sous-domaines sont des catégories requises par Ici Formation", "subtitleExplanation": "Les images, vidéos, liens hypertextes ne sont pas supportés par Ici Formation.", "noTraining": "Vous n'avez pas de formation active", "tooltip": "Vous pouvez entrer une accroche (max 255 caractères) pour votre formation et elle sera mise en avant lors de la recherche de formations", "table": {"header": {"trainings": "Formations", "domain": "Domaine", "subDomain": "Sous-domaine", "export": "Exportable", "publicFormation": "Public", "sanction": "Sanction", "diplome": "Diplôme", "prerequis": "Prérequis"}, "label": {"publicFormation": "Description du public ciblé", "sanction": "<PERSON><PERSON> le chiffre correspondant à la sanction de formation", "diplome": "Niveau <PERSON> Diplôme obtenu à l'issue de la formation", "accroche": "Ajouter une accroche à votre formation", "prerequis": "Prérequis de la formation"}, "attestation": {"attestation": "Attestation de présence", "habilitation": "Une habilitation", "certification": "Une certification", "qualification": "Une qualification", "titre": "Un titre professionnel", "diplome": "Diplôme"}, "diplome": {"pasDiplome": "Pas <PERSON>ôme", "niveau5": "Niveau V - (CAP, BEP...)", "niveau4": "Niveau IV - (Bac)", "niveau3": "Niveau III - (Bac+2)", "niveau2": "Niveau II - (Bac+3 et 4)", "niveau1": "Niveau I - (Bac+5 et plus)"}}, "save": "<PERSON><PERSON><PERSON><PERSON>"}}, "maFormation": {"title": "Paramétrer vos formations pour exporter votre catalogue de formations et l'importer sur MaFormation", "actions": {"export": "Exporter", "settings": "Paramétrer mes formations"}, "form": {"title": "Paramétrer mes formations", "noTraining": "Vous n'avez pas de formation active", "table": {"header": {"trainings": "Formations", "idCarif": "Identifiant Carif Oref", "lePlus": "Le Plus de ma formation", "prerequis": "Niveau minimum du candidat", "tauxReussite": "<PERSON><PERSON>", "openSalaries": "Formation ouverte aux salariés", "openPE": "Formation ouverte aux demandeurs d'emploi", "openEtudiants": "Formation ouverte aux étudiants", "openEntreprises": "Formation ouverte aux entreprises", "difficulte": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "vae": "Formation disponible en VAE", "bilanCP": "Formation disponible en Bilan de Compétences", "export": "Exportable"}}, "save": "<PERSON><PERSON><PERSON><PERSON>"}}, "webhooks": {"events": {"proposal": {"label": "Événements sur les propositions"}, "certification": {"title": "Certifications", "updated": "Mise à jour", "created": "Création"}, "certificationPartner": {"title": "Partenariats de certification", "label": "Événements sur les partenariats", "processing": "Demande en traitement", "aborted": "<PERSON><PERSON><PERSON>", "active": "Partenariat actif", "refused": "<PERSON><PERSON><PERSON> refusée", "suspended": "Partenariat suspendu", "revoked": "Partenariat révoqué", "file": "Tous les événements sur les documents"}, "certificationPartnerInvoice": {"label": "Événements sur les factures de partenariats", "created": "Facture créée", "updated": "Facture mise à jour", "paid": "Facture payée", "deleted": "Facture supprimée", "invoice": "Tous les événements sur les factures de partenariats"}, "evaluation": {"title": "Évaluations", "organismChanged": "La note de l'organisme a changé", "organismNew": "Nouvelle note pour l'organisme", "trainingChanged": "La note de la formation a changé", "trainingNew": "Nouvelle note pour la formation", "trainingActionChanged": "La note de l'action de formation a changé", "trainingActionNew": "Nouvelle note pour l'action de formation"}, "registrationFolder": {"label": "Événements sur les dossiers", "title": "Mes dossiers de formation", "created": "Création du dossier de formation", "updated": "Mise à jour du dossier de formation", "canceledByAttendee": "Annulé (par le titulaire)", "canceledByAttendeeNotRealized": "Annulation titulaire (non réalisé)", "canceledByOrganism": "Annulé (par l'organisme)", "canceledByFinancer": "Annulé (par le financeur)", "refusedByAttendee": "<PERSON><PERSON><PERSON> titulaire", "refusedByOrganism": "Refusé (par l'organisme)", "refusedByFinancer": "Refusé (par le financeur)", "rejectedWithoutTitulaireSuite": "Annulé sans suite", "rejected": "Annulé sans suite", "notProcessed": "<PERSON> traiter", "validated": "<PERSON><PERSON><PERSON>", "waitingAcceptation": "Validé (En cours d'instruction Pôle emploi)", "accepted": "Accepté", "inTraining": "En formation", "terminated": "Sortie de formation", "serviceDoneDeclared": "Service fait déclaré", "serviceDoneValidated": "Service fait validé", "file": "Tous les événements sur les documents"}, "registrationFolderAlert": {"title": "Alerte sur les dossiers de formation", "notAccepted": "Pas accepté par le titulaire", "notInTraining": "Pas en formation", "notServiceDoneDeclared": "Service fait non déclaré", "notValidated": "Pas validé par l'organisme"}, "registrationFolderBilling": {"title": "Facturation des dossiers de formation", "notBillable": "Pas facturable", "depositWait": "Acompte en attente de versement", "depositPaid": "Acompte versé", "toBill": "À facturer", "billed": "<PERSON><PERSON><PERSON><PERSON>", "paid": "<PERSON><PERSON>"}, "registrationFolderControl": {"title": "État de contrôle des dossiers de formation", "matToolTip": "Permet d'indiquer l'état de contrôle du dossier", "notInControl": "Aucun contrôle en cours", "inControl": "En cours de contrôle", "released": "Contr<PERSON>le terminé"}, "registrationFolderFile": {"label": "Événements sur les documents", "added": "Document ajouté", "updated": "Document mis à jour", "deleted": "Document supprimé", "valid": "Document validé", "toReview": "Document à vérifier", "refused": "Document refusé", "signaturePartially": "Signature partielle", "signatureCompleted": "Signature completée", "signatureDeclined": "Signature déclinée"}, "certificationFolder": {"label": "Événements sur les dossiers", "title": "Tous les dossiers de certification", "created": "Création du dossier de certification", "updated": "Mise à jour du dossier de certification", "notReady": "<PERSON><PERSON><PERSON>", "toControl": "<PERSON> contrôler", "refused": "<PERSON><PERSON><PERSON><PERSON>", "toRegister": "À enregistrer", "registered": "Enregistré", "inTrainingStarted": "Formation démarrée", "inTrainingEnded": "Formation terminée", "toTake": "<PERSON><PERSON><PERSON><PERSON> à passer", "toRetake": "À repasser", "failed": "<PERSON><PERSON><PERSON>", "aborted": "Abandonné", "success": "<PERSON><PERSON><PERSON><PERSON>", "accrochageOk": "Accrochage r<PERSON>i", "accrochageKo": "Accrochage en erreur", "missingData": "Données accrochage manquantes", "file": "Tous les événements sur les documents"}, "certificationFolderSurvey": {"label": "Événements sur les enquêtes", "title": "Toutes les enquêtes", "created": "Questionnaire \"Situation professionnelle en début de cursus\" est accessible (Enquête créée)", "sixMonthExperienceAvailable": "Questionnaire \"Situation professionnelle de 6 mois\" est accessible", "longTermExperienceAvailable": "Questionnaire \"Situation professionnelle au moins un an\" est accessible", "initialExperienceAnswered": "Questionnaire \"Situation professionnelle en début de cursus\" répondu", "sixMonthExperienceAnswered": "Questionnaire \"Situation professionnelle de 6 mois\" répondu", "longTermExperienceAnswered": "Questionnaire \"Situation professionnelle au moins un an\" répondu"}, "certificationFolderFile": {"label": "Événements sur les documents", "added": "Document ajouté", "updated": "Document mis à jour", "deleted": "Document supprimé", "valid": "Document validé", "toReview": "Document à vérifier", "refused": "Document refusé"}, "certificationPartnerFile": {"label": "Événements sur les documents", "added": "Document ajouté", "updated": "Document mis à jour", "deleted": "Document supprimé", "valid": "Document validé", "toReview": "Document à vérifier", "refused": "Document refusé"}, "certificationPartnerAudit": {"label": "Événements sur les audits de partenariat", "audit": "Tous les audits de partenariat", "pendingComputation": "Audit en préparation (collecte des données en cours)", "computing": "Audit analyse des données en cours", "inProgress": "Audit en cours", "completed": "Audit finalisé", "compliant": "Audit conforme", "nonCompliant": "Audit non conforme", "partiallyCompliant": "Audit conforme partiellement"}, "reseller": {"title": "Revendeur"}, "subscription": {"title": "Souscription", "created": "Souscription créée", "updated": "Souscription mise à jour"}, "connection": {"title": "Connexion", "initializeStarted": "Connexion débutée", "initialized": "Connexion complétée", "revoked": "Connexion révoquée"}, "organism": {"title": "Organisme", "cpfCatalogUploadFinished": "Import du catalogue dans EDOF terminé", "cpfCatalogExportFinished": "Export du catalogue EDOF terminé"}}, "table": {"header": {"url": "URL", "actions": "Actions"}}, "title": "Webhooks", "actions": {"disable": "Désactiver", "enable": "Activer", "delete": "Êtes-vous sûr(e) de vouloir supprimer ce webhook ?"}, "deliveries": {"title": "Deliveries", "table": {"noData": "<PERSON><PERSON><PERSON> don<PERSON>", "header": {"guid": "Guid - Évènement", "actions": "Actions", "statusCode": "Status Code", "date": "Date", "event": "<PERSON><PERSON><PERSON><PERSON>"}}, "dialog": {"title": "Message d'erreur", "closeButton": "<PERSON><PERSON><PERSON>"}, "nodata": "Au<PERSON>ne donnée envoy<PERSON> par <PERSON><PERSON>ok"}, "form": {"fields": {"name": {"label": "Nom"}, "url": {"label": "URL", "error": "L'url de votre webhooks doit être valide et ne doit pas être locale"}, "secret": {"label": "Secret"}, "sendEverything": {"label": "Quels sont les événements que vous souhaitez voir déclencher ce webhook ?", "value": "Tous"}, "ignoreSsl": {"label": "Ignorer SSL ?", "value": "O<PERSON>"}, "events": {"error": "Veuillez sélectionner au moins 1 événement"}}, "errors": {"errorZapier": "Vous ne pouvez pas ajouter de hooks Zapier via les webhooks. Utilisez l'app Zapier si vous souhaitez envoyer les hooks."}}}, "workflow-runs": {"title": "<PERSON><PERSON>", "noData": "Aucune exécution de processus trouvée", "status": {"label": "Statut : ", "SUCCEEDED": "<PERSON><PERSON><PERSON><PERSON>", "FAILED": "<PERSON><PERSON><PERSON>", "RUNNING": "En cours", "PAUSED": "En pause", "STOPPED": "<PERSON><PERSON><PERSON><PERSON>"}, "tasks": "tâche(s)", "duration": "ms", "errors": {"loadingError": "Erreur lors du chargement des exécutions de processus", "retryError": "<PERSON><PERSON><PERSON> lors de la relance du processus"}}, "message-templates": {"title": "Messages et notifications"}, "activepieces": {"activate": "Activer l'option Activepieces pour seulement 49€/mois avec Wedof", "documentation": "Découvrir la documentation d'Activepieces"}, "signature": {"signatureSettingsDialog": "Paramétrage de vos signatures", "signatureTitle": "Signature représentant votre organisme", "initialsTitle": "<PERSON><PERSON> représentant votre organisme", "stampTitle": "Tampon représentant votre organisme", "configure": "Cliquez sur configurer vos signatures", "subscriptionOptionDialog": {"subtitle": "Ajoutez la Signature Électronique à tous vos documents ! Sélectionnez les signataires et faites signer vos documents générés automatiquement via Wedof. Profitez d'un nombre illimité de signatures. <a href='/'>Cliquez ici pour en savoir plus</a>", "subtitle2": "La Signature Électronique est une option qui vous sera facturée <b>49€ HT / mois</b> (ou 490€ HT / an). <br/><br/>V<PERSON> pouvez désactiver l'application à tout moment.", "subtitle2Trial": "Profitez d'une <b>période d'essai de 15 jours</b> pour découvrir les fonctionnalités de la Signature Électronique. A la fin de celle-ci, la Signature Électronique vous sera facturée <b>49€ HT / mois</b> (ou 490€ HT / an). <br/><br/>Vous pouvez désactiver l'application à tout moment."}}, "workflow": {"subscriptionOptionDialog": {"subtitle": "Créez des processus Métiers Automatisés ! Interconnectez vos outils internes et autres outils SaaS sans coder directement depuis Wedof.", "subtitle2": "L'application Processus Métiers Automatisés est une option qui vous sera facturée <b>49€ HT / mois</b> (ou 490€ HT / an). <br/><br/>Vous pouvez désactiver l'application à tout moment.", "subtitle2Trial": "Profitez d'une <b>période d'essai de 15 jours</b> pour découvrir les fonctionnalités. A la fin de celle-ci, l'application Processus Métiers Automatisés vous sera facturée <b>49€ HT / mois</b> (ou 490€ HT / an). <br/><br/>Vous pouvez désactiver l'application à tout moment."}}}, "common": {"registrationFolder": {"title": "Dossier de formation", "attendee": {"updateRestriction": "Certaines informations associées ne sont modifiables que par le candidat ou si vous avez sa pièce d'identité. ", "attendee": "Apprenant", "candidate": "Candidat", "firstName": "Prénom", "firstName2": "Deuxième prénom", "firstName3": "Troisième prénom", "lastName": "Nom de famille", "dateOfBirth": "Date de naissance", "nameCityOfBirth": "Ville de naissance", "nameCountryOfBirth": "Pays de naissance", "birthName": "Nom de naissance", "gender": {"label": "Civilité", "female": "Madame", "male": "<PERSON>"}, "placeOfBirth": {"title": "Lieu de naissance", "country": "<PERSON>é(e) à l'étranger", "city": "Né(e) en France", "placeholderCountry": "Choisir un pays"}, "degreeTitle": "Diplôme", "email": "Email", "phoneFixed": "Téléphone fixe", "phoneNumber": "Téléphone portable", "address": "<PERSON><PERSON><PERSON>", "copy": "{{value}} a été copié dans le presse-papier"}, "franceTravailFunding": "Pôle Emploi associé au dossier de formation", "feedback": "Traitement de {{processedFolderCount}} dossier sur {{folderDialogCount}} en cours", "plural": {"feedback": "Traitement de {{processedFolderCount}} dossiers sur {{folderDialogCount}} en cours"}, "state": {"notProcessed": "Le dossier de formation est en attente de traitement, vous avez 48 heures ouvrées pour le valider et envoyer la proposition à l'apprenant.", "notProcessedAlert": "A<PERSON> de valider le dossier, veuillez modifier la date de début de session. Elle ne peut être antérieure au {{cpfSessionMinDate}}.", "validated": "Le dossier de formation est validé, il est en attente d'acceptation par l'apprenant.", "waitingAcceptation": "Le dossier de formation est en attente d'acceptation par un financeur (Pôle Emploi, OPCO..).", "accepted": "Le dossier de formation est accepté. L'apprenant est en attente du début de sa formation.", "inTraining": "L'apprenant est actuellement en cours de formation.", "terminated": "L'apprenant est sorti de la formation. Il a effectué {{completionRate}}% du parcours de formation.", "serviceDoneDeclared": "L'apprenant a effectué {{completionRate}}% du parcours de formation. Le dossier de formation est : Service fait déclaré. Il est en attente de la validation du service fait.", "serviceDoneValidated": "L'apprenant a effectué {{completionRate}}% du parcours de formation. Le dossier de formation est clôturé.", "canceledByAttendee": "L'apprenant a annulé son dossier de formation.", "canceledByAttendeeNotRealized": "L'apprenant a annulé son dossier de formation", "canceledByOrganism": "Vous avez annulé le dossier de formation.", "canceledByFinancer": "Le financeur a annulé le dossier de formation", "refusedByAttendee": "L'apprenant a refusé le dossier de formation.", "refusedByOrganism": "<PERSON><PERSON> avez refusé le dossier de formation.", "rejectedWithoutTitulaireSuite": "Le dossier de formation a été déclaré sans suite.", "rejectedWithoutCdcSuite": "Le dossier de formation a été déclaré sans suite.", "rejectedWithoutOfSuite": "Le dossier de formation a été déclaré sans suite.", "rejected": "Le dossier de formation a été déclaré sans suite."}, "controlState": {"update": "Modifier l'état du contrôle", "tooltip": {"notInControl": "Aucun contrôle en cours du financeur", "inControl": "Actuellement en cours de contrôle par le financeur", "released": "Contr<PERSON>le terminé"}, "state": {"notInControl": "<PERSON><PERSON><PERSON> contr<PERSON><PERSON>", "inControl": "En cours de contrôle", "released": "Contr<PERSON>le terminé"}, "actions": {"notInControl": "<PERSON><PERSON><PERSON> contr<PERSON><PERSON>", "inControl": "En cours de contrôle", "released": "Contr<PERSON>le terminé"}}, "lastUpdate": "Date de dernière mise à jour"}, "certificationFolder": {"title": "Dossier de certification", "date": "{{date}}", "enrollmentDate": "Date d'inscription", "examinationDate": "Date de début de l'examen", "examinationDates": "Date d'examen", "examinationEndDate": "Date de fin de l'examen", "examinationPlace": "Lieu de l'examen", "tiersTemps": "Tiers temps", "examinationDetails": "Détails sur le passage de l'examen", "issueDate": "Date d'obtention de la certification", "issueDateCertificate": "Date d'obtention de la certification (Parchemin n°{{certificateId}})", "expirationDate": "Date de fin de validité de la certification", "detailedResult": "Détail du résultat de l'examen", "europeanLanguageLevel": "Niveau de langue Européen", "digitalProofLink": "Preuve numérique de l'obtention de la certification", "badgeAssertion": "Badge", "comment": "Commentaire", "certifier": "Organisme certificateur", "amountHt": "Prix HT", "amountHtToolTip": "Renseignez un prix sur ce dossier s'il est différent du prix par dossier défini au niveau du partenariat ou depuis la certification", "skillSets": "Les blocs de compétences du dossier de certification doivent obligatoirement être renseignés", "cdcExport": {"maxDateForExport": "Le dossier doit être accroché afin d'être disponible dans le Passeport de Compétences du candidat avant le {{maxDate}}", "no": "Le dossier ne contient pas les informations obligatoires pour le {{result}}", "processedOkCertifierInformations": "Le dossier a été accroché le {{date}} et est disponible dans le Passeport de Compétences du candidat", "processedOkCertifier": "Le dossier a été accroché et est disponible dans le Passeport de Compétences du candidat", "processedOkPartner": "La certification est disponible dans le Passeport de Compétences du candidat", "processedKo": "Le dossier n'a pas été accroché par la CDC à cause d'une erreur", "processedKoInformations": "<a target='_blank' href='/assistance/guides/certificateurs/statut-accrochage'>Le dossier accroché le {{date}} a été refusé par la CDC à cause de l'erreur : {{errorMessage}}</a> ", "exportedInformations": "Le dossier a été exporté pour la CDC le {{date}} - Fournissez l'accusé de traitement à Wedof dès que possible depuis votre <a target=\"_blank\" href=\"profil\">profil</a> dans l'encart Caisse des Dépôts.", "exportedPartner": "Le dossier a été exporté pour être ajouté au Passeport de Compétences", "exported": "Le dossier a été exporté pour la CDC - Fournissez l'accusé de traitement à Wedof dès que possible."}, "certificate": {"generateCertificate": "<PERSON><PERSON><PERSON><PERSON> le parchemin", "certificateGenerating": "Le parchemin est en cours de création", "confirmGeneration": "Vous allez générer automatiquement le parchemin pour ce dossier de certification. Souhaitez-vous continuer ?", "info": "La génération automatique des parchemins est disponible dans Wedof"}, "tooltip": {"examinationDate": "Date à laquelle le titulaire a commencé l'examen de la certification", "examinationEndDate": "Date à laquelle le titulaire a fini l'examen de la certification", "detailedResult": "Score ou base de notation atteint par le titulaire lors de la certification", "expirationDate": "Dans le cas d'une certification valide pour une période donnée, la date d'expiration est calculée en fonction de la date de délivrance et de la durée de validité de certification soit {{validityPeriod}} ans pour cette certification.", "digitalProofLink": "Dans le cas où une preuve de la certification est remise sous forme numérique, indiquer le lien d'accès qui doit être durable et vérifié. Si le lien n'est pas renseigné, Wedof remplira automatiquement le champ avec un lien interne Wedof.", "enrollmentDate": "Date d'inscription de l'apprenant à la certification ou à l'examen auprès du certificateur."}, "certificateGeneratedAutomatically": "Le parchemin sera généré automatiquement lors du passage à l'état \"Réussi\".", "gradesPass": {"SANS_MENTION": "Sans mention", "MENTION_ASSEZ_BIEN": "Mention assez bien", "MENTION_BIEN": "Mention bien", "MENTION_TRES_BIEN": "Mention très bien", "MENTION_TRES_BIEN_AVEC_FELICITATIONS_DU_JURY": "Mention très bien avec félicitations du jury"}, "gradePassPlaceholder": "Choisissez une mention", "gradePassLabel": "Mention obtenue", "cdcState": {"title": "État de l'accrochage du dossier", "notExported": "<PERSON><PERSON> accro<PERSON>", "exported": "Envoyé et en attente de l'accusé", "processedOk": "Accrochage r<PERSON>i", "processedKo": "Accrochage en erreur"}, "cdcExcluded": "Accrocher le dossier", "cdcAccrochageHelp": "Si vous ne parvenez toujours pas à accrocher ce dossier après avoir inséré les bonnes données du candidat, il se peut que celui-ci n'ait pas de numéro de sécurité sociale ce qui empêche son accrochage du côté de la CDC.", "deletion": "Attention cette opération est irréversible! Êtes-vous sûr(e) de vouloir supprimer le dossier de certification {{externalId}} ?", "state": {"title": "État de la certification du dossier", "toRegister": "À enregistrer", "refused": "<PERSON><PERSON><PERSON><PERSON>", "registered": "Enregistré", "toTake": "<PERSON><PERSON><PERSON><PERSON> à passer", "toControl": "<PERSON> contrôler", "toRetake": "À repasser", "failed": "<PERSON><PERSON><PERSON>", "aborted": "Abandonné", "success": "<PERSON><PERSON><PERSON><PERSON>", "accrochageOk": "Accrochage r<PERSON>i", "accrochageKo": "Accrochage en erreur", "missingData": "Données accrochage manquantes", "isCertifier": {"toRegister": "Le dossier de certification du candidat est en attente d'enregistrement.", "registered": "Le dossier de certification du candidat est enregistré. Le candidat est en attente ou en cours de préparation à la certification.", "toTake": "Le candidat est en attente de passer l'examen de certification.", "toRetake": "Le candidat est en attente d'un nouveau passage de l'examen de certification.", "toControl": "Le candidat a passé l'examen de certification. Le dossier est en attente de contrôle de l'examen.", "success": "Le candidat a obtenu la certification : {{certification}}.", "successSkillSets": "Le candidat a obtenu le(s) bloc(s) de compétences : {{skillSets}}", "refused": "Le dossier de certification est refusé.", "failed": "Le candidat n'a pas obtenu la certification : {{certification}}.", "aborted": "Le candidat a abandonné l'examen de certification."}, "isTrainer": {"toRegister": "Le dossier de certification de l'apprenant est en attente d'enregistrement de la part du certificateur ({{certifier}}).", "registered": "Le dossier de certification de l'apprenant a été enregistré par le certificateur ({{certifier}}).", "toTake": "L'apprenant est prêt à passer l'examen de certification.", "toRetake": "Le certificateur ({{certifier}}) indique que l'apprenant n'a pas obtenu la certification ou que le dossier n'est pas conforme. L'apprenant est en attente d'un nouveau passage l'examen de certification.", "toControl": "Le dossier de certification est en attente de contrôle par le certificateur ({{certifier}}).", "success": "L'apprenant a <b>obtenu</b> la certification : <b>{{certification}}</b>.", "successSkillSets": "L'apprenant a <b>obtenu</b> le(s) bloc(s) de compétences : <b>{{skillSets}}</b>", "refused": "Le dossier de certification a été refusé par le certificateur ({{certifier}}).", "failed": "L'apprenant n'a pas obtenu la certification : {{certification}}.", "aborted": "L'apprenant a abandonné l'examen de certification."}}, "validation": {"examinationDateError": "Renseignez une date de passage de l'examen", "examinationPlaceError": "Renseignez un lieu de passage de l'examen", "examinationTypeError": "Renseignez une modalité de passage d'examen", "issueDateError": "Vérifier la cohérence des dates", "noDigitalProofLinkAlert": "Pensez à renseigner pour chaque dossier le lien spécifique de la preuve numérique."}, "feedback": "Traitement de {{processedFolderCount}} dossier sur {{folderDialogCount}} en cours", "plural": {"feedback": "Traitement de {{processedFolderCount}} dossiers sur {{folderDialogCount}} en cours"}, "stateLastUpdate": "Date du dernier changement d'état", "import": {"title": "Mettre à jour les dossiers de certification depuis Excel", "information": "Les dossiers de certification doivent déjà exister dans Wedof.", "information2": "La mise à jour via l'import d'un XML ne déclenchera aucun évènement sur les dossiers concernés (parchemin, email, webhook etc.)", "information3": "Pensez à vérifier que tous les documents nécessaires sont renseignés et conformes.", "raiseEvents": "Déclencher les automatisations : envoi de messages et notifications, génération de documents, webhooks etc.", "success": "Le fichier Excel a été traité, reportez-vous au fichier de rapport d'import CSV pour obtenir les détails du traitement."}}, "certification": {"certifInfo": "Certif Info N° {{certifInfo}}", "updatedOn": "<PERSON><PERSON><PERSON> mise à jour : {{date}} ", "estimatedRegistrationFoldersCount": "Nombre de dossiers CPF estimé au ", "estimatedRegistrationFoldersCountToolTip": "Total des dossiers sortis de formation d'après les données de l'Open Data de la CDC", "exportXml": "Gestion de l'Accrochage certificateur", "cpfDateStart": "Date de début de validité CPF : ", "cpfDateEnd": "Date de fin de validité CPF : ", "certificationPartnerFileTypesTitle": "Documents pour devenir partenaire", "certificationPartnerFileTypesSubtitle": "Documents requis par le certificateur dans le cadre d'une demande de partenariat", "certificationFolderFileTypesSubtitle": "Documents associés à chaque dossier de certification", "certificationFolders": "Nombre de dossiers de certification", "link": "Lien de la certification", "validityPeriodSet": "La certification du titulaire a-t-elle une limite de validité ? ", "validityPeriod": "<PERSON><PERSON><PERSON> (en années)", "compliance": "Conformité", "complianceTooltip": "La conformité du partenariat dépend du résultat des audits. Elle est modifiable dès qu'un audit à été réalisé", "amountHt": "Prix HT par certification", "amountHtPlaceholder": "Prix HT du passage de la certification", "amountHtTooltip": "Le prix du passage de la certification sera appliqué à chaque dossier de certification afin de connaître votre chiffre d'affaires total. Vous pouvez également renseigner le prix du passage de la certification par partenariat.", "validityPlaceholder": "Durée de validité (en années)", "autoRegistering": {"title": "Automatiquement passer à Enregistré un dossier de certification", "help": "Dans le cas où des documents requis à l'état Enregistré manqueraient sur des dossiers issus de partenaire, l'automatisation ne s'appliquera pas."}, "allowGenerateXmlAutomatically": "Accrocher automatiquement les dossiers réussis complets pour cette certification", "allowPartnershipRequest": "Autoriser les demandes de partenariats depuis Wedof ?", "partnershipComment": "Informations sur la demande de partenariat", "partnershipCommentPlaceholder": "Message à destination d'un organisme qui effectue une nouvelle demande de partenariat. V<PERSON> pouvez utiliser ce champ pour lui demander des informations (nombre de dossiers prévus, lien d'un document à remplir...)", "manageMyPartnershipTitle": "Gestion des partenariats", "manageMypartnershipDescription": "Gérer l'habilitation et la gestion de vos partenaires", "manageMyCertificationFolders": "Gestion des dossiers de certification", "obtentionSystem": {"title": "Mode d'obtention", "scoring": "Par score", "admission": "Par admission"}, "examinationType": {"titleDefault": "Modalité par défaut de l'examen", "title": "Modalité de l'examen", "A_DISTANCE": "À distance", "MIXTE": "Mixte (à distance et en présentiel)", "EN_PRESENTIEL": "En présentiel"}, "surveyOptional": "Autoriser le téléchargement du parchemin par le candidat sans les enquêtes de suivi d’insertion professionnelle à jour", "allowPartialSkillSets": "L'enseignement de la certification peut être divisé par bloc de compétences", "files": {"noData": "Ajouter un document attendu", "table": {"nom": "Nom", "toState": "Requis", "confirmDeletion": "Êtes-vous sûr(e) de vouloir supprimer le type de document {{name}} ?"}}, "modalities": "Pour connaître les modalités d'accès, les modalités d'évaluation et les modalités de réussite, consultez la", "modalitiesLink": "page de la certification sur France Compétences.", "managePartnership": {"tooltip": {"expirationDate": "Date d'expiration", "expired": "Expirée", "oneYear": "Expire dans moins d'un an", "disabled": "Cette action est impossible car la certification est expirée."}, "action": {"invite": "In<PERSON><PERSON> sur Wedof", "inviteSent": "Invitation envoyée", "createPromoted": "Nouvelle demande", "createUnpromoted": "<PERSON>er", "request": "Envoyer la demande"}, "menu": {"abort": "Abandonner la demande", "refuse": "Refuser la demande", "reopen": "Repasser à l'état \"Demande en traitement\"", "reopenShort": "Relancer la demande", "pendingActivation": "<PERSON><PERSON> le partenariat", "revertPendingAcceptation": "Annuler l'activation", "pendingRevocation": "Révoquer le partenariat", "revertPendingRevocation": "Annuler la révocation", "pendingSuspension": "Suspendre le partenariat", "revertPendingSuspension": "Annuler la suspension", "reinitialize": "Réinitialiser les données"}, "partnerCount": {"none": "0 partenariat", "singular": "1 partenariat", "plural": "{{count}} partenariats"}, "requestDialog": {"title": "Votre demande de partenariat va être envoyée", "message": "Vous allez soumettre aux organismes Certificateur \"{{certifiersName}}\" votre demande de partenariat sur la certification \"{{certificationName}}\". Si des documents sont demandés par le certificateur, pensez à les envoyer.", "success": "Votre demande a bien été envoyée et est passée à l'état \"Demande en traitement\""}, "invitationDialog": {"title": "Coordonnées du certificateur", "message": "Vous allez inviter l'organisme Certificateur dont vous êtes partenaire, à rejoindre Wedof afin de partager automatiquement vos dossiers de certification avec lui.", "success": "Votre invitation a bien été envoyée", "certifierSuffix": "du contact certificateur"}, "confirmHabilitation": "Attention, le partenariat sera créé avec l'habilitation <b>{{habilitation}}</b>.", "confirm": {"aborted": "Êtes vous sûr(e) de vouloir passer la demande à l'état \"Demande abandonnée\" ? L'organisme correspondant en sera notifié par email.", "refused": "Êtes vous sûr(e) de vouloir passer la demande à l'état \"Demande refusée\" ? L'organisme à l'origine de la demande de partenariat en sera notifié par email.", "processing": "Êtes vous sûr(e) de vouloir repasser la demande à l'état \"Demande en traitement\" ? L'organisme correspondant en sera notifié par email.", "pendingActivation": "Êtes vous sûr(e) de vouloir activer le partenariat avec l'habilitation choisie ? Le partenariat va être ajouté automatiquement sur France Compétences d'ici quelques minutes et passera alors dans Wedof à l'état \"Partenariat actif\".", "revertPendingAcceptation": "Êtes vous sûr(e) de vouloir annuler l'activation du partenariat ?", "pendingRevocation": "Êtes vous sûr(e) de vouloir révoquer le partenariat ? Le partenariat va être retiré automatiquement sur France Compétences d'ici quelques minutes et passera alors dans Wedof à l'état \"Partenariat révoqué\".", "revertPendingRevocation": "Êtes vous sûr(e) de vouloir annuler la révocation du partenariat ?", "pendingSuspension": "Êtes vous sûr(e) de vouloir suspendre le partenariat ? Le partenariat va être retiré automatiquement sur France Compétences d'ici quelques minutes et passera alors dans Wedof à l'état \"Partenariat suspendu\".", "revertPendingSuspension": "Êtes vous sûr(e) de vouloir annuler la suspension du partenariat ?", "new": "Vous allez créer une nouvelle demande de partenariat à l'état \"Demande à compléter\". Vous pourrez alors la compléter et l'envoyer au certificateur afin qu'il l'étudie. Souhaitez-vous continuer ?", "reinitialize": "Êtes vous sûr(e) de vouloir réinitialiser ces informations ? Les fichiers déposés seront supprimés et les données seront réinitialisées à leur état par défaut.", "activationInternal": "Êtes vous sûr(e) de vouloir activer le partenariat avec l'habilitation choisie ? Le partenariat va être ajouté automatiquement et passera alors dans Wedof à l'état \"Partenariat actif\".", "revocationInternal": "Êtes vous sûr(e) de vouloir révoquer le partenariat ? Le partenariat va être retiré automatiquement et passera alors dans Wedof à l'état \"Partenariat révoqué\".", "suspensionInternal": "Êtes vous sûr(e) de vouloir suspendre le partenariat ? Le partenariat va être retiré automatiquement et passera alors dans Wedof à l'état \"Partenariat suspendu\"."}, "confirmQuestion": "Souhaitez-vous poursuivre ?", "confirmDeletion": "Êtes-vous sûr(e) de vouloir supprimer cette demande de partenariat non envoyée ?", "wedofClient": "L'organisme est inscrit sur Wedof"}, "audit": {"title": "Audits du partenariat", "partnerTitle": "Historique des audits", "generate": "Générer le document d'audit", "createReportTemplate": "<PERSON><PERSON><PERSON> le modèle de rapport d'audit", "updateReportTemplate": "Modifier le modèle de rapport d'audit", "subtitle": "Vous pouvez dès à présent générer un document d'audit pour un partenaire", "criterias": {"title": "Critères d'audit des partenaires", "subtitle": "Définissez les critères qui seront évalués pour créer automatiquement le rapport d'audit d'un partenaire.", "dynamicRequirement": "<em>{{scopeTitle}}</em> {{operationTitle}} <b>{{parameter}}</b>", "dynamicRequirementUnary": "<em>{{scopeTitle}}</em> {{operationTitle}}", "add": "Ajouter un critère", "edit": "Modifier le critère d'audit", "settings": "Paramètres des audits", "auditNotAvailable": "L'état du partenariat ne permet pas la création d'un nouvel audit", "form": {"code": {"title": "Identifiant", "help": "Choisissez un identifiant unique pour la variable qui sera remplie automatiquement dans votre modèle lors de la génération du rapport d'audit. Si vous n'en choisissez pas, Wedof en générera un pour vous."}, "title": "Intitulé", "scope": "Donnée auditée", "placeHolder": "Selectionner un critère", "advice": {"title": "Conseils de mise en conformité", "help": "Rédigez des conseils à destination du partenaire afin de l'aider à rentrer en conformité s'il ne satisfait pas ce critère."}, "operation": {"title": "Exigence"}, "severity": {"title": "Est-ce que le critère entraîne une non-conformité ?", "none": "Non", "majeure": "<PERSON><PERSON>, non-conformité Majeure", "mineure": "Oui, non-conformité Mineure", "tooltip": {"majeure": "La non-conformité du critère entraîne une non-conformité Majeure", "mineure": "La non-conformité du critère entraîne une non-conformité Mineure"}, "disclaimer": "Attention, en cas de non-conformité majeure ou mineur d'un critère la conformité de l'audit du partenaire peut être impactée."}}, "confirmDeletion": "Êtes-vous sûr(e) de vouloir supprimer ce critère portant sur la donnée {{scopeTitle}} ?", "list": {"category": {"aiEvaluated": "Critères évalués par IA (bêta) 🤖", "wedofConnectionAndAccess": "Connexions et partage de données", "qualiopi": "<PERSON><PERSON><PERSON><PERSON>", "trainings": "Catalogue de Formations (Mon Compte Formation)", "certificationFolders": "Examens de certification", "certificationFolderSurveys": "Taux de réponse aux questionnaires de suivi d'insertion professionnelle", "abortCertificationFolders": "<PERSON><PERSON> d'abandon"}, "helper": {"trainings": "Ce critère n'est pertinent que pour les organismes ayant un catalogue de formation sur Mon Compte Formation.", "aiEvaluated": "Ce critère utilise l'intelligence artificielle pour évaluer automatiquement la conformité. L'IA peut commettre des erreurs."}, "wedofConnexionCpf": "Connexion EDOF", "wedofConnexionKairos": "Connexion Kairos (AIF)", "wedofAccess": "Partage des données via Wedof", "qualiopiTrainingAction": "Qualiopi Actions de formation", "qualiopiBilanCompetences": "Qualiopi Bilan de Compétences", "qualiopiVAE": "Qualiopi Actions permettant la VAE", "qualiopiFormationApprentissage": "Qualiopi Actions de formation par apprentissage", "trainingsCount": "Nombre total de formations", "trainingActionsCount": "Nombre total d'actions de formation", "trainingsTitle": "Intitulé des formations", "trainingsTitleAI": "Intitulé des formations par IA (bêta)", "trainingsGoal": "Objectif pédagogique des formations", "trainingsContent": "Contenu des formations", "trainingsContentSummary": "Points forts des formations", "trainingsExpectedResults": "Résultats attendus des formations", "trainingsPrerequisites": "Prérequis des formations", "trainingsCompliance": "Conformité des formations", "trainingsPricingMin": "Prix minimum des formations", "trainingsPricingMax": "Prix maximum des formations", "trainingsPricingAverage": "Prix moyen des formations", "trainingsDurationMin": "Durée minimum des formations (h)", "trainingsDurationMax": "Durée maximum des formations (h)", "trainingsDurationAverage": "Durée moyenne des formations (h)", "trainingsAverageRating": "Évaluation moyenne", "trainingsTeachingMethod": "Modalité d'enseignement des formations", "trainingsZone": "Zone géographique des formations", "takeRate": "<PERSON><PERSON> de <PERSON>", "takeRate1Month": "Taux de passage à 1 mois", "takeRate3Months": "Taux de passage à 3 mois", "takeRate6Months": "Taux de passage à 6 mois", "takeRateStrict1Month": "Taux de passage strict à 1 mois", "takeRateStrict3Months": "Taux de passage strict à 3 mois", "takeRateStrict6Months": "Taux de passage strict à 6 mois", "successRate": "<PERSON><PERSON>", "skillSets": "Blocs de compétences", "initialExperienceAnsweredRate": "Taux de réponse au questionnaire initial", "sixMonthExperienceAnsweredRate": "Taux de réponse au questionnaire après 6 mois", "longTermExperienceAnsweredRate": "Taux de réponse au questionnaire après 1 an", "abortRate": "<PERSON><PERSON> d'abandon", "abortRateBeforeTraining": "Taux d'abandon avant formation", "abortRateInTraining": "Taux d'abandon pendant formation", "abortRateAfterTraining": "Taux d'abandon après formation"}}, "template": {"template": "Modèle d'audit", "title": "Audit des partenaires", "titleTemplates": "Modèles d'audit des partenaires", "subtitleTemplates": "Définissez les modèles d'audit à réaliser sur vos partenaires", "create": "Créer un nouveau modèle d'audit", "update": "Modifier le modèle de rapport", "delete": "Supprimer le modèle d'audit", "pendingCreation": "Le modèle est en cours de création, veuil<PERSON><PERSON> patienter", "updateCriterias": "<PERSON><PERSON><PERSON> les critères", "automaticAudit": {"complete": {"title": "Clô<PERSON>r les audits", "help": "A partir de la conformité des partenaires sur les différents critères, le résultat de l'audit est déterminé automatiquement et l'audit est clôturé. Attention, le rapport pourra être généré mais ne pourra pas être modifié."}, "updateCompliance": {"title": "Mettre à jour la conformité des partenaires", "help": "Le résultat de l'audit affecte la conformité du partenaire (par ex. en cas de non conformité majeure sur un critère, l'audit est 'Non conforme' et le partenaire passe donc à 'Non conforme' sur cette certification)"}, "suspend": {"title": "Suspendre les partenaires en cas de non-conformité majeure", "help": "Si une non-conformité majeure est détectée, le partenaire est suspendu sur cette certification"}, "suspendedNotAllowed": "La suspension de partenaire n'est pas autorisé sur une certification inactive", "partnerCompliance": "Auditer les partenaires dont la conformité actuelle est", "summary": "{{subtitle}}"}, "auditPartners": {"title": "Lancer un audit général", "create": "Lancer l'audit général", "messageKey": "Vous êtes sur le point de générer un audit pour vos partenaires à l'état Actif.", "feedback": "{{auditCount}} audits ont été générés sur la certification {{certificationName}}"}, "confirmDeletion": "Êtes-vous sûr(e) de vouloir supprimer ce modèle d'audit ?", "allowVisibilityPartner": "Le partenaire peut visualiser l'audit une fois clôturé", "form": {"name": "Intitulé", "allowVisibilityPartner": "Visible pour le partenaire une fois clôturé"}, "auditCount": "Nombre d'audits réalisés sur les partenaires", "table": {"name": "<PERSON><PERSON><PERSON><PERSON>", "criterias": "Critères", "audits": "Audits", "date": "<PERSON>"}, "deactivate": {"menu": "Désactiver la gestion des audits", "subtitle": "Attention, vous êtes sur le point de désactiver la gestion des audits sur cette certification ({{certificationName}}). Toute désactivation entraîne la suppression totale des modèles d'audits, des audits générés et de l'état de conformité de vos partenaires. Êtes-vous sûr de vouloir continuer ?"}}, "ai": {"train": "Entraîner l'IA", "trainSuccess": "L'IA a été entraînée avec succès !", "trainingTitle": {"summary": "Indiquez si les exemples de titres de formations suivants sont conformes ou non, et justifiez votre réponse.", "question": "Ce titre est-il conforme ?", "justification": {"title": "Justification", "placeholder": "Expliquez pourquoi ce titre est conforme ou non"}, "titlePlaceholder": "Ajouter un nouvel intitulé de formation", "add": "Ajouter cet intitulé"}, "highPrecision": "Précision de l'IA élévée", "midPrecision": "Précision de l'IA moyenne", "lowPrecision": "Précision de l'IA trop faible - Entraînez l'IA", "trainingLoading": "Entraînement en cours...", "trainConfirm": "Confirmer et entraîner l'IA", "loadingTitles": "Chargement des intitulés de formations...", "loadingTitlesError": "Erreur lors du chargement des intitulés de formation. Veuillez réessayer plus tard."}, "pendingCancellation": {"title": "La gestion des audits de vos partenaires pour cette certification est en cours de désactivation. L'historique sera supprimé au renouvellement de votre abonnement.", "reactivateButton": "Réactiver la gestion des audits"}, "activateContact": "Contactez votre référent Wedof pour activer la gestion des audits"}}, "evaluation": {"noEvaluation": "L'organisme n'a pas encore d'évaluation associée à la certification."}, "files": {"title": "Documents", "noData": "Ajouter un type de document attendu", "noDataPartner": "Aucun document n'est requis", "subtitlePartnership": "Liste des documents associés au partenariat", "manageFiles": "Gérer vos documents", "certification": {"partnerFiles": "Ajouter un document visible pour vos partenaires", "publicFiles": "Ajouter un document visible dans le catalogue des certifications"}, "addFile": "Ajouter un ", "upload": "Ajouter un document", "toolTip": {"visibilityAttendeeAndUploaded": "Document visible dans l'Espace Apprenant", "uploadByAttendee": "Document déposable par l'apprenant depuis l'Espace Apprenant"}, "required": "Requis pour l'état : {{toState}}", "autogenerated": "Généré automatiquement à l'état : {{toState}}", "onRequestGenerated": "Générable à tout état", "download": "Télécharger le document", "open": "Ouv<PERSON>r le document", "confirmDeletionComment": "Attention, en supprimant le document vous allez perdre le commentaire, si vous souhaitez le conserver modifiez l'état du fichier.\n\nÊtes-vous sûr(e) de vouloir supprimer le document {{fileName}} ?", "confirmDeletion": "Êtes-vous sûr(e) de vouloir supprimer le document {{fileName}} ?", "link": {"label": "Lien vers le document", "placeholder": "https://www.exemple.com/mon-fichier.pdf"}, "signedState": {"title": "Le document doit être signé", "none": "Aucune signature collectée", "partially": "Au moins une signature collectée", "completed": "Toutes les signatures ont été collectées"}, "menu": {"open": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "Ajouter un lien", "replaceLink": "Remplacer le lien", "download": "Télécharger", "downloadUnavailable": "Le document ne peut pas être téléchargé tant que toutes les signatures n'ont pas été collectées", "upload": "Attacher un document", "replaceUpload": "Remplacer le document", "drive": "Document Google Drive", "replaceDrive": "Remplacer par Document Google Drive", "uploadViaLink": "Ajouter à partir d'un lien", "reUploadViaLink": "Remplacer à partir d'un lien"}, "next": "Document suivant", "previous": "Document précédent", "state": {"toFileState": {"toReview": "À vérifier", "valid": "Valider", "refused": "Refuser"}, "fileState": {"toReview": "À vérifier", "valid": "<PERSON><PERSON><PERSON>", "refused": "<PERSON><PERSON><PERSON><PERSON>"}, "fileStateLong": {"added": "Document ajouté", "updated": "Document mis à jour", "deleted": "Document supprimé", "toReview": "Document à vérifier", "valid": "Document validé", "refused": "Document refusé"}, "tooltip": {"toReview": "Document à vérifier", "valid": "Document validé", "refused": "Document refusé"}, "explanation": {"toReview": "Le document est actuellement <b>À vérifier</b>.", "valid": "Le document est actuellement <b><PERSON><PERSON></b>.", "refused": "Le document est actuellement <b><PERSON><PERSON><PERSON><PERSON></b>."}, "attendeeTooltip": {"toReview": "Le document est à vérifier par l'organisme {{owner}}", "refused": "Le document a été refusé par l'organisme {{owner}}"}, "allEvents": "Tous les événements sur les documents"}, "form": {"label": "Commentaire", "comment": "Ajou<PERSON>z un commentaire à destination {{roles}}"}, "copySuccess": "Le lien du document a été copié dans le presse-papier", "copySuccessAttendee": "Le lien vers le document dans l'Espace Apprenant a été copié dans le presse-papier", "copySuccessAttendeeUpload": "Le lien vers l'Espace Apprenant a été copié dans le presse-papier", "limitedDocument": "Document limité à certain(e)s", "candidate": {"surveyNotOptional": "Vous avez un ou plusieurs questionnaire(s) de suivi d'insertion professionnelle en attente de réponse. Vous devez y répondre afin de télécharger votre parchemin.", "surveyOptional": "Vous avez un ou plusieurs questionnaire(s) de suivi d'insertion professionnelle en attente de réponse. Souhaitez-vous répondre au questionnaire avant de télécharger votre parchemin ?", "answerSurvey": "Répondre au questionnaire", "downloadCertificate": "Télécharger le parchemin"}}, "fileTypes": {"types": {"link": "lien ou URL Google Drive", "all": "Tout type de fichier", "pdf": "PDF (.pdf)", "doc": "Word (.doc, .docx)", "xls": "Excel (.xls, .xlsx)", "ppt": "PowerPoint (.ppt, .pptx)", "txt": "Text (.txt)", "img": "Image (.png, .jpg)", "archive": "Archive (.zip, .rar, .7z, .ace, .tar.gz)"}, "documentation": "Voir la documentation associée afin de créer le parfait modèle de document", "add": "Ajouter un document attendu", "update": "Modifier le document attendu", "generated": "Document généré automatiquement", "notGenerated": "Document à déposer", "title": "Documents attendus", "table": "Liste des documents attendus", "freeFile": {"label": "Activer l'ajout de documents libres", "errors": {"activated": "Les documents libres sont déjà autorisés", "deactivated": "Les documents libres sont déjà interdits"}, "authorizeOrNot": "L'ajout de documents libres est actuellement <b>{{state}}</b>", "activate": "Activer", "deactivate": "Désactiver"}, "form": {"generated": {"title": "Générer automatiquement", "help": "Le document sera généré automatique avec le modèle de document transmis à l'état indiqué", "infoSubscribe": "La génération automatique des documents est disponible dans Wedof", "infoApplication": "Activez l'application Génération de documents pour générer automatiquement vos documents"}, "name": {"title": "Nom du document", "placeholder": "Dossier d'admission"}, "tags": {"help": "Afficher le document uniquement sur les dossiers ayant au moins un des tags choisis"}, "certifications": {"title": "Certifications", "help": "Afficher le document uniquement sur les dossiers associés à une des certifications choisies"}, "accept": {"title": "Type de fichier", "placeholder": "Choisir un type de fichier"}, "description": {"title": "Description du document", "placeholder": "Dossier d'admission du candidat à la certification"}, "template": {"title": "Modèle du document"}, "toState": {"generateTitle": "Générer à l'état", "title": "Requis pour passer à l'état {{toState}}", "mandatoryState": "Aucun état, généré à la demande", "preview": {"formInvalid": "V<PERSON> devez renseigenr tous les champs requis afin de prévisualiser le document", "create": "V<PERSON> devez créer le type de document afin de visualiser le modèle de document", "update": "Attention, le document sera automatiquement sauvegardé si vous téléchargez l'aperçu"}}, "attendeeTitleCertificationEntity": "Espace Candidat", "attendeeTitleRegistrationEntity": "Espace Apprenant", "partnerTitle": "Partenaire", "allowVisibilityAttendee": {"title": "Visible", "help": "L'apprenant / candidat peut visualiser le document dans son espace."}, "allowSignAttendee": {"title": "Nécessite une signature", "help": "L'apprenant / candidat doit signer le document."}, "allowUploadAttendee": {"title": "Déposable", "help": "L'apprenant / candidat peut déposer le document dans son espace."}, "allowVisibilityPartner": {"title": "Visible", "help": "Le partenaire peut visualiser le fichier."}, "allowSignPartner": {"title": "Nécessite une signature", "help": "Le partenaire doit signer le document."}, "allowUploadPartner": {"title": "Déposable", "help": "Le partenaire peut déposer le fichier."}, "allowVisibilityPublic": {"title": "Public", "help": "Le document est public : visible par tous les organismes."}, "qualiopiIndicators": {"title": "Indicateurs Qualiopi", "placeholder": "Aucun indicateur qualiopi associé", "help": "Les indicateurs Qualiopi seront associés à l'activité lors de l'ajout du document"}}, "isAutoGenerate": "Document généré automatiquement"}, "organism": {"organism": "Organisme", "updateSiret": "Vous souhaitez modifier le numéro de SIRET associé à votre compte ? <a target='_blank' href='/assistance/guides/utilisateurs/changement-siret'>Merci de consulter la documentation.</a>", "siret": "Siret : {{siret}}", "customize": "Configuration", "catalogMcf": "Gestion de mon catalogue Mon Compte Formation", "sendAs": {"title": "Personnalisation de l'expéditeur des emails", "unknown": "<PERSON>'adresse <b>{{email}}</b> n'est pas utilisée comme expéditeur des emails envoyés par Wedof", "notActivated": "A<PERSON> de valider votre nouvel expéditeur, <b>cliquez sur le lien</b> que vous avez reçu sur la boîte <b>{{email}}</b>", "activated": "L'adresse <b>{{email}}</b> est utilisée comme expéditeur des emails envoyés par Wedof avec le nom d'expéditeur <b>{{name}}</b>. <a href='mailto:<EMAIL>' target='_blank'>Contactez le support pour changer le nom.</a>"}, "certifier": "Organisme certificateur", "training": "Organisme de formation", "certifierAndTraining": "Organisme de formation & certificateur", "manageMyRegistrationFolderFiles": "Gestion des dossiers de formation", "registrationFolderFileTypesSubtitle": "Documents associés à chaque dossier de formation", "nondiffusible": "L'organisme a choisi de ne pas diffuser ses informations au répertoire Sirene ([ND] = Non Diffusible)", "qualiopi": {"subtitle": "Mis à jour le {{today}}", "trainingAction": "Actions de formation", "bilanCompetences": "Bilan de Compétences", "vae": "Actions permettant de faire valider les acquis de l'expérience", "formationApprentissage": "Actions de formation par apprentissage"}, "userOrganism": {"userSuccessfullyInvited": "L'utilisateur {{email}} a bien été associé à {{organism}}", "userSuccessfullyUpdated": "L'utilisateur {{email}} a bien été mis à jour", "userSuccessfullyCreatedAndInvited": "Un compte Wedof a été créé et associé à votre organisme avec l'adresse mail {{email}}", "userSuccessfullyRevoked": "L'utilisateur a bien été révoqué de votre organisme", "label ": "Ajouter un utilisateur à cet organisme", "associateUser": "Associer {{firstName}} {{lastName}} à l'organisme {{organism}}", "createUser": "<PERSON><PERSON><PERSON> le compte {{email}} sur cet organisme", "placeholder": "Saisis<PERSON>z une adresse email pour créer un compte ou rechercher un utilisateur", "help": "Les utilisateurs que vous ajoutez auront toutes les permissions sur votre organisme et ses dossiers à part le droit d'ajouter d'autres utilisateurs (en tant que propriétaire de l'organisme, cette permission vous est réservée).", "helpUpdate": "Modifier l'utilisateur", "helpRemove": "Révoquer l'accès de {{email}} à votre organisme", "helpPassword": "Demander la réinitialisation du mot de passe de {{email}}", "confirmDeletion": "Êtes-vous sûr(e) de vouloir révoquer l'accés de {{email}} ?", "confirmNewPassword": "Une demande de renouvellement de mot de passe a été envoyée à {{email}}.", "notFound": "L'utilisateur n'a pas été trouvé", "userAlreadyInvited": "L'utilisateur est déjà associé à votre organisme", "listUserInvited": "Utilisateurs", "limitReached": "Vous avez atteint la limite du nombre d'utilisateurs pour votre organisme. Si vous souhaitez associer un nouvel utilisateur, passez à l'offre supérieure.", "table": {"user": "Utilisa<PERSON>ur", "firstName": "Prénom", "lastName": "Nom", "email": "Email", "accountType": "<PERSON><PERSON><PERSON>", "actions": "Actions"}, "create": {"title": "C<PERSON>er un utilisateur", "submit": "<PERSON><PERSON><PERSON> et associer", "password": "Mot de passe (vide = généré aléatoirement)", "passwordCheck": "Confirmer le mot de passe"}, "update": {"title": "Modifier l'utilisateur", "submit": "Mettre à jour"}, "firstName": "Prénom", "lastName": "Nom de famille", "email": "Email", "phoneNumber": "Numéro de téléphone"}, "catalog": {"export": {"title": "Exporter votre catalogue au format XML Lhéo", "subtitle": "Wedof permet d'exporter votre catalogue EDOF au format XML Lhéo. Modifiez-le comme vous le souhaitez puis réimportez-le pour mettre à jour votre offre de formation.", "partners": "Des difficultés pour mettre à jour votre catalogue EDOF ? Nous vous proposons un export de votre catalogue au format Lhéo afin que vous puissiez le modifier et l'importer.", "success": "L'export a commencé. Quand il sera terminé, vous recevrez un email avec le fichier XML au format Lhéo", "button": {"export": "Exporter"}}, "import": {"title": "Importer votre catalogue dans EDOF au format XML Lhéo", "button": {"import": "Importer", "downloadReport": "Rapport d'import"}, "sendFile": "Importez le fichier de catalogue EDOF au format XML Lhéo", "help": "Fournissez un fichier XML au format Lhéo EDOF. Le nom du fichier doit être au format suivant <SIRET>_<nomfichier>.xml et <nomfichier> ne doit pas contenir de point. Votre fichier ne doit pas dépasser 100 Mo.", "errorFile": "Me<PERSON>i de transmettre un fichier XML", "success": "L'import a bien été effectué. Son traitement par EDOF peut prendre jusqu'à plusieurs heures."}}, "importCertificationFolders": {"title": "Importer mon historique de dossiers de certification hors CPF", "feedback": "Import en cours de votre historique"}}, "session": {"title": "{{sessionInfo.trainingTitle}}", "certification": "{{sessionInfo.certificationName}}", "city": "Se déroule à {{sessionInfo.city}}", "registrationFolderCount": "{{sessionInfo.registrationFolderCount}} dossiers", "date": "Du {{startDate}} au {{endDate}}", "trainerQualification": {"title": "Qualification du formateur", "professional": "Ancien professionnel du secteur concerné", "teacher": "Enseignant (lycée, université)", "trainer": "Formateur d'adultes ayant suivi une formation spécialisée au domaine concerné", "engineer": "Ingénieur", "prevention": "<PERSON><PERSON><PERSON><PERSON>", "psychologist": "Psychologue", "responsible": "Responsable qualité - hygiène - sécurité - environnement"}}, "table": {"filter": {"clear": "Supp<PERSON>er le filtre"}}, "form": {"placeholder": "Non renseigné"}, "menu": {"open": "Voir le détail", "close": "Ma<PERSON><PERSON> le d<PERSON>"}, "activities": {"title": "Activités & tâches", "by": " par {{by}}", "markAsDone": "Marquer comme terminée", "isDone": "{{task}} a bien été terminée.", "startTask": "<PERSON><PERSON><PERSON><PERSON> la tâche", "create": {"task": "<PERSON><PERSON><PERSON> une tâche", "activity": "Créer une activité"}, "move": {"treoMessage": "Vous allez déplacer l'activité de cette entité vers une autre entité.", "submit": "<PERSON><PERSON><PERSON><PERSON>", "title": "Déplacer une activité/tâche", "confirmLabel": "Êtes-vous sûr(e) de vouloir déplacer l'activité ?", "chooseType": "Choisir un type", "entityClass": {"label": "Type"}, "entityId": {"label": "Entité", "placeholder": {"CertificationFolder": "1234-4567789", "RegistrationFolder": "I5798642579", "CertificationPartner": "12", "Proposal": "HCGUIF"}}}, "confirmDeletion": "Êtes-vous sûr(e) de vouloir supprimer {{title}} ?", "update": {"task": "Mettre à jour la tâche", "activity": "Mettre à jour l'activité"}}, "invoice": {"noData": "Aucune facture disponible", "title": "Factures", "dueDate": "Date d'échéance : ", "column": {"id": "N°", "description": "Description", "state": "État", "menu": ""}, "type": {"title": "Type", "value": {"invoice": "Facture", "deposit": "Acompte", "creditNote": "Avoir"}}, "state": {"title": "État", "value": {"paid": "Payée", "canceled": "<PERSON><PERSON><PERSON>", "waitingPayment": "En attente"}}, "menu": {"download": "Télécharger", "makePayment": "Payer", "markAsPaid": "Marquer comme Payée", "markAsCanceled": "Marquer comme Annulée", "markAsWaitingPayment": "Marquer comme En attente", "confirmDeletion": "Êtes-vous sûr(e) de vouloir supprimer la facture {{externalId}} ?"}, "form": {"create": "Ajouter une facture", "update": "Modifier la facture", "externalId": "<PERSON><PERSON><PERSON><PERSON>", "paymentLink": {"title": "Lien de paiement", "placeholder": "https://www.mysite/invoice/pay/123456"}, "description": "Description", "dueDate": "Date d'échéance", "chooseFileOrUrl": {"title": "Ajouter un fichier ou un lien", "file": "<PERSON><PERSON><PERSON>", "link": {"title": "<PERSON><PERSON>", "placeholder": "https://www.mysite/invoices/123456"}}}}, "dates": {"fromToSameDay": "Le {{date}}", "fromTo": "Du {{startDate}} au {{endDate}}"}, "shortcut": {"certification": "Certification", "partnership": "Gestion des partenariats", "partner": "Partenariat", "certificationFolders": "Gestion des dossiers", "xml": "Accrochage", "certifierAccess": "Partage de données", "statistics": "Statistiques", "organism": "Organisme", "activities": "Activités", "surveys": "Suivi de l'insertion professionnelle", "metadatas": "Donn<PERSON>", "attendee": "Apprenant", "candidate": "Candidat", "certificationFolder": "Dossier de certification", "registrationFolder": "Dossier de formation", "webhook": "Webhooks", "certificateTemplate": "Génération du parchemin", "trainings": "Formations", "messageTemplates": "Messages et notifications", "proposal": "Proposition", "invoices": "Factures", "skills": "Compétences", "audit": "Audit", "workflowRuns": "<PERSON><PERSON>"}, "filters": {"saveAsFilter": "Sauvegarder le filtre", "noFilter": "<PERSON>ut", "existingFilterAlert": "Il existe déjà un filtre identique enregistré : {{filterName}}", "table": {"title": "Gestion des filtres", "filterName": "Nom", "filterColor": "<PERSON><PERSON><PERSON>", "actions": "Actions"}}, "skills": {"title": "Référentiel de compétences", "noData": "Aucune compétence", "noDataSkillSet": "Aucun bloc de compétence déclaré", "deleteUnavailable": "V<PERSON> devez supprimer les compétences associées pour supprimer le bloc de compétence", "confirmDeletion": "Êtes-vous sûr(e) de vouloir supprimer cette compétence {{fullCode}} ?", "form": {"title": {"skillSet": {"update": "Modifier un bloc de compétence", "create": "Ajouter un bloc de compétence"}, "skill": {"update": "Modifier une compétence", "create": "Ajouter une compétence"}}, "skillSet": "Bloc de compétence", "code": "Code", "label": "Titre", "description": "Description", "modalities": "Modalités"}, "placeholderAll": "Tous les blocs de compétences"}, "message-templates": {"noData": "Vous n'avez pas de modèle de message créé", "summary": "Ce modèle de message de type {{type}} est actuellement {{state}}. Il sera envoyé {{delay}} aux destinataires choisis lors des événements {{events}} {{entityClass}} {{certification}} {{hasTag}} {{tags}}", "description": "Ce modèle de message de type {{type}} est actuellement {{state}}. Il sera envoyé {{resend}} {{delay}} aux destinataires choisis lors des événements <b>{{events}}</b> {{entityClass}} {{certification}} {{hasQualiopi}} {{hasTag}} {{tags}}. {{enforceConditionsLabel}}", "descriptionParts": {"typeStateOnly": "Ce modèle de message de type {{type}} est actuellement {{state}}.", "certification": {"all": "et associé à n'importe quelle certification", "multiple": "et associé à {{count}} certification(s)"}, "enforceConditionLabel": "Le message ne sera envoyé que si l'entité respecte toujours les conditions au moment de l'envoi.", "delay": {"instant": " <b>immédiatement</b> ", "withDelay": "avec un délai de <b>{{delayQuantity}} {{delayUnit}}</b>"}, "allowResend": {"resend": " / renvoyé ", "onetime": " une seule fois et "}, "withTags": "et avec le(s) tag(s)", "withQualiopi": "en adéquation avec le(s) indicateur(s) Qualiopi"}, "add": "Ajouter un nouveau modèle de message", "settings": "G<PERSON>rer vos modèles de messages", "column": {"title": "Nom du modèle", "type": "Type"}, "formTitle": {"configuration": "Configuration", "writing": "Rédaction", "options": "Options"}, "title": "Nom du modèle", "subject": "Sujet du message", "menu": {"delete": "<PERSON><PERSON><PERSON><PERSON>", "activate": "Activer", "deactivate": "Désactiver", "duplicate": "<PERSON><PERSON><PERSON><PERSON>", "confirmDeletion": "Êtes-vous sûr(e) de vouloir supprimer ce modèle de mail {{title}} ?"}, "type": {"title": "Type", "value": {"email": "Email", "sms": "SMS"}, "countSms": "Environ {{count}} SMS"}, "sendAs": {"title": "Expéditeur", "matTooltip": "Les expéditeurs sont gérés au niveau de la personnalisation de votre organisme", "placeholder": "Ajou<PERSON>z un expéditeur dans la configuration de votre organisme"}, "replyTo": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "matTooltip": "Vous pouvez ajouter plusieurs adresses mail séparées par des virgules (ex: <EMAIL>,<EMAIL>)"}, "state": {"title": "État", "value": {"active": "Actif", "inactive": "Inactif"}}, "certifInfos": {"title": "Liste des certifications", "noEntries": "Aucune certification trouvée", "placeholder": "Choisir une ou plusieurs certifications", "tooltip": "Sélectionnez une ou plusieurs certifications pour lesquelles vous souhaitez envoyer un message. Si aucune certification n'est séléctionnée, par défaut tous les mails seront envoyés à toutes les certifications."}, "entityClass": {"title": "Entité", "value": {"RegistrationFolder": "Dossier de formation", "CertificationFolder": "Dossier de certification", "CertificationPartner": "Partenariat de certification", "Proposal": "Proposition commerciale", "CertificationFolderSurvey": "Enquête de suivi d'insertion professionnelle"}}, "delay": "<PERSON><PERSON><PERSON> d'envoyer :", "enforceConditions": "Re-vérifier les conditions à l'expiration du délai", "qualiopiIndicators": "Indicateurs Qualiopi", "qualiopiIndicatorsTooltip": "Les indicateurs Qualiopi seront associés à l'activité lors de l'envoi du message", "allowResend": {"title": "Autoriser l'envoi multiple", "help": "Permettre l'envoi de ce message plusieurs fois à une même entité"}, "cc": "Ajouter un destinataire en cc", "cci": "Ajouter un destinataire en cci", "to": {"title": "Ajouter un destinataire", "matTooltip": {"email": "Choisir votre destinataire parmi l'apprenant, le certificateur, le partenaire ou vous-même. Utilisez les variables mise à votre disposition pour ajouter dynamiquement un destinataire. Vous pouvez ajouter plusieurs adresses mail séparées par des virgules (ex: <EMAIL>,<EMAIL>)", "sms": "Choisir votre destinataire parmi l'apprenant, le certificateur, le partenaire ou vous-même. Utilisez les variables mise à votre disposition pour ajouter dynamiquement un destinataire. Vous pouvez ajouter plusieurs numéros de téléphones séparés par des virgules (ex: 0612121212,0702020202)"}}, "body": {"title": "Contenu du message", "addNewData": "Vous souhaitez ajouter une autre donnée ?", "seeDictionnary": "Retrouvez l'ensemble des variables dans notre documentation", "tooltip": "V<PERSON> pouvez insérer du contenu HTML"}, "events": {"title": "Événements", "placeholder": "Choisir un ou plusieurs événement(s)", "allFolderEvent": "Tous les événements sur le dossier", "allSurveyEvent": "Tous les événements sur une enquête", "allCertificationPartnerEvent": "Tous les événements sur le partenariat de certification", "allProposalEvent": "Tous les événements sur la proposition commerciale", "proposalApplied": "Proposition appliquée", "proposalCreated": "<PERSON><PERSON><PERSON>", "proposalUpdated": "Mis à jour", "proposalDeleted": "Supprimée"}, "tags": {"tooltip": "Envoyer le message uniquement aux dossiers portant le(s) tag(s)"}, "actions": {"send": "Recevoir un message test", "testSend": "Un {{type}} de test vient de vous être envoyé sur {{sendTo}}", "matToolTip": "<PERSON><PERSON> de<PERSON> créer ou mettre à jour votre modèle de message afin de l'essayer"}, "messages": {"noData": "Aucun message", "state": {"sent": "<PERSON><PERSON><PERSON>", "notSent": "Non envoyé", "notSentUnauthorized": "Non envoyé", "notSentEnforcedConditions": "Non envoyé", "notSentMissingData": "Non envoyé", "failed": "<PERSON><PERSON><PERSON>", "scheduled": "Programmé", "toSend": "À envoyer"}, "scheduled": "Programmé pour être envoyé par {{type}} {{date}} à {{email}}", "sent": "<PERSON><PERSON><PERSON> par {{type}} {{date}} à {{email}}", "failed": "Erreur sur l'envoi du message qui aurait du être envoyé par {{type}} {{date}} à {{email}}", "notSent": "Le message n'a pas pu être envoyé.", "notSentUnauthorized": "Le message n'a pas pu être envoyé à cause de votre abonnement / application.", "notSentEnforcedConditions": "Le message n'a pas été envoyé, les conditions à l'expiration du délai ne sont plus respectées.", "notSentMissingData": "Le message n'a pas été envoyé, des informations nécessaires sont manquantes (adresse du destinataire).", "menu": {"state": {"scheduled": "Envoyer maintenant", "sent": "Envoyer à nouveau", "notSentEnforcedConditions": "Forcer l'envoi", "notSentMissingData": "Retenter l'envoi", "failed": "Retenter l'envoi"}, "show": "Voir en ligne", "cancel": "Annuler l'envoi", "confirmDeletion": "Êtes-vous sûr(e) de vouloir supprimer ce message {{subject}} ?"}}, "search": "Rechercher une variable"}, "certificationFolderSurvey": {"created": "Questionnaire \"Situation professionnelle en début de cursus\" est accessible (Enquête créée)", "sixMonthExperienceAvailable": "Questionnaire \"Situation professionnelle de 6 mois\" est accessible", "longTermExperienceAvailable": "Questionnaire \"Situation professionnelle au moins un an\" est accessible", "initialExperienceAnswered": "Questionnaire \"Situation professionnelle en début de cursus\" répondu", "sixMonthExperienceAnswered": "Questionnaire \"Situation professionnelle de 6 mois\" répondu", "longTermExperienceAnswered": "Questionnaire \"Situation professionnelle au moins un an\" répondu"}, "workingContract": {"title": "Contrat d'apprentissage", "externalIdTrainingOrganism": "Numéro externe", "externalIdDeca": "Numéro DECA", "financer": {"title": "Financeur", "opcoCfaAtlas": "Atlas", "opcoCfaAfdas": "<PERSON><PERSON><PERSON>", "opcoCfaEp": "OPCO EP", "opcoCfaMobilites": "OPCO Mobilités", "opcoCfaAkto": "Akto", "opcoCfaOcapiat": "Ocapiat", "opcoCfaUniformation": "Uniformation", "opcoCfa2i": "OPCO 2i", "opcoCfaConstructys": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "opcoCfaSante": "OPCO Santé", "opcoCfaOpcommerce": "L'Opcommmerce"}, "state": {"title": "État", "draft": "Brouillon", "sent": "Transmis", "pendingAcceptation": "En cours d'instruction", "accepted": "Engagé", "cancelled": "<PERSON><PERSON><PERSON>", "refused": "<PERSON><PERSON><PERSON><PERSON>", "broken": "Rupture", "completed": "Soldé"}, "type": {"title": "Type", "11": "Premier contrat d’apprentissage de l’apprenti", "21": "Nouveau contrat avec un apprenti qui a terminé son précédent contrat auprès d’un même employeur", "22": "Nouveau contrat avec un apprenti qui a terminé son précédent contrat auprès d’un autre employeur", "23": "Nouveau contrat avec un apprenti dont le précédent contrat auprès d’un autre employeur a été rompu", "31": "Modification de la situation juridique de l’employeur", "32": "Changement d’employeur dans le cadre d’un contrat saisonnier", "33": "Prolongation du contrat suite à un échec à l’examen de l’apprenti", "34": "Prolongation du contrat suite à la reconnaissance de l’apprenti comme travailleur handicapé", "35": "Modification du diplôme préparé par l’apprenti", "36": "Autres changements (maître d’apprentissage, de durée de travail hebdomadaire, réduction de durée, etc.)", "37": "Modification du lieu d’exécution du contrat", "38": "Modification du lieu principal de réalisation de la formation théorique"}, "amount": "Engagement", "startDate": "Date de début du contrat", "endDate": "Date de fin du contrat", "signedDate": "Date de conclusion du contrat", "amendmentDate": "Date d'effet de l'avenant", "breakingDate": "Date de rupture", "employer": "Employeur"}, "total": "Total"}, "layout": {"user": {"actions-menu": {"info": "Mon compte", "application": "Mes applications", "settingsSubscription": "Mon abonnement", "sign-out": "Déconnexion"}, "subscription": {"select": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "try": "<PERSON><PERSON><PERSON> gratuit", "access": "Mon accès", "monthly": "HT / mois", "yearly": "HT / an", "monthlyAbo": "Abonnement mensuel", "priceBy-folder": "Facturation au dossier", "by-folder": "HT / dossier", "annual": "Forfait annuel", "smsSent": {"tooltip": "Période de facturation des SMS : du {{startDate}} au {{endDate}}", "title": "SMS envoyés (0,06€ HT / sms)"}, "audit": {"title": "Auditer rapidement vos partenaires", "description": "Critères personnalisables", "description2": "Contrôler la conformité de votre réseau", "activate": "Activer l'option sur chacune de vos certifications"}, "training": {"none": {"offer": "Aucun abonnement", "badge_subscribe": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "badge": "Essayer"}, "free": {"offer": "<PERSON><PERSON><PERSON>", "badge": "<PERSON><PERSON><PERSON>"}, "access": {"offer": "Access", "badge": "Access", "price": "49€ "}, "access_plus": {"offer": "Access +", "badge": "Access +", "price": "99€ "}, "trial": {"offer": "Essai en cours", "badge": "<PERSON><PERSON><PERSON>", "remaining-days": "Jour(s) restant(s)"}, "api": {"offer": "API", "badge": "API", "price_2023": "99€ ", "price": "199€ "}, "essential": {"offer": "Essentiel", "badge": "Essentiel", "price_2023": "149€ ", "price": "249€ "}, "standard": {"offer": "Standard", "badge": "Standard", "price_2023": "349€ ", "price": "399€ "}, "premium": {"offer": "Premium", "badge": "Premium", "price_2023": "649€ ", "price": "699€ "}, "annual": "Profitez d'une remise en choissant l'<b>offre annuelle</b> quand vous validerez votre nouvel abonnement"}, "certifier": {"foldersCountExplanation": "gratuit au delà de {{count}} dossiers / mois", "foldersCountExplanationAccess": "Jus<PERSON><PERSON>'à {{count}} dossiers / an", "none": {"offer": "Aucun abonnement", "badge_subscribe": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "badge": "Essayer"}, "free": {"offer": "<PERSON><PERSON><PERSON>", "badge": "<PERSON><PERSON><PERSON>"}, "trial": {"offer": "Essai en cours", "badge": "<PERSON><PERSON><PERSON>", "remaining-days": "Jour(s) restant(s)"}, "usage": {"offer": "<PERSON>ur", "subtype": {"standard": "Standard", "premium": "Premium"}}, "access": {"offer": "Certificateur Access", "badge": "Access"}, "unlimited": {"offer": "Certificateur Premium (annuel)", "badge": "Premium"}, "partner": {"offer": "Partenaire", "badge": "Partenaire"}, "partnerPlus": {"offer": "Partenaire +", "badge": "Partenaire +"}, "folders": {"header": "Suivi des dossiers", "explanation": "Suivez le parcours de certification de vos candidats"}, "synchronization": {"header": "Synchronisation", "explanation": "Synchronisez Wedof avec France Compétences, les OF partenaires, Mon Compte Formation / CDC"}, "autoDeposit": {"header": "Accrochage automatique", "explanation": "Créez et déposez automatiquement vos accrochages pour la caisse des dépôts."}, "api": {"header": "API", "explanation": "Accédez à l'API pour automatiser les tâches"}, "webhooks": {"header": "Webhooks (hors no-code)", "explanation": "Créez des Webhooks pour déclencher des automatisations"}, "apps": {"header": "Applications", "explanation": "Connectez Wedof avec vos outils (Salesforce, Slack, Ici Formation...)"}, "nocode": {"header": "No-code", "explanation": "Automatisez Wedof avec les outils no-code (Activepieces, n8n, <PERSON><PERSON><PERSON>, Make...)"}, "adequation": "Vérification adéquation des formations - bêta**", "openData": "Synchronisation OpenData", "messageTemplates": "Messages et notifications", "surveys": "Enquête Suivi d'insertion professionnelle", "audit": "Audit des partenaires", "documents": "Génération de documents", "promoteCertification": "Mise en avant de vos certifications", "generateCertificate": "Parchemins de certification", "attendee": "Espace Candidat", "partnerStatistics": "Statistiques sur les partenaires", "support": "Support", "supportStandard": "Mail", "supportPremium": "Prioritaire (mail, téléphone...)", "managePartnership": "Gestion des partenaires", "importExport": "Import & Export Excel des dossiers de certifications", "additionalCost": {"price": "990€ HT", "priceAccess": "495€ HT", "header": "Frais de mise en service", "title": "Gestion des dossiers historiques des partenaires avant la date initiale d'abonnement ainsi que la mise en place"}, "additionalCostBeta": "Les fonctionnalités en beta sont disponibles quelque soit votre offre durant la période de test", "additionalCostFree": "Frais de mise en service offerts (d'une valeur de 990€ HT) pour toute souscription effective avant le 31/05/2023 inclus.", "goAnnual": "-10% avec un abonnement annuel"}, "mySubscription": "Mon abonnement", "mySubscriptions": "Mes abonnements", "manageMySubscription": "<PERSON><PERSON><PERSON> mon abonnement", "registrationFoldersLimit": "Limite de dossiers de formation {{period}}", "certificationFoldersCount": "Dossiers de certification (période en cours)", "certificationFoldersCountAccessMonthly": "Dossiers de certification (période mensuelle en cours)", "certificationFoldersCountAccessAnnual": "Quota de dossiers de certification (créés)", "activeApps": "Applications actives", "workflow": "Processus Métiers Automatisés", "signature": "Signature Electronique", "subscriptionOptionStates": {"enabled": "Actif", "disabled": "Inactif", "trial": "<PERSON><PERSON><PERSON> gratuit", "pending_disable_trial": "Désactivation prévue", "pending_enable": "Activation en cours", "pending_enable_trial": "Activer l'essai gratuit", "pending_disable": "Désactivation prévue"}, "habilitation": "Support aux habiliations", "manageCertifierContact": "Mise en relation Certificateur", "autoEvaluationAudit": "Auto-évaluation avant un audit", "updateTrainingActions": "Mise à jour des actions de formation suite à un audit non conforme", "allowAudits": "Certifications avec gestion des audits", "startDate": "Date de début de l'abonnement", "startTrial": "Date de début de la période d'essai", "endDate": "Date de fin d'abonnement", "period": "Période de facturation", "fromTo": "du {{from}} au {{to}}", "endTrial": "Date de fin de la période d'essai", "cancellation": "Annulation", "pendingCancellation": "En cours d'annulation", "renewDate": "Date de fin de période", "switchTrialCertifier": "Découvrez gratuitement l'offre Organisme certificateur \"Premium\" pendant 15 jours", "switchTrialTraining": "Découvrez gratuitement l'offre Organisme de formation \"Premium\" pendant 15 jours", "myPartner": "Accès", "myPartnerAccess": "Partenaire de certification", "invoices": {"downloadInvoice": "Voir mes factures", "downloadLast": "Télécharger ma dernière facture"}, "included": {"folderLimit": "10 dossiers / mois", "folderLimitAccess": "50 dossiers / an", "folderUnlimited": "Dossiers illimités", "api": "Connecteur EDOF et Webhooks", "apps": "Apps (Slack, Salesforce...)", "financial": "Statistiques financières", "proposals": "Création de propositions", "inscriptions": "Inscriptions automatisées", "standardIncluded": "Fonctionnalités de l'abonnement Standard"}, "changeSubscription": "Changer d'abonnement Formation", "chooseSubscription": "Choisir un abonnement Formation", "changeSubscriptionCertifier": "Changer d'abonnement Certification", "chooseSubscriptionCertifier": "Choisir un abonnement Certification", "enableApp": "Activer l'application", "tryApp": "Essayer l'application", "modal": {"title": {"certifier": "Abonnement Espace Certification", "training": "Abonnement Espace Formation"}, "access": {"certifier": "Voir mon abonnement Certification", "training": "Voir mon abonnement Formation"}, "subtitle": "Les informations suivantes se réfèrent à votre abonnement actuel", "subtitlePartner": "En tant que Partenaire d'un Organisme Certificateur, vous avez un accès gratuit à Wedof restreint à la gestion des dossiers de certification", "features": "FONCTIONNALITÉS PRINCIPALES"}, "proposals": {"subtitle": "Gérer vos propositions commerciales avec Wedof", "explanation": "Améliorer votre prospection commerciale via l'outil des propositions", "description": {"description1": "Une proposition générique peut être attribuée à plusieurs apprenants,", "description2": "Une proposition individuelle concerne un seul apprenant,", "description3": "Une proposition est affiliée à une ou plusieurs actions de formations,", "description4": "Valider automatiquement les dossiers créés avec la proposition"}, "isNotEnabled": {"subtitle": "Plus que quelques pas", "explanation": "Votre espace de propositions n'a pas encore été initialisé. <PERSON><PERSON>'activer, contactez notre support à l'adresse ci-dessous (nous vous demanderons votre logo au format vectoriel .SVG)"}}, "analytics": {"subtitle": "Vos statistiques en un clin d'oeil grâce à Wedof", "explanation": "Sachez en temps réel l'impact de votre catalogue CPF sur les apprenants ainsi que vos données financières", "description": {"description1": "Vos formations les plus vues,", "description2": "Vos formations les plus cliquées,", "description3": "Votre nombre de dossiers validés, acceptés, annulés,", "description4": "Votre chiffre d'affaires à partir des dossiers acceptés et bien plus encore"}}, "training-folders": {"subtitle": "Gestion des dossiers de formation", "explanation": "Une gestion simplifiée, intuitive et sur mesure de vos dossiers de formation", "description": {"description1": "<PERSON><PERSON><PERSON> votre chiffre d'affaires par état,", "description2": "Traitements par lots,", "description3": "Recherche avancée avec des filtres par session, par formation, par villes, etc,", "description4": "Exports CSV avancés,", "description5": "Données personnalisées : tags, notes privées..."}}, "apps": {"subtitle": "Connectez Wedof avec vos outils digitaux", "explanation": "Connectez Wedof avec vos outils (Salesforce, Slack, Ici Formation...)", "description": {"description1": "Connectez Wedof et Salesforce afin de gagner du temps et de la fiabilité dans le suivi commercial de vos dossiers de formation,", "description2": "Exportez votre catalogue de formation sur Ici Formation,", "description3": "Faites le lien entre Wedof et Slack pour ne manquer aucune notification"}, "workflow": {"subtitle": "Processus Métiers Automatisés", "explanation": "Choisissez une de nos offres d'abonnement puis activez notre option d'automatisation des processus métiers", "description": {"description1": "<PERSON><PERSON><PERSON> des processus métiers sur-mesure,", "description2": "Connectez tous vos outils facilement,", "description3": "Automatisez sans coder (NoCode),", "description4": "Créez des exports personnalisés,", "description5": "Créez des formulaires personnalisés..."}}}, "certification": {"subtitle": "Prenez le contrôle de vos certifications", "explanation": "G<PERSON>rez vos partenariats et synchronisez vos informations avec France Compétences, vos OF partenaires, Mon Compte Formation et la CDC.", "description": {"description1": "<PERSON><PERSON>vez automatiquement les dossiers des candidats à certifier,", "description2": "Mesurez la qualité de vos organismes partenaires,", "description3": "Détectez les fraudes sur vos certifications"}}, "certifierAccess": {"subtitle": "Gérer vos exports XML", "explanation": "Génerez automatiquement vos exports XML vers la Caisse des Dépôts.", "description": {"description1": "Gagnez du temps lors de la génération de vos fichiers XML", "description2": "Cette option est disponible avec l'abonnement Certificateur"}}, "api": {"explanation": "Accédez à l'API pour automatiser les tâches"}, "webhooks": {"explanation": "Créez des Webhooks pour déclencher des automatisations"}, "nocode": {"explanation": "Automatisez Wedof avec les outils no-code (Activepieces, n8n, <PERSON><PERSON><PERSON>, Make...)"}, "automatisationEDOF": {"explanation": "Utilisez les automatisations EDOF"}, "automatisationKairos": {"explanation": "Récupérez les dossiers Kairos AIF"}, "automatisationOpco": {"explanation": "Récupérez les dossiers d'apprentissage (OPCO)"}, "folders": {"explanation": "Nombre de dossiers de formation que vous pouvez créer depuis Wedof ou importer depuis EDOF."}, "help": {"proposal": "Vous êtes intéressé(e) par les propositions et les inscriptions automatisées ? C'est inclus dans l'offre <b>Premium</b>", "apps": "Vous êtes intéressé(e) par les applications et les intégrations ? C'est inclus à partir de l'offre <b>Standard</b>", "statistics": "Vous êtes intéressé(e) par les statistiques financières ? C'est inclus à partir de l'offre <b>Standard</b>", "action": "Souscrivez à une offre à partir de l'offre 'Essentiel' afin de pouvoir agir sur vos dossiers grâce aux fonctionnalités du CRM.", "folders": "Vous êtes intéressé(e) par le CRM des dossiers de formation ? C'est inclus à partir de l'offre <b>Essentiel</b>", "webhook": "Vous êtes intéressé(e) par l'application des Webhooks ? C'est inclus à partir de l'offre <b>API</b>", "attendee": " Vous êtes intéressé(e) par l'espace Apprenant ? C'est inclus à partir de l'offre <b>Standard</b>", "displayTrainingAndTrainingActions": "Vous êtes intéressé(e) par les statistiques et données de formation de vos partenaires ? C'est inclus dans l'offre <b>Certificateur Premium</b>", "managePartnership": "Vous êtes intéressé(e) par la gestion et l'habilitation de vos partenariats ? C'est inclus dans l'offre <b>Certificateur Premium</b>", "manageAudit": "Vous êtes intéressé(e) par l'audit de vos partenaires ? C'est inclus dans l'offre <b>Certificateur Premium</b>", "excelImportExport": "Vous êtes intéressé(e) par l'export et l'import excel des dossiers de certification ? C'est inclus dans l'offre  <b>Certificateur Premium</b>", "generateCertificate": " Vous êtes intéressé(e) par la génération automatique de parchemins de vos dossiers de certification ? C'est inclus dans l'offre  <b>Certificateur Premium</b>", "accrochageDialog": "Vous êtes intéressé(e) par l'accrochage automatique de vos dossiers de certification ? C'est inclus dans l'offre <b>Certificateur Premium</b>", "messageTemplatesTraining": "Vous êtes intéressé(e) par l'application des Messages et notifications ? C'est inclus à partir de l'offre <b>Essentiel</b>", "messageTemplatesCertifier": "Vous êtes intéressé(e) par l'application des Messages et notifications ? C'est inclus dans l'offre <b>Certificateur Premium</b>", "documentCertifier": "Vous êtes intéressé(e) par la génération automatique de vos documents ? C'est inclus dans l'offre <b>Certificateur Premium</b>", "documentTraining": "Vous êtes intéressé(e) par la génération automatique de vos documents ? C'est inclus dans l'offre <b>Access</b>", "surveys": "Vous êtes intéressé(e) par l'export des enquêtes de vos titulaires à France Compétences ? C'est inclus dans l'offre <b>Certificateur Premium</b>", "catalog": "Vous êtes intéressé(e) par l'export / import de votre catalogue MCF ? C'est inclus à partir de l'offre <b>API</b>"}, "table": {"header": {"folders": "Dossiers", "api": "API", "webhooks": "Webhooks (hors no-code)", "apps": "Applications", "nocode": "No-code", "attendee": "Espace Apprenant", "financial": "Statistiques financières", "proposals": "Propositions commerciales", "userLimit": "Utilisateurs", "crm": "CRM", "messageTemplates": "Messages et notifications", "documents": "Génération de documents", "automatisationEDOF": "Automatisation EDOF", "automatisationKairos": "Automatisation Kairos", "automatisationOpco": "Automatisation des OPCO"}, "row": {"foldersLimit": "10 dossiers / mois", "basic": "Usage basique", "limit": "Usage limité"}, "contact": {"question": "Une question sur nos abonnements ?", "questionPartnership": "Vous souhaitez modifier votre abonnement ?", "contact": "Contactez-nous", "contactPartnership": "Contactez {{partnership}}", "phoneNumber": "+ 33 5 54 54 55 53"}}}, "roles": {"ROLE_USER": "Utilisa<PERSON>ur", "ROLE_SALES": "Commercial", "ROLE_OWNER": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ROLE_MISSING": "Inconnu"}, "askForDemo": "Demander une demo"}}, "navigation": {"certificationOrganism": {"title": "Espace certification", "children": {"certificationFolders": {"title": "Dossiers"}, "partners": {"title": "Certifications & partenariats"}}}, "trainingOrganism": {"title": "Espace formation", "children": {"certifications": {"title": "Certifications & partenariats"}, "stats": {"title": "Statistiques"}, "folders": {"title": "Dossiers"}, "proposals": {"title": "Propositions commerciales"}}}, "helpCenter": {"title": "Centre d'aide", "children": {"documentation": {"title": "Documentation"}, "faq": {"title": "FAQ"}, "support": {"title": "Nous contacter"}}}}, "profile": {"user": {"update": "Modifier", "noSync": "Aucune habilitation (EDOF, Kairos-AIF...)", "form": {"title": {"main": "Mon Profil", "info": "Informations de contact", "password": "Modification mot de passe"}, "fields": {"first-name": {"label": "Prénom", "error": "Votre prénom est obligatoire"}, "last-name": {"label": "Nom", "error": "Votre nom est obligatoire"}, "email": {"label": "Email", "error": "Un email valide est obligatoire"}, "phone": {"label": "Téléphone", "error": "Le numéro de téléphone n'est pas valide"}, "address": {"label": "<PERSON><PERSON><PERSON>"}, "password": {"label": "Mot de passe"}, "password-repeat": {"label": "Vérification du mot de passe"}}, "actions": {"submit": "<PERSON><PERSON><PERSON><PERSON>", "update-password": "Modifier mon mot de passe"}}, "logout-confirmation": {"title": "Confirmation de déconnexion", "description": "Vous devez vous reconnecter afin de prendre en compte vos changements.", "actions": {"close": "Se reconnecter"}}}, "organism": {"update": "Configurer", "nondiffusible": "Votre organisme a choisi de ne pas diffuser ses informations au répertoire Sirene ([ND] = Non Diffusible)", "noLinkedInPageUrl": "Renseignez votre page LinkedIn", "linkedInPageUrl": "Allez sur votre page LinkedIn", "form": {"fields": {"name": {"label": "Nom de la société"}, "location": {"label": "<PERSON><PERSON><PERSON>", "error": "<PERSON><PERSON><PERSON> adresse trouvée", "placeholder": "Rechercher une adresse"}, "address": {"label": "Rue"}, "postalCode": {"label": "Code Postal"}, "city": {"label": "Ville"}, "email": {"label": "Email", "error": "Un email doit être valide"}, "phone": {"label": "Téléphone", "error": "Un numéro téléphone à 10 chiffres doit être renseigné"}, "url": {"label": "Site Web"}, "siret": {"label": "SIRET"}, "linkedinPageUrl": {"label": "Page LinkedIn", "placeholder": "https://www.linkedin.com/company/my-page/", "error": "Cette adresse ne semble pas être une page LinkedIn valide"}, "customColorScheme": {"label": "Couleur principale", "placeholder": "#4e6ead"}, "tools": {"title": "Sondage", "subtitle": "Afin de vous proposer de nouvelles intégrations avec des outils existants, nous souhaiterions savoir quels sont les outils que vous utilisez pour les besoins suivants :", "billingSoftware": "Logiciel de facturation", "crm": "CRM utilisé (hors Wedof)"}, "agreement": {"label": "Numéro d'agrément", "errors": {"required": "Le numéro d'agrément doit être renseigné", "pattern": "Le numéro d'agrément doit comporter entre 10 et 12 chiffres"}}, "uaiNumber": {"label": "Numéro UAI (CFA)", "help": "Ne s'applique que pour les Centres de Formation d'Apprentissage"}, "tvaTaux": {"label": "Taux de TVA", "option": {"0": "0 %", "5-5": "5.5 %", "20": "20 %"}, "error": "Votre taux de TVA doit être renseigné"}}}}, "api-token": {"title": "Jetons d'API", "apiDoc": "Documentation de l'API", "actions": {"add": "Générer un jeton", "copy": "<PERSON><PERSON><PERSON> le jeton"}, "lastUsed": " - Dernière utilisation le ", "copySuccess": "Le jeton a été copié dans le presse-papier.", "form": {"title": "Générer un jeton d'API", "labels": {"name": "Nom du jeton"}, "placeholders": {"name": "Nom du jeton"}}, "deletion": "Êtes-vous sûr(e) de vouloir supprimer le jeton **{{name}}** ?"}}, "certification": {"common": {"certification": {"mandatoryFiles": "Livrables"}, "certificate": {"fileType": "Le modèle de parchemin peut être prévisualisé et édité en ligne depuis le type de document \"Parchemin\" de la certification.", "certificateTemplate": "Génération automatique du parchemin", "subtitle": "Générer automatiquement votre parchemin de certification à partir de l'état Réussi d'un dossier de certification.", "certificateTemplateActive": "La génération automatique des parchemins pour votre certification est actuellement : <b>active</b>.", "certificateTemplateNotActive": "La génération automatique des parchemins pour votre certification est actuellement : <b>inactive</b>. Ajouter un modèle de parchemin afin de l'activer.", "mandatory": "Le QR code ne doit pas être supprimer. Vous devez obligatoirement vérifier le modèle pour vérifier que toutes les variables du parchemin soient bien intégrées dans le modèle."}, "coCertifiers": "Attention, ces données sont partagées entre plusieurs certificateurs.", "create": {"title": "Nouvelle certification", "subtitle": "Créer une certification interne dans Wedof", "helper": "Gérez vos certifications non reconnues par France Compétences et préparez ainsi vos dépôts RNCP et RS.", "form": {"create": "Créer la certification", "name": "Nom de la certification", "link": {"label": "Lien de la certification", "placeholder": "Fournissez le lien vers la nouvelle certification (si applicable)"}}}}, "folders": {"createFolder": {"create": "Nouveau dossier", "title": "Nouveau dossier de certification", "form": {"attendee": {"attendee": "Recherchez un candidat par son email ou numéro téléphone portable", "noAttendee": "<PERSON><PERSON><PERSON> trouvé", "label": "Candidat"}, "certification": {"certification": "Certification", "placeholderCertification": "Recherchez une certification", "noCertification": "Aucune Certification trouvée", "tooltip": "Recherchez une certification par son nom, certifInfo, rs code ou rncp code", "archived": " (archivée)"}, "partner": {"partner": "Organisme de formation", "placeholderPartnerDisabled": "Sélectionner une certification...", "placeholderPartner": "Recherchez un organisme de formation partenaire", "noPartner": "Aucun partenaire trouvé", "tooltip": "Recherchez un organisme de formation par son nom ou numéro de siret", "revoked": " (révoqué)"}, "skillsSet": {"title": "Bloc(s) de compétence(s)", "help": "Les blocs de compétences dépendent de la certification et du partenariat.", "placeholder": "Renseigner les blocs de compétences"}, "type": {"label": "Dossier à l'initiative de ", "help": "Permet d'indiquer l'initiative à laquelle le dossier de certification est créé", "types": {"certifie": "Certifié(e)", "of": "Organisme de formation", "poleEmploi": "<PERSON><PERSON>le Emploi", "employeur": "Employeur", "autre": "<PERSON><PERSON>"}}, "enrollmentDate": "Date d'inscription à la certification", "submit": "<PERSON><PERSON><PERSON> le dossier", "attendees": {"button": "<PERSON><PERSON><PERSON> le candidat", "searching": "Recherche de candidat avec :"}}}, "candidate": "Candidat", "title": "Dossiers de certification", "subtitle": "Liste des dossiers de certification pour tous organismes partenaires synchronisés et certifications confondus.", "subtitleCount": "{{folderCount}} dossiers", "no-data": "Aucun dossier de certification disponible. Avez-vous invité vos partenaires à la synchronisation de leurs dossiers ?", "certificationView": "Voir par certifications", "certifications": {"folderCount": "{{certificationInfo.folderCount}} dossiers", "title": "Dossiers de certification", "subtitle": "Liste les dossiers de certification par certification.", "folderView": "Voir par dossiers"}, "kanban": {"toolTipRevenue": "<PERSON><PERSON>re d'affaires HT (montant indicatif)", "toolTipExplication": "Chiffre d'affaires partiel - Pensez à indiquer le prix par dossier sur toutes vos certifications", "toolTipExplicationTrainingOrganism": "Chiffre d'affaires partiel ne prenant en compte que les dossiers de certification rattachés à vos certifications et non de vos partenariats en tant qu'organisme de formation - Pensez à indiquer le prix par dossier sur toutes vos certifications", "toolTipRevenueNoCA": "Renseigner le prix HT de vos certifications"}, "actions": {"declare": "{{isUpdatable}} <PERSON><PERSON><PERSON><PERSON><PERSON> comme ", "goBack": "{{isUpdatable}} <PERSON><PERSON><PERSON> à ", "update": "", "toRegister": "À Enregistrer", "registered": "Enregistré", "toTake": "<PERSON><PERSON><PERSON><PERSON> à passer", "toControl": "<PERSON> contrôler", "success": "<PERSON><PERSON><PERSON><PERSON>", "failed": "<PERSON><PERSON><PERSON>", "refused": "<PERSON><PERSON><PERSON><PERSON>", "toRetake": "À repasser", "aborted": "Abandonné", "inTrainingStarted": "Formation démarrée", "inTrainingEnded": "Formation terminée", "null": "Mettre à jour"}, "skillSets": {"warningUpdate": "Attention, tous les dossiers de certification dont le dossier de formation est associé à la formation seront modifiés et les blocs de compétences ne pourront plus être modifiés. Êtes-vous sûr(e) de vouloir continuer ?", "confirmUpdate": "Mettre à jour tous les dossiers de certification issus de la même formation"}}, "certifiers": {"form": {"labels": {"full-name": "Nom complet", "email": "<PERSON><PERSON><PERSON> email", "phoneNumber": "Téléphone"}, "placeholders": {"full-name": "Prénom et Nom de votre contact chez le certificateur", "email": "<PERSON>resse email de votre contact chez le certificateur", "phoneNumber": "Téléphone de votre contact chez le certificateur"}, "errors": {"email": "<PERSON><PERSON><PERSON> une adresse email valide"}}}, "partners": {"title": "Certifications & partenariats", "estimatedRegistrationFoldersCount": "Nombre de dossiers CPF estimés au ", "estimatedRegistrationFoldersCountToolTip": "Total des dossiers sortis de formation d'après les données de l'Open Data", "websites": "Sites internet", "skillSets": "Blocs de compétences", "addPartner": {"title": "Nouvelle demande de partenariat", "message": "Vous allez créer dans Wedof un partenariat à l'état \"Demande en traitement\" sur la certification \"{{externalId}} - {{name}}\" pour l'organisme de formation choisi.", "subtitle": "Vous pourrez ensuite soit activer ce partenariat sur France Compétences soit refuser cette demande.", "subtitleInternal": "Vous pourrez ensuite soit activer soit refuser cette demande.", "submit": "<PERSON><PERSON><PERSON> la demande de partenariat", "displayPartnerIfExists": "Aff<PERSON>r le partenariat.", "partner": {"placeholder": "Recherchez un organisme", "label": "Organisme de formation visé par le partenariat", "noOrganismFound": "Aucun organisme n'a été trouvée", "toolTip": "Si l'organisme recherché est introuvable, renseignez le numéro de siret pour l'ajouter"}}, "subtitle": "Partenariats avec les organismes de formation, classés par certification.", "certifications": {"partners": "{{certificationInfo.partnerCount}} partenariats", "no-data": "Vous n'avez aucune certification déclarée auprès de France Compétences, si cette information est incorrecte, contactez-nous."}, "auditsPendingCancellation": "La génération d'audits est en cours d'annulation", "allowAudits": "La génération d'audit est {{state}}", "enabled": {"true": "Active", "false": "Inactive"}, "searchbar": "Recherchez une certification par : nom, numéro certif info, code rome, code domaine, code RNCP ou code RS", "actions": {"csvExport": "Export en CSV"}, "table": {"partnership": {"title": "Partenariat de certification", "titleRequest": "<PERSON><PERSON><PERSON> partena<PERSON>", "partnerPlaceholder": "Votre organisme n'a pas de partenariat sur cette certification", "certifierPlaceholder": "Cet organisme n'a pas de partenariat sur cette certification", "stateLabel": "État", "auditComplianceLabel": "Conformité", "state": {"draft": "<PERSON><PERSON><PERSON> <PERSON> compléter", "processing": "Demande en traitement", "active": "Partenariat actif", "aborted": "<PERSON><PERSON><PERSON>", "refused": "<PERSON><PERSON><PERSON> refusée", "suspended": "Partenariat suspendu", "revoked": "Partenariat révoqué"}, "pendingActivation": "En cours d'activation", "pendingRevocation": "En cours de révocation", "pendingSuspension": "En cours de suspension", "pendingActivationSuffix": "(en cours d'activation)", "pendingRevocationSuffix": "(en cours de révocation)", "pendingSuspensionSuffix": "(en cours de suspension)"}, "complianceInProgress": "Au moins un audit en cours", "complianceLastUpdate": "Date du dernier changement d'état de conformité", "stateLastUpdate": "Date du dernier changement d'état", "certification": "Certification", "certifier": "<PERSON>ur", "certifiers": "Certificateur(s)", "organism": "Organisme", "certificationFolders": "Dossiers", "trainings": "Formations", "accessUpdated": "La synchronisation a bien été mise à jour", "access": "Partage des données", "connections": "Connexions", "partnerAccess": "Synchronisation avec le certificateur", "partnerAccessSubtitle": "Partage automatique de vos dossiers de certification avec le certificateur {{ name }}", "certifierAccess": "Partage des données avec le partenaire", "certifierAccessSubtitle": "Récupération automatique des dossiers de certification de votre partenaire {{ name }}", "no-access": "Vous n'avez pas de synchronisation avec ce partenaire", "no-data": "Vous n'avez aucun partenaire déclaré auprès de France Compétences pour la certification **[{{name}}]({{link}})**, si cette information est incorrecte [contactez-nous](mailto:<EMAIL>).", "comment": "Informations complémentaires", "partnerComment": "Complétez ou motivez votre demande", "partnerCommentPlaceholder": "Renseignez ici les informations que vous souhaitez partager avec le certificateur.", "commentTooltip": "Ces informations sont visibles par le certificateur et par le partenaire.", "dataRequiresCertifierAccess": "Un partage de données actif est requis pour obtenir cette donnée", "accessState": {"waiting": "En attente d'acceptation", "accepted": "Autorisé", "refused": "<PERSON><PERSON><PERSON><PERSON>", "terminated": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "none": "Aucun"}, "accessStateHelp": {"waiting": "Le partage des données est en attente d'acceptation du partenaire", "accepted": "Le partage des données est autorisé par le partenaire", "refused": "Le partage des données a été refusé par le partenaire", "terminated": "Le partage des données a été révoqué", "none": "Vous n'avez pas encore demandé le partage des données au partenaire"}, "connectionState": {"state": {"inactive": "Inactive", "active": "Active", "inProgress": "Activation en cours", "suspended": "Suspendu", "revoked": "Révoquée", "failed": "É<PERSON><PERSON>e", "refreshing": "Actualisation", "accountNotExist": "Compte inexistant"}, "nonActive": "Ayant un problème", "tableCard": {"name": "Connexion", "state": "État", "failedAt": "Depuis le {{date}}"}}, "connectionStateHelp": {"active": "La connexion du partenaire aux sources de données externes (EDOF, Kairos...) est active", "failed": "La connexion du partenaire aux sources de données externes (EDOF, Kairos...) est inactive, ouvrez le détail pour en savoir plus", "partial": "La connexion du partenaire aux sources de données externes est partielle, ouvrez le détail pour en savoir plus"}, "accessFailedTooltip": " car le partenaire n'a pas habilité Wedof à se connecter à EDOF.", "habilitation": {"train": "Habilitation pour former", "evaluate": "Habilitation pour organiser l'évaluation", "train_evaluate": "Habilitation pour former et organiser l'évaluation", "self": "<PERSON>ur", "label": "Rôle du partenaire"}, "actions": {"askAccess": "Demander la synchronisation", "askAccessAgain": "Demander à nouveau", "revoked": {"action": "Révoquer le partage", "messageKey": "Êtes-vous sûr(e) de vouloir révoquer le partage des données ?"}, "cancel": {"action": "Annuler la demande", "messageKey": "Êtes-vous sûr(e) de vouloir annuler la demande de partage des données ?"}, "refuse": {"action": "Refuser le partage des données", "messageKey": "Êtes-vous sûr(e) de vouloir refuser la demande de partage des données ?"}, "copyInviteLink": "copier le lien d'invitation", "copyInviteLinkSuccess": "Le lien d'invitation à la synchronisation pour l'organisme de formation partenaire a été copié dans le presse-papier."}, "filters": {"accessState": "Synchronisation automatique"}, "wrongInvitation": "Le lien d'invitation n'est pas destiné à l'organisme de siret {{siret}}."}, "form": {"title": "De<PERSON>er à l'organisme {{name}} de synchroniser automatiquement ses dossiers", "description": "Un email d'invitation va être envoyé à votre partenaire. Vous pouvez compléter ou corriger ci-dessous les données de votre contact chez ce partenaire :", "labels": {"full-name": "Nom complet du contact", "email": "<PERSON><PERSON><PERSON> email du contact"}, "placeholders": {"full-name": "Prénom et Nom de votre contact chez le partenaire", "email": "<PERSON>resse email de votre contact chez le partenaire"}, "errors": {"email": "<PERSON><PERSON><PERSON> une adresse email valide"}}, "trainingsZone": "Zone géographique des formations", "registration-folder": {"no-data": "Aucun dossier disponible concernant le partenaire de cette certification."}, "partnership": {"title": "<PERSON><PERSON><PERSON> ce partenariat", "messageKey": "Attention, vous êtes sur le point de devenir le certificateur en charge de la gestion de ce partenariat. Cette action est définitive. Souhaitez vous continuer ?"}}, "inactive": "Certification inactive", "surveys": {"title": "Suivi de l'insertion professionnelle", "subtitle": "Ces enquê<PERSON>, composées de trois questionnaires, remplies par les titulaires ont pour objectif d'analyser leurs situations professionnelles à moyen et long terme après l'obtention de leurs certifications en concordance avec les objectifs de la certification et devra être transmis à France Compétences.", "explanation": {"initial": "Questionnaire initial a pour objectif d'analyser la situation professionnelle des titulaires avant le passage et l'obtention de votre certification", "sixMonth": "Questionnaire à 6 mois a pour objectif d'examiner la situation professionnelle 6 mois des titulaires après l'obtention de la certification ", "longTerm": "Questionnaire à 1 an ou plus a pour objectif d'examiner la situation professionnelle des titulaires minimum 1 an après l'obtention de la certification "}, "exportDisabled": "Aucune enquête à exporter", "statistiques": {"created": "Nombre total d'enquêtes", "beforeCertificationSuccess": "Réponses au questionnaire avant le passage de la certification", "sixMonths": "Réponses au questionnaire 6 mois après l'obtention de la certification", "finished": "Réponses au questionnaire 1 an après l'obtention de la certification", "help": "{{totalCanAnswer}} questionnaires doivent être répondus par les titulaires"}}, "audit": {"noData": "Aucun audit", "feedbackPartner": "Vous avez des questions relatives aux résultats d'audits ou à votre partenariat ? Contactez votre certificateur.", "warningCreate": "Attention, la création d'un audit engendra la création d'un abonnement à 99 € HT / mois par certification.", "create": "Nouvel audit", "createFirst": "Lancer un audit", "createUnavailable": "Ajouter des critères à vos modèles d'audit afin de générer des audits", "createUnavailableNoTemplate": "Aucun modèle d'audit n'a été trouvé sur la certification {{certificationName}}", "header": "<PERSON>t du {{date}}", "criterias": "Voir les détails", "report": {"generate": "Générer le rapport", "edit": "Modifier le rapport", "open": "<PERSON><PERSON><PERSON><PERSON><PERSON> le rapport"}, "restart": "Relancer l'audit", "complete": "Clôturer l'audit", "completeAndSuspended": "Clôturer l'audit et suspendre le partenariat", "completeNoReport": "Attention, vous n'avez pas généré de rapport pour cet audit. Si vous le clôturez maintenant, vous ne pourrez plus générer le rapport.", "completeNoUpdatable": "Attention, une fois clôturé vous ne pourrez plus modifier le rapport.", "result": {"title": "Résultat", "none": "Non renseigné", "compliant": "Conforme", "nonCompliant": "Non conforme", "partiallyCompliant": "Partiellement conforme"}, "notes": "Notes", "startDate": "Date d'enregistrement des données", "updatePartnerCompliance": "Mettre à jour la conformité du partenaire d'après le résultat de cet audit", "endDate": "Date de clôture", "state": {"pendingComputation": "En préparation (collecte des données en cours)", "computing": "Analyse des données en cours", "inProgress": "En cours", "completed": "<PERSON><PERSON><PERSON><PERSON>", "failed": "<PERSON><PERSON><PERSON>"}, "criteriaCompliance": {"compliant": "Conforme", "nonCompliant": "Non conforme", "partiallyCompliant": "Partiellement conforme", "notApplicable": "Non applicable"}, "criteriaCountTitle": "Critères conformes", "confirmDeletion": "Êtes-vous sûr(e) de vouloir supprimer l'audit ?", "evaluatedCriterias": {"criteria": "Critère", "severity": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "requirement": "Exigence", "value": "Valeur enregistrée", "nonApplicable": "Non applicable", "advice": "Conseils de mise en conformité : {{advice}}"}, "tooltipInProgress": "{{pendingComputationOrComputing}} audit(s) en cours d'analyse"}}, "training-organism": {"candidates": {"button": "<PERSON><PERSON><PERSON> le candidat", "dialog": {"errors": {"firstName": "Le prénom du candidat est obligatoire", "lastName": "Le nom de famille du candidat est obligatoire", "email": "L'email du candidat doit être valide", "phoneNumber": "Le numéro de téléphone doit être valide", "phoneAlreadyExist": "Ce numéro de téléphone est déjà associé à un(e) candidat(e)", "emailAlreadyExist": "Cet email est déjà associé à un(e) candidat(e)", "useEmailOrPhone": "Utiliser {{contextEntity}} existant"}, "toolTip": "Le numéro de téléphone portable ou le numéro de téléphone fixe est requis afin de créer un nouveau candidat"}}, "attendees": {"title": "Apprenants", "button": "<PERSON><PERSON><PERSON> l'apprenant", "searching": "Recherche de l'apprenant avec :", "dialog": {"title": {"attendee": "Nouvel apprenant", "candidate": "Nouveau candidat", "update": "Modifier"}, "lastName": "Nom de famille", "firstName": "Prénom", "email": "Email", "phoneNumber": "Téléphone portable", "phoneFixed": "Téléphone fixe", "dateOfBirth": "Date de naissance", "nameCityOfBirth": "Ville de naissance", "nameCityOfBirthNotFound": "Aucune ville n'a été trouvée", "nameCityOfBirthSearch": "Recherchez une ville", "countryOfBirth": "Pays de naissance", "gender": {"gender": "Civilité", "female": "Madame", "male": "<PERSON>"}, "address": {"city": "Ville", "zipCode": "Code postal", "number": "Numéro de rue", "residence": "Bâtiment", "roadName": "Nom de rue", "repetitionIndexLabel": "Complément", "roadTypeLabel": "Type de rue"}, "degree": "Diplôme", "poleEmploiId": "Identifiant Pôle Emploi", "poleEmploiRegionCode": "Zone géo Pôle Emploi", "placeRequired": "Pour des raisons légales, le lieu de naissance est requis si le pays de naissance n'est pas la France", "birthName": "Nom de naissance (si différent du nom)", "errors": {"firstName": "Le prénom de l'apprenant est obligatoire", "lastName": "Le nom de famille de l'apprenant est obligatoire", "email": "L'email de l'apprenant doit être valide", "phoneNumber": "Le numéro de téléphone doit être valide", "phoneAlreadyExist": "Ce numéro de téléphone est déjà associé à un(e) apprenant(e)", "emailAlreadyExist": "Cet email est déjà associé à un(e) apprenant(e)", "phoneFixed": "Le numéro de téléphone fixe doit être valide"}, "toolTip": "Le numéro de téléphone portable ou le numéro de téléphone fixe est requis afin de créer un nouvel apprenant"}}, "certification-access": {"text": {"waiting": "Vous n'avez pas encore autorisé ou révoqué la synchronisation automatique dossiers de **{{certifier.name}}** pour la certification **{{certification.name}}**", "terminated": "Vous avez révoqué la synchronisation automatique des dossiers associés à la certification **{{certification.name}}** de **{{certifier.name}}** est en attente.", "refused": " <PERSON><PERSON> a<PERSON> refusé synchronisation automatique des dossiers associés à la certification **{{certification.name}}** de **{{certifier.name}}**.", "accepted": "**{{certifier.name}}** est autorisé à recevoir les informations associées vos dossiers pour la certification <a alt='lien vers fiche de la certification' href='https://reseau.intercariforef.org/formations/certification-{{certification.certifInfo}}.html'>**{{certification.name}}**</a>."}, "status": {"waiting": "La synchronisation de vos dossiers est en attente d'autorisation.", "terminated": "Vous avez révoqué la synchronisation de vos dossiers", "refused": "<PERSON><PERSON> avez refusé la synchronisation de vos dossiers.", "accepted": "Vous avez accepté la synchronisation de vos dossiers."}}, "folders": {"title": "Dossiers de formation", "subtitle": "Liste des dossiers de formations en cours pour votre organisme, toutes certifications confondues.", "subtitleCount": "{{folderCount}} dossiers", "sessionView": "Par session", "kanbanView": "Ka<PERSON><PERSON>", "revenue": {"total": "Total : "}, "toolTip": {"revenue": "<PERSON><PERSON>re d'affaires HT (montant indicatif)", "kanbanView": "Afficher les dossiers sous forme de kanban", "tableView": "Afficher les dossiers sous forme de tableau"}, "searchbar": "Recherchez un dossier par nom, prénom ou numéro de dossier", "createFolder": {"create": "Nouveau dossier", "toolTip": {"notAvailable": "Vous avez atteint la limite de dossiers autorisés dans le cadre de votre abonnement gratuit", "available": "<PERSON><PERSON><PERSON> un dossier hors CPF"}, "title": "Nouveau dossier de formation", "form": {"attendee": {"attendee": "Apprenant", "placeholderAttendee": "Rechercher un apprenant par email ou numéro de téléphone portable", "noAttendee": "<PERSON><PERSON><PERSON>nt trouvé", "label": "Recherchez un apprenant par son email ou numéro de téléphone"}, "type": {"type": "Type de dossier *", "polemploi": "<PERSON><PERSON><PERSON> (Autres)", "individual": "Autofinancement", "company": "Entreprise", "opco": "OPCO"}, "poleEmploi": {"poleEmploiDevis": "Numéro du devis Pôle Emploi", "poleEmploiId": "Identifiant Pôle Emploi", "poleEmploiRegionCode": "Code région Pôle Emploi"}, "session": {"session": "Session de formation", "placeholderSession": "Rechercher une session", "noSession": "Aucune Session trouvée", "label": "Recherchez une session de votre organisme", "sessionResult": "Du {{startDate}} au {{endDate}} - {{city}} - {{trainingTitle}}"}, "tarif": {"ttc": {"tarif": "Tarif TTC", "placeholderTarif": "<PERSON><PERSON>f T<PERSON> pour ce dossier"}, "ht": {"tarif": "<PERSON><PERSON>f <PERSON>", "placeholderTarif": "<PERSON><PERSON>f HT pour ce dossier"}, "tooltip": {"ttc": "Le tarif TTC est par défaut celui de l'action de formation. Vous pouvez modifier ce prix manuellement", "ht": "Le tarif HT dépend du taux de TVA associé à votre organisme et du prix TTC"}, "noTva": "En cliquant ici, vous pouvez personnaliser votre taux de TVA afin d'obtenir le tarif HT."}, "partnership": {"placeholder": "Recherchez une entreprise", "label": "En partenariat avec", "noOrganismFound": "Aucune entreprise n'a été trouvée", "toolTip": "Si l'entreprise recherchée est introuvable, renseignez le numéro de siret pour l'ajouter"}, "errors": {"attendee": "Veuillez rentrer un email ou un numéro de téléphone pour l'apprenant", "type": "Veuillez indiquer le type du dossier", "session": "<PERSON>euillez indiquer une session"}, "save": "<PERSON><PERSON><PERSON> le dossier"}}, "common": {"externalId": "N° dossier ", "type": {"individual": "Autofinancement", "poleEmploi": "<PERSON><PERSON><PERSON> (Autres)", "cpf": "CPF", "company": "Entreprise", "opco": "OPCO (Manuel)", "opcoCfa": "OPCO (Apprentissage)", "kairosAif": "<PERSON><PERSON> (AIF)"}, "billingState": {"notBillable": "Pas facturable", "depositWait": "Acompte en attente de versement", "depositPaid": "Acompte versé", "toBill": "À facturer", "billed": "<PERSON><PERSON><PERSON><PERSON>", "paid": "<PERSON><PERSON>"}, "amount": "Montant HT", "title": "Formation", "funding": "Financement", "partnership": "En partenariat avec {{partner}}", "startDate": "Début de session", "endDate": "Fin de session", "duration": "Durée totale", "hoursInCenter": "En centre", "hoursInCompany": "En entreprise", "sessionDate": "Dates de la session", "noSessionDate": "Dates de session à définir", "date": "Du {{startDate}} au {{endDate}} ", "dateWithCompletionRate": "Du {{startDate}} au {{endDate}} ({{completionRate}}% réalisé / {{expectedCompletionRate}}% attendu)", "dateWithCompletionRateWithNoExpectedCompletionRate": "Du {{startDate}} au {{endDate}} ({{completionRate}}% réalisé)", "completionRateDone": "{{completionRate}}% réalisé {{expectedCompletionRate}}", "completionRateDoneDate": "{{completionRate}}% réalisé au {{date}} {{expectedCompletionRate}}", "completionRate": "Taux de réalisation", "city": "Lieu de formation", "teachingModality": {"presentiel": "Présentiel", "mixte": "Mixte présentiel et à distance", "distanciel": "À distance"}, "updatePrice": "Modifier le prix", "notes": {"label": "Notes privées", "placeholder": "Informations complémentaires"}, "state": {"notProcessed": "<PERSON> traiter", "validated": "<PERSON><PERSON><PERSON>", "waitingAcceptation": "Validé (En cours d'instruction Pôle emploi)", "accepted": "Accepté", "inTraining": "En formation", "terminated": "Sortie de formation", "serviceDoneDeclared": "Service fait déclaré", "serviceDoneValidated": "Service fait validé", "canceledByAttendee": "Annulé (par le titulaire)", "canceledByAttendeeNotRealized": "Annulation titulaire (non réalisé)", "canceledByOrganism": "Annulé (par l'organisme)", "canceledByFinancer": "Annulé (par le financeur)", "refusedByAttendee": "<PERSON><PERSON><PERSON> titulaire", "refusedByOrganism": "Refusé (par l'organisme)", "refusedByFinancer": "Refusé (par le financeur)", "rejectedWithoutTitulaireSuite": "Annulé sans suite", "rejectedWithoutCdcSuite": "Annulé sans suite", "rejectedWithoutOfSuite": "Annulé sans suite", "rejected": "Annulé sans suite", "toBill": "À Facturer", "billed": "<PERSON><PERSON><PERSON><PERSON>", "depositPaid": "Acompte versé", "paid": "<PERSON><PERSON>", "state": "État"}, "links": {"folder": "Consulter le dossier sur l'Espace Des Organismes de Formation (EDOF)", "training_action_public": "Consulter les détails de l'action de formation"}}, "no-data": "Aucun dossier disponible pour votre organisme", "table": {"actions": "Actions", "firstName": "Prénom", "lastName": "Nom", "fullName": "NOM Prénom", "attendee": "Apprenant", "state": "État", "certificationState": "État", "trainingName": "Formation", "sessionStartDate": "D<PERSON>but", "sessionEndDate": "Fin", "email": "Email", "price": "Montant HT", "funding": "Financement HT", "organism": "Organisme", "certification": "Certification", "tags": "Tags"}, "funding": {"cpf": "CPF", "poleEmploi": "<PERSON><PERSON><PERSON> (Autres)", "individual": "Autofinancement", "company": "Entreprise", "opco": "OPCO", "opcoCfa": "OPCO (Apprentissage)", "kairosAif": "<PERSON><PERSON> (AIF)"}, "actions": {"cancel": "{{isUpdatable}} Annuler le dossier", "cancelAttendeeDidNotCome": "{{isUpdatable}} Non présentation de l'apprenant", "canceledByAttendeeNotRealized": "{{isUpdatable}} Annulation titulaire (non réalisé)", "cancelByOrganism": "{{isUpdatable}} Annuler par l'organisme", "inTraining": "{{isUpdatable}} Déclarer l'entrée en formation", "refuse": "{{isUpdatable}} Refuser le dossier", "waitingAcceptation": "{{isUpdatable}} En attente d'acceptation", "refusedByAttendee": "{{isUpdatable}} Refus titulaire", "rejectedWithoutTitulaire": "{{isUpdatable}} Annuler sans suite", "serviceDone": "{{isUpdatable}} Déclarer le service fait", "terminate": "{{isUpdatable}} Déclarer la sortie de formation", "update": "{{isUpdatable}} Modifier le dossier", "validate": "{{isUpdatable}} Valider le dossier", "accept": "{{isUpdatable}} Accepter le dossier", "toBill": "{{isUpdatable}} Marquer comme facturé"}, "actions2": {"canceled": "{{isUpdatable}} Annuler le dossier", "canceledAttendeeDidNotCome": "{{isUpdatable}} Non présentation de l'apprenant", "canceledByOrganism": "{{isUpdatable}} Annuler par l'organisme", "canceledByAttendeeNotRealized": "{{isUpdatable}} Annulation titulaire (non réalisé)", "refusedByOrganism": "{{isUpdatable}} Refuser le dossier", "inTraining": "{{isUpdatable}} Déclarer l'entrée en formation", "refused": "{{isUpdatable}} Refuser le dossier", "waitingAcceptation": "{{isUpdatable}} En attente d'acceptation", "refusedByAttendee": "{{isUpdatable}} Refus titulaire", "rejectedWithoutTitulaireSuite": "{{isUpdatable}} Annuler sans suite", "serviceDoneDeclared": "{{isUpdatable}} Déclarer le service fait", "terminated": "{{isUpdatable}} Déclarer la sortie de formation", "updated": "{{isUpdatable}} Modifier le dossier", "validated": "{{isUpdatable}} Valider le dossier", "accepted": "{{isUpdatable}} Accepter le dossier", "serviceDoneValidated": "{{isUpdatable}} Marquer comme facturé", "allBilling": "{{isUpdatable}} Marquer comme facturé", "paid": "{{isUpdatable}} Marquer comme payé", "null": "Mettre à jour"}, "dialog": {"title": {"cancel": "<PERSON><PERSON>r le dossier", "canceledByAttendeeNotRealized": "Annulation titulaire (non réalisé)", "inTraining": "Déclarer l'entrée en formation", "refuse": "Refuser le dossier", "refusedByAttendee": "Refuser par le titulaire", "waitingAcceptation": "Dé<PERSON>larer en attente d'acceptation", "rejectedWithoutTitulaire": "<PERSON><PERSON><PERSON><PERSON><PERSON> comme annulé sans suite", "serviceDone": "Dé<PERSON>larer le service fait pour le dossier", "terminate": "Déclarer la sortie de formation", "update": "Modifier le dossier", "validate": "Valider le dossier", "accept": "Accepter le dossier", "toBill": "Marquer comme facturé", "paid": "Marquer comme payé", "examToRegister": "Demander l'enregistrement de l'apprenant pour l'examen de certification", "examRegistered": "Enregistrer l'apprenant pour l'examen de certification", "examToTake": "L'apprenant est prêt pour le passage de la certification", "examToRetake": "L'apprenant est prêt à repasser la certification", "examRefused": "<PERSON><PERSON><PERSON> de la demande de passage de l'examen", "examToControl": "Le résultat de l'examen est à contrôler", "examSuccess": "L'apprenant a réussi l'examen de certification", "examFailed": "L'apprenant a raté l'examen de certification", "examAborted": "L'apprenant abandonne l'examen de certification", "plural": {"cancel": "Annuler les dossiers", "canceledByAttendeeNotRealized": "Annulation des titulaires (non réalisé)", "inTraining": "Déclarer l'entrée en formation des dossiers", "refuse": "Refuser les dossiers", "refusedByAttendee": "Refus par les titulaires", "rejectedWithoutTitulaire": "<PERSON><PERSON><PERSON><PERSON><PERSON> comme annulé sans suite", "waitingAcceptation": "Dé<PERSON>larer en attente d'acceptation", "serviceDone": "Dé<PERSON>larer le service fait pour les dossiers", "terminate": "Déclarer la sortie de formation des dossiers", "update": "Modifier les dossiers", "validate": "Valider les dossiers", "accept": "Accepter les dossiers", "toBill": "Facturer les dossiers", "paid": "Marquer comme payé", "examToRegister": "Demander l'enregistrement des apprenants pour l'examen de certification", "examRegistered": "Enregistrer les apprenants pour l'examen de certification", "examToTake": "Les apprenants sont prêts pour le passage de la certification", "examToRetake": "Les apprenants sont prêts à repasser la certification", "examSuccess": "Les apprenants ont réussis l'examen de certification", "examFailed": "Les apprenants ont ratés l'examen de certification", "examAborted": "Les apprenants abandonnent l'examen de certification", "examRefused": "<PERSON><PERSON><PERSON> des demandes de passage de l'examen", "examToControl": "Les résultats des examens sont à contrôler"}}, "content": {"warning": "Attention cette opération est irréversible !", "validate": "Êtes-vous sûr(e) de valider le dossier ?", "toBill": "Aucune facture n'est générée directement gérée par Wedof. Il vous faut utiliser votre outil de facturation habituelle et indiquer le numéro de facture que celui-ci aura générée.<br/><br/><p>Si vous souhaitez intégrer votre outil de facturation avec Wedof <a href='mailto:<EMAIL>' target='_blank'>contactez-nous</a></p>", "canceledByAttendeeNotRealized": "Êtes-vous sûr(e) de déclarer le dossier comme annulé par le titulaire (non réalisé) ?", "accept": "Êtes-vous sûr(e) d'accepter le dossier ?", "refusedByAttendee": "Êtes-vous sûr(e) de déclarer le dossier comme refusé par le titulaire ?", "waitingAcceptation": "Êtes-vous sûr(e) de déclarer le dossier comme en attente d'acceptation ?", "rejectedWithoutTitulaire": "Êtes-vous sûr(e) de déclarer le dossier comme annulé sans suite ?", "examToRegister": "Êtes-vous sûr(e) de demander l'enregistrement de l'apprenant pour l'examen de certification ?", "examRegistered": "Êtes-vous sûr(e) d'enregistrer l'apprenant pour l'examen de certification ?", "examToTake": "Êtes-vous sûr(e) de déclarer que l'apprenant est prêt pour le passage de la certification ?", "examToRetake": "Êtes-vous sûr(e) de déclarer que l'apprenant est prêt à repasser la certification ?", "examToControl": "Êtes-vous sûr(e) de déclarer que l'examen est à contrôler ?", "examRefused": "Êtes-vous sûr(e) de refuser de la demande de passage de l'examen ?", "examSuccess": "Êtes-vous sûr(e) de déclarer que l'apprenant a réussi l'examen de certification ?", "examFailed": "Êtes-vous sûr(e) de déclarer que l'apprenant a échoué à l'examen de certification ?", "examAborted": "Êtes-vous sûr(e) de déclarer que l'apprenant abandonne l'examen ?", "canRetake": "Souhaitez-vous autoriser l'apprenant à repasser la certification ?", "paid": "Êtes-vous sûr(e) de marquer comme payé le dossier ?", "update": "Êtes-vous sûr(e) de modifier le dossier ?", "plural": {"validate": "Êtes-vous sûr(e) de valider les dossiers ?", "accept": "Êtes-vous sûr(e) d'accepter les dossiers ?", "canceledByAttendeeNotRealized": "Êtes-vous sûr(e) de déclarer les dossiers comme annulé par les titulaires (non réalisé) ?", "waitingAcceptation": "Êtes-vous sûr(e) de déclarer les dossiers comme en attente d'acceptation ?", "refusedByAttendee": "Êtes-vous sûr(e) de déclarer les dossiers comme refus par les titulaires ?", "rejectedWithoutTitulaire": "Êtes-vous sûr(e) de déclarer les dossiers comme annulé sans suite ?", "examToRegister": "Êtes-vous sûr(e) de demander l'enregistrement des apprenants pour l'examen de certification ?", "examRegistered": "Êtes-vous sûr(e) d'enregistrer l'apprenant pour l'examen de certification ?", "examToTake": "Êtes-vous sûr(e) de déclarer que les apprenants sont prêts pour le passage de la certification ?", "examToRetake": "Êtes-vous sûr(e) de déclarer que les apprenants sont prêts à repasser la certification ?", "examSuccess": "Êtes-vous sûr(e) de déclarer que les apprenants ont réussi l'examen de certification ?", "examFailed": "Êtes-vous sûr(e) de déclarer que les apprenants ont échoué à l'examen de certification ?", "examAborted": "Êtes-vous sûr(e) de déclarer que les apprenants abandonnent l'examen de certification ?", "examRefused": "Êtes-vous sûr(e) de refuser de les demandes de passage de l'examen ?", "examToControl": "Êtes-vous sûr(e) de déclarer que les examens sont à contrôler ?", "paid": "Êtes-vous sûr(e) de marquer comme payé les dossiers ?", "update": "Tous les dossiers pour lesquels la nouvelle valeur renseignée est différente de la valeur actuelle seront modifiés. Êtes-vous sûr(e) de modifier les dossiers ?"}}, "labels": {"absenceDuration": "<PERSON><PERSON><PERSON> d'absence (heures)", "code": "<PERSON>son", "date": "Date", "priceChange": "Modifier le prix", "description": "Message public (visible pour l'apprenant)", "endDate": "Date de fin", "forceMajeureAbsence": "Absence pour force majeure", "startDate": "Date de début", "billNumber": "Numéro de facture", "indicativeDuration": "Durée de la formation", "attendeeDidNotCome": "Confirmez que l'apprennant ne s'est pas présenté à la formation."}, "placeholders": {"absenceDuration": "<PERSON><PERSON><PERSON> d'absence (heures)", "code": "<PERSON>son", "date": "Date", "description": "Description...", "priceChange": "Modifier le prix", "priceChangeNote": "Message public", "endDate": "Date de fin", "startDate": "Date de début", "billNumber": "BIL-001", "indicativeDuration": "Nombre d'heures de la formation"}, "suffix": {"percent": "%", "price": "= {{value}} €", "indicativeDuration": "= {{value}} heures"}, "errors": {"minDate": "La date doit être postérieure ou égale au début de la formation", "maxDate": "La date doit être inférieure ou égale à la fin de la formation", "indicativeDuration": "Vérifier la cohérence des durées de formation", "hoursInCenter": "La durée de formation en centre ne peut être supérieure à la durée totale de formation", "hoursInCompany": "La durée de formation en entreprise ne peut être supérieure à la durée totale de formation"}}}, "certifications": {"title": "Certifications & partenariats", "subtitle": "Liste des certifications et de vos partenariats avec les certificateurs", "trainingsActives": {"title": "Suivi des formations publiées", "onsite": "Présentiel", "online": "Distanciel", "compliance": "Conformité", "disclaimer": "Attention, ce lien peut également afficher d'autres actions de formation de l'organisme sur cette certification si elles ont un titre similaire", "complianceType": {"notVerified": "La formation n'a pas été vérifiée", "compliant": "La formation est en adéquation avec la certification", "notCompliant": "La formation n'est pas en adéquation avec la certification", "lastUpdate": "<PERSON><PERSON><PERSON> mise à jour le {{date}}", "menu": {"notVerified": "À vérifier", "compliant": "Conforme", "notCompliant": "Non conforme"}}}, "statistiques": {"title": "Statistiques", "titleCertification": "Statistiques globales de la certification", "foldersTitle": "Examens de certification", "ratingTitle": "Satisfaction des apprenants", "trainingsTitle": "Formations préparant à la certification", "foldersByStateTitle": "Dossiers de certification", "abortTitle": "Abandon", "foldersTotal": "Dossiers de certification", "exportPartner": "Exporter les statistiques de vos partenaires (csv)", "takeRate": "Taux de passage global", "takeRateHelp": "Pour cette certification, le nombre de candidats qui ont passé au moins une fois l'examen sur le nombre total des candidats qui devaient le passer", "takeRateHelpPartner": "Pour cette certification et ce partenaire, le nombre de candidats qui ont passé au moins une fois l'examen sur le nombre total des candidats qui devaient le passer", "takeRate1Month": "Taux de passage après au moins 1 mois", "takeRateHelp1Month": "Taux qui prend en compte la tolérance d'un délai d'1 mois entre le passage à Prêt à passer et le passage effectif de l'examen", "takeRate3Months": "Taux de passage après au moins 3 mois", "takeRateHelp3Months": "Taux qui prend en compte la tolérance d'un délai de 3 mois entre le passage à Prêt à passer et le passage effectif de l'examen", "takeRate6Months": "Taux de passage après au moins 6 mois", "takeRateHelp6Months": "Taux qui prend en compte la tolérance d'un délai de 6 mois entre le passage à Prêt à passer et le passage effectif de l'examen", "takeRate1Year": "Taux de passage après au moins 1 an de tolérance", "takeRateHelp1Year": "Taux qui prend en compte la tolérance d'un délai d'un an entre le passage à Prêt à passer et le passage effectif de l'examen", "takeRateStrict1Month": "Taux de passage après au moins 1 mois (strict)", "takeRateStrictHelp1Months": "Taux de passage strict des dossiers de plus de 1 mois à l'état Prêt à passer et les états avec un passage effectif pour la même période", "takeRateStrict3Months": "Taux de passage après au moins 3 mois (strict)", "takeRateStrictHelp3Months": "Taux de passage strict des dossiers de plus de 3 mois à l'état Prêt à passer et les états avec un passage effectif pour la même période", "takeRateStrict6Months": "Taux de passage après au moins 6 mois (strict)", "takeRateStrictHelp6Months": "Taux de passage strict des dossiers de plus de 6 mois à l'état Prêt à passer et les états avec un passage effectif pour la même période", "successRate": "<PERSON><PERSON>", "successRateHelp": "Pour cette certification, le nombre de candidats qui ont réussi l'examen sur le nombre total de ceux qui l'ont passé", "successRateHelpPartner": "Pour cette certification et ce partenaire, le nombre de candidats qui ont réussi l'examen sur le nombre total de ceux qui l'ont passé", "toRegisterCount": "À enregistrer", "refusedCount": "<PERSON><PERSON><PERSON><PERSON>", "registeredCount": "Enregistré", "toTakeCount": "<PERSON><PERSON><PERSON><PERSON> à passer", "toControlCount": "<PERSON> contrôler", "toRetakeCount": "À repasser", "failedCount": "<PERSON><PERSON><PERSON>", "abortedCount": "Abandonné", "successCount": "<PERSON><PERSON><PERSON><PERSON>", "abortRate": "Taux d'abandon global", "abortRateHelp": "Pour cette certification, le nombre de dossiers de certification à l'état 'Abandonné' sur le nombre total de dossiers de certification", "abortRateHelpPartner": "Pour cette certification et ce partenaire, le nombre de dossiers de certification à l'état 'Abandonné' sur le nombre total de dossiers de certification", "abortRateBeforeTraining": "Taux d'abandon avant la formation", "abortRateInTraining": "Taux d'abandon pendant la formation", "abortRateAfterTraining": "Taux d'abandon après la formation", "averageRating": "Évaluation moyenne", "averageRatingHelp": "Pour cette certification et ce partenaire, la moyenne des évaluations données sur EDOF (notée sur 5)", "reviewRate": "Taux d'évaluation", "reviewRateHelp": "Pour cette certification et ce partenaire, le nombre d'évaluations données sur EDOF sur le nombre total des dossiers sortis de formation", "reviewCount": "Nombre d'évaluations", "reviewCountHelp": "Pour cette certification et ce partenaire, le nombre d'évaluations données sur EDOF", "minPricing": "Prix minimum", "minPricingHelp": "Pour cette certification et ce partenaire, le prix le plus bas parmi les actions de formations du catalogue EDOF", "maxPricing": "Prix maximum", "maxPricingHelp": "Pour cette certification et ce partenaire, le prix le plus haut parmi les actions de formations du catalogue EDOF", "averagePricing": "Prix moyen", "averagePricingHelp": "Pour cette certification et ce partenaire, le prix moyen des actions de formations du catalogue EDOF", "minDuration": "Durée minimum", "minDurationHelp": "Pour cette certification et ce partenaire, la durée la plus basse parmi les actions de formations du catalogue EDOF", "maxDuration": "Durée maximum", "maxDurationHelp": "Pour cette certification et ce partenaire, la durée la plus haute parmi les actions de formations du catalogue EDOF", "averageDuration": "<PERSON><PERSON><PERSON> moyenne", "averageDurationHelp": "Pour cette certification et ce partenaire, la durée moyenne des actions de formations du catalogue EDOF", "summary": {"good": "Pas de problème majeur identifié sur les statistiques", "issue": "Les statistiques suivantes sont à contrôler : ", "unavailable": "Les statistiques importantes ne sont pas encore disponibles"}, "noDataRegistrationFolder": "Sans financement connu"}, "actions": {"manage": "<PERSON><PERSON>rer la synchronisation", "terminate": "Révoquer", "authorize": "Autoriser", "authorizeAgain": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "modal": {"title": {"authorize": "Autoriser la synchronisation de vos dossiers", "terminate": "Révoquer la synchronisation de vos dossiers"}}}}, "sessions": {"title": "Dossiers de formation", "subtitle": "Liste des sessions de formations en cours pour votre organisme, toutes certifications confondues.", "folderView": "Voir par dossiers", "no-data": "Aucune session n'est disponible pour votre organisme.", "label": "{{sessionInfo.trainingTitle}}", "labelNoDate": "Dates à définir", "dates": "{{startDate}} au {{endDate}}", "attendees": "{{sessionInfo.activeAttendeeCount}} apprenants", "filters": {"startDate": "D<PERSON>but", "endDate": "Fin"}, "searchbar": "Recherchez par identifiant de session ou intitulé de formation", "search": "Rechercher la session", "registration-folder": {"no-data": "Aucun dossier en cours pour cette session."}}, "stats": {"title": "Statistiques", "period": {"label": "Période", "start": "D<PERSON>but", "end": "Fin"}, "select": {"label": "Filtrer par formation", "default": "Tout le catalogue", "noEntrie": "Aucune formation trouvée", "placeholder": "Filtrer sur une formation"}}, "proposals": {"title": "Propositions", "titleKanban": "Propositions <PERSON><PERSON><PERSON><PERSON><PERSON>", "kanbantva": "<PERSON><PERSON><PERSON> <PERSON> renseigner le taux de TVA de votre organisme", "subtitleCount": "{{proposalCount}} propositions", "subtitle": "Liste des propositions de réduction attribuées à des personnes ou à des organismes.", "subtitleKanban": "Liste des propositions individuelles de réduction attribuées à des personnes.", "button": "C<PERSON>er une proposition", "noData": "Aucune proposition n'a été créée par votre organisme", "confirmDeletion": "Êtes-vous sûr(e) de vouloir supprimer la proposition {{code}} ?", "confirmDeletionMultiple": "Êtes-vous sûr(e) de vouloir supprimer les propositions sélectionnées ?", "proposalCard": {"stateLastUpdate": "Date du dernier changement d'état", "generic": "Proposition <PERSON><PERSON><PERSON><PERSON><PERSON>", "individual": "Proposition Individuelle", "code": "Code", "codeRequired": {"label": "Nécessite l'utilisation d'un code", "value": "Aucun code requis"}, "limitUsage": "Limite d'utilisation", "usedCount": "Nombre d'utilisation actuel", "expireDate": "Date d'expiration", "link": {"link": {"title": "<PERSON><PERSON>", "placeholder": "Voir le Lien Apprenant"}, "linkCommercial": {"title": "Lien Commercial", "placeholder": "Voir le Lien Commercial"}}, "autoValidate": "Valider automatiquement", "sessionDates": "Du {{sessionStartDate}} au {{sessionEndDate}} ", "indicativeDuration": "Durée de formation (h)", "phoneNumber": "Téléphone"}, "table": {"proposal": {"title": "Proposition", "expire": "Expire le ", "generic": "Générique", "stateLastUpdate": "Date du dernier changement d'état"}, "tags": {"title": "Tags"}, "conditions": {"title": "Conditions", "none": "Le prix de base est appliqué ", "fixed": "Le prix est fixé à {{amount}} € TTC ", "percentPlus": "Une augmentation de {{amount}} % est appliquée par rapport au prix de base ", "percentMinus": "Une réduction de {{amount}} % est appliquée par rapport au prix de base ", "amountPlus": "Une augmentation de {{amount}} € est appliquée par rapport au prix de base ", "amountMinus": "Une réduction de {{amount}} € est appliquée par rapport au prix de base "}, "discountType": {"none": "Aucun", "percent": "Pourcentage", "fixed": "Fixe", "amount": "<PERSON><PERSON>"}, "trainingActions": {"title": "Actions de formation", "noTrainingActions": "Valable pour toutes actions de formation"}, "states": {"title": "État", "template": "Générique", "active": "Active", "ended": "Terminée", "expired": "Expirée", "draft": "Brouillon", "viewed": "<PERSON><PERSON>", "accepted": "Acceptée", "refused": "<PERSON><PERSON><PERSON><PERSON>", "cancelled": "<PERSON><PERSON><PERSON>"}, "validation": {"auto": "Validation automatique", "manuel": "Validation manuelle"}, "sessionDates": {"title": "Date de session", "date-from-to": "avec une session du ", "date-from": "avec un début de session le", "no-date": "avec aucune dates de session choisies"}, "actions": {"copy": "Copier le lien apprenant", "active": "{{update}} Activer", "draft": "{{update}} Mettre en brouillon", "refused": "{{update}} <PERSON><PERSON><PERSON><PERSON><PERSON> comme refusée"}}, "form": {"subtitle": "C<PERSON>er une proposition", "fields": {"firstName": {"label": "Prénom", "placeholder": "Prénom", "error": "Veuillez renseigner un prénom"}, "lastName": {"label": "Nom", "placeholder": "Nom", "error": "Veuillez renseigner un nom"}, "email": {"label": "Email", "placeholder": "Email", "error": "Veuillez renseigner un email", "emailTooltip": "Indiquez une adresse mail valide"}, "phone": {"label": "Téléphone", "placeholder": "Téléphone", "error": "Veuillez renseigner un numéro de téléphone", "phoneNumberTooltip": "Indiquez un numéro de téléphone valide"}, "discountType": {"label": "Gestion du prix", "placeholder": "Aucune modification", "options": {"none": {"radio": "Aucun", "label": "Aucun", "value": "none", "description": ""}, "percent": {"radio": "Variation en %", "label": "Modification du prix en %", "placeholder": "In<PERSON><PERSON> le pourcentage d'augmentation / réduction", "value": "percent", "increase": "Augmentation de ", "decrease": "Réduction de ", "unit": " %", "error": "Veuillez indiquer le pourcentage", "description": ""}, "fixed": {"radio": "Modifier le montant", "label": "<PERSON><PERSON>", "placeholder": "<PERSON><PERSON><PERSON> un nouveau montant", "unit": " € TTC", "value": "fixed", "error": "Veuillez indiquer un nouveau montant pour la formation", "description": ""}, "amount": {"radio": "Variation en €", "label": "Modification du prix en €", "placeholder": "<PERSON><PERSON><PERSON> la réduction ou l'augmentation", "value": "Amount", "unit": " €", "decrease": "Réduction de ", "increase": "Augmentation de ", "error": "Veuillez indiquer un montant", "description": ""}}, "percent": {"increase": "Augmentation du prix en %", "decrease": "Réduction du prix en %"}, "amount": {"increase": "Augmentation du prix en €", "decrease": "Réduction du prix en €"}, "fixed": "Nouveaux prix", "errors": {"increase-max": "l'augmentation ne peut pas dépasser {{value}}", "increase-min": "L'augmentation ne peut pas être inférieure à {{value}}", "decrease-max": "La réduction ne peut pas dépasser {{value}}", "decrease-min": "La réduction ne peut pas être inférieure à {{value}}", "fixed-max": "Le montant ne peut pas dépasser {{value}}", "fixed-min": "Le montant ne peut pas être inférieur à {{value}}"}}, "amount": {"label": "<PERSON><PERSON>", "placeholder": "<PERSON><PERSON>", "error": "Veuillez renseigner un montant", "labelDisabled": "Montant de l'action de formation choisie"}, "code": {"label": "Code de la proposition", "placeholder": "Généré aléatoirement", "help": "Vide = Généré aléatoirement", "error": "Veuillez renseigner un code de 5 à 15 caractères (a-zA-Z0-9_)"}, "codeRequired": {"label": "Nécessite l'utilisation d'un code ?"}, "expire": {"label": "Date d'expiration", "help": "Vide = N'expire pas", "placeholder": "N'expire pas", "error": "La date d'expiration doit être dans le futur"}, "limitUsage": {"label": "Limite d'utilisation(s)", "help": "Vide = illimité", "placeholder": "Illimité", "error": "Veuillez renseigner un nombre limite d'utilisation"}, "description": {"label": "Message public", "placeholder": "Message à destination du futur apprenant"}, "notes": {"label": "Notes privées", "placeholder": "Informations complémentaires"}, "trainingActions": {"label": "Actions de formation", "placeholder": "Valable pour toutes les actions de votre organisme", "searching": "Recherche de l'action de formation en cours...", "noTrainingAction": "Aucune action de formation correspondant à cette recherche", "trainingAction": "Action de formation"}, "sessionStartDate": {"label": "Date début de session", "placeholder": "<PERSON>", "error": "Veuillez indiquer une date de début si une date de fin est indiquée"}, "sessionEndDate": {"label": "Date fin de session", "placeholder": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "completionRate": {"label": "Taux de réalisation (%)"}, "indicativeDuration": {"label": "Durée de formation (h)", "placeholder": "Ex: 14"}, "type": {"label": "Type de proposition", "generic": "Générique", "single": "Individuelle"}, "sales": {"label": "Agent commercial", "placeholder": "Selectionner un commercial", "toolTip": "Agent commercial en charge de la gestion à la proposition", "noUserFound": "Aucun commercial trouvé"}}, "button": {"text": "<PERSON><PERSON><PERSON> la <PERSON>"}}, "searchbar": "Rechercher une proposition par prénom, nom, email, identifiant d'action de formation, nom de formation, notes privées, code, tags et message public"}, "common": {"discountType": {"title": "Modifier le prix", "none": {"radio": "Aucun"}, "percent": {"radio": "Variation en %", "label": "Variation en %", "placeholder": "<PERSON><PERSON><PERSON> le pourcentage (positif ou négatif)", "unit": " %"}, "fixed": {"radio": "Nouveau prix", "label": "Nouveau prix", "placeholder": "<PERSON><PERSON><PERSON> le nouveau prix", "unit": " TTC"}, "amount": {"radio": "Variation en €", "label": "Variation en €", "placeholder": "<PERSON><PERSON><PERSON> la variation (positive ou négative)", "unit": " €"}, "errors": {"min": "Le prix ne peut être inférieur à {{value}}€", "max": "Le prix ne peut pas dépasser {{value}}€", "minAmount": "La variation en € ne peut pas être inférieure à {{value}}€", "maxAmount": "La variation en € ne peut pas dépasser {{value}}€"}}, "minDateCpf": "CPF : Date minimale d'entrée en formation pour un dossier validé aujourd'hui (heure de Paris) : ", "activities": {"noData": "Aucune activité ni tâche", "form": {"title": "<PERSON><PERSON><PERSON> une activité ou une tâche", "updateTitle": "Modifier", "taskDone": "<PERSON><PERSON><PERSON> la tâche", "activityType": {"activity": "Activité", "task": "<PERSON><PERSON><PERSON>"}, "label": {"type": "Type", "startTime": "Date de début", "startAt": "<PERSON><PERSON><PERSON><PERSON><PERSON> le", "endTime": "Date de fin", "endedAt": "<PERSON><PERSON><PERSON><PERSON> le", "dueDate": "Date d'échéance", "origin": "Origine", "link": "<PERSON><PERSON>", "title": "Nom", "description": "Description", "qualiopi": "Associée à Qualiopi", "done": "<PERSON><PERSON><PERSON>", "indicator": "Indicateurs Qualiopi à associer", "user": "Responsable", "isDone": "Terminée"}, "submit": "Créer l'activité", "type": {"phone": "Téléphone", "email": "Email", "meeting": "Meeting", "chat": "Cha<PERSON>", "sms": "SMS", "progress": "Taux de réalisation", "examination": "Examen", "training": "Formation", "cdc": "Certification", "remark": "<PERSON><PERSON><PERSON>", "file": "Document", "connectionLog": "Log de connexion"}, "placeholder": {"qualiopi": "Aucun indicateur qualiopi associé"}}, "type": {"create": "Création", "update": "Mise à jour", "updateState": "Mise à jour état", "phone": "Téléphone", "email": "Email", "meeting": "Meeting", "chat": "Cha<PERSON>", "sms": "SMS", "progress": "Taux de réalisation", "examination": "Examen", "training": "Formation", "cdc": "Certification", "remark": "<PERSON><PERSON><PERSON>", "file": "Document", "connectionLog": "Log de connexion"}}}}, "support": {"title": "Nous contacter", "subtitle": "Nous sommes à votre disposition pour vous assister dans votre utilisation de la plateforme Wedof :", "content": {"text1": "Contactez-nous à l'adresse ", "text2": " dans les cas de figure suivant :"}, "list": {"text1": "Si vous faites l'expérience d'un problème technique.", "text2": "Si vous avez une suggestion concernant l'amélioration de Wedof.", "text3": "Si vous avez besoin d'aide pour effectuer une tâche spécifique."}, "mail": "<EMAIL>"}, "attendee": {"share": "Partager le dossier avec {{type}}", "type": {"attendee": "Apprenant", "candidate": "Candidat"}, "filesCard": {"nofile": "Aucun document"}, "registrationFolder": {"state": {"notProcessed": "Votre dossier de formation est en attente de traitement.", "notProcessedAlert": "A<PERSON> de valider le dossier, l'organisme doit saisir une date de début de session.", "validated": "Votre dossier de formation est en attente d'acceptation.", "waitingAcceptation": "Votre dossier de formation est en attente d'acceptation par un financeur (Pôle Emploi, OPCO..).", "accepted": "Votre dossier de formation est accepté.", "inTraining": "Vous êtes actuellement en cours de formation.", "terminated": "Vous avez été déclaré comme sorti de la formation et avez effectué {{completionRate}}% du parcours de formation.", "serviceDoneDeclared": "Vous avez effectué {{completionRate}}% du parcours de formation.", "serviceDoneValidated": "Vous avez effectué {{completionRate}}% du parcours de formation.", "canceledByAttendee": "Vous avez annulé votre dossier de formation.", "canceledByAttendeeNotRealized": "Vous avez annulé votre dossier de formation", "canceledByOrganism": "L'organisme a annulé votre dossier de formation.", "canceledByFinancer": "Le financeur a annulé votre dossier de formation", "refusedByAttendee": "<PERSON><PERSON> avez refusé votre dossier de formation.", "refusedByOrganism": "L'organisme a refusé votre dossier de formation.", "rejectedWithoutTitulaireSuite": "Le dossier de formation a été déclaré sans suite.", "rejectedWithoutCdcSuite": "Le dossier de formation a été déclaré sans suite.", "rejectedWithoutOfSuite": "Le dossier de formation a été déclaré sans suite.", "rejected": "Le dossier de formation a été déclaré sans suite."}}, "spaces": {"certificationFolderLink": "Espace Candidat", "registrationFolderLink": "Espace Apprenant"}, "experiences": {"title": "Mes Expériences", "add": "Ajouter une expérience", "noData": "Aucune expérience", "created": "Expérience créée le {{createdOn}}", "form": {"title": "Ajouter une expérience", "form": {"qualification": "Niveau de qualification", "certificationName": "Intitulé de la certification - dernière certification obtenue", "job": "Intitulé du poste", "startDate": "Date de début", "endDate": "Date de fin", "stillInPosition": "Je suis toujours en position", "companyName": "Nom de l'entreprise", "salaryYearly": {"title": "Rémunération brute annuelle (en €)", "error": "La rémunération doit être brute et annuelle"}, "situation": {"title": "Situation", "active": "En poste (hors alternance)", "inactive": "Inactif", "searching": "En recherche d'emploi", "training": "En formation"}, "contractType": {"title": "Type de contrat ou de statut", "cdd": "CDD", "cdi": "CDI", "interim": "Intérim", "independant": "Indépendant", "inactif": "Sans travail"}, "executiveStatus": "Statut cadre"}}}, "survey": {"title": "Suivi de l'insertion professionnelle", "subtitle": "Dans le but d'analyser l'impact de votre certification sur votre parcours professionnel nous vous invitons à remplir ces différents questionnaires. Ces questionnaires sont obligatoires et demandés par France Compétences.", "subtitleAttendee": "Ces informations sont collectées uniquement dans un cadre réglementaire à destination de France Compétences afin de démontrer la valeur d’usage de la certification sur le marché de l’emploi", "noData": "Aucune expérience associée", "date": "Questionnaire complété le {{date}}", "dueDate": "Questionnaire à compléter à partir du {{date}}", "candidateSituation": "Situation professionnelle", "initialExperience": {"title": "Situation professionnelle en début de cursus", "description": "Questionnaire ayant pour objectif d'analyser votre situation professionnelle avant le passage et l'obtention de votre certification <b>{{certificationName}}</b>."}, "sixMonthExperience": {"title": "Situation professionnelle 6 mois après l'obtention de la certification", "description": "Questionnaire ayant pour objectif d'examiner votre situation professionnelle 6 mois après l'obtention de la certification <b>{{certificationName}}</b>."}, "longTermExperience": {"title": "Situation professionnelle au moins un an après l'obtention de la certification", "description": "Questionnaire ayant pour objectif d'analyser votre situation professionnelle 1 an après l'obtention de la certification <b>{{certificationName}}</b>."}, "form": {"title": "Ma <PERSON> professionnelle", "add": "Ajouter une nouvelle situation professionnelle", "noSituationUpdate": "Ma situation n'a pas changé"}}}}}