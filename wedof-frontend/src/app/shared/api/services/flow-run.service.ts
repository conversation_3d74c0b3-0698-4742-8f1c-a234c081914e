import {HttpClient} from '@angular/common/http';
import {Injectable} from '@angular/core';
import {Observable} from 'rxjs';
import {APIEndpoint} from './api-endpoint.enum';
import {FlowRun, FlowRunResponse} from '../models/flow-run';

@Injectable({
    providedIn: 'root'
})
export class FlowRunService {

    constructor(private httpClient: HttpClient) {
    }

    getFlowRunsByEntity(entityClass: string, entityId: string): Observable<FlowRunResponse> {
        return this.httpClient.get<FlowRunResponse>(`${APIEndpoint.API}/flow-runs/${entityClass}/${entityId}`);
    }

    retryFlowRun(flowRun: FlowRun): Observable<void> {
        // Determine application name from flow run source, default to 'workflow' for backward compatibility
        const applicationName = flowRun.source || 'workflow';
        return this.httpClient.post<void>(`${APIEndpoint.API}/flow-runs/${flowRun.id}/retry/${applicationName}`, {
            strategy: 'ON_LATEST_VERSION'
        });
    }
}
