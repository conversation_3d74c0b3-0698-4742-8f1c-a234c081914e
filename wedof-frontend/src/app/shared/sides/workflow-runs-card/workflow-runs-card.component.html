<mat-card class="mat-card flex h-full flex-auto flex-col mt-3 p-5 pb-0 pt-3 border shadow-none sm"
          [ngClass]="{'card-loading':cardLoading}"
          *ngIf="shouldShowCard">
    <div class="flex items-center mb-2">
        <mat-icon
            [matBadge]="total > 0 ? total.toString() : null"
            matBadgePosition="below after"
            matBadgeSize="small"
            class="mr-3 card-loading-show text-4xl"
            color="primary">play_arrow
        </mat-icon>
        <div class="flex items-center">
            <span
                class="text-xl font-semibold card-loading-show">{{ 'private.application.workflow-runs.title' | translate }}</span>
        </div>
    </div>

    <div *ngIf="!cardLoading && showPromotionalContent" class="flex flex-col mt-3 mb-3">
        <div class="light:bg-cool-gray-50 dark:bg-cool-gray-700 border mb-4 p-4">
            <ul>
                <li *ngFor="let description of workflowPromoConfig.descriptions" class="flex items-center pb-1">
                    <mat-icon svgIcon="check" color="primary" class="icon-small mr-2"></mat-icon>
                    {{ description | translate }}
                </li>
            </ul>
            <div class="flex justify-center mt-4 mb-3">
                <button type="button"
                        (click)="activateWorkflowApp()"
                        color="primary"
                        class="mt-2"
                        mat-flat-button>
                    {{ 'private.layout.user.subscription.enableApp' | translate }}
                </button>
            </div>
        </div>
    </div>
    <div *ngIf="!cardLoading && !showPromotionalContent" class="flex flex-col mt-3 mb-3 overflow-y-auto">
        <div *ngIf="errorMessages.length > 0" class="mb-2">
            <div *ngFor="let errorMessage of errorMessages" class="text-red-500 text-sm">
                {{ errorMessage }}
            </div>
        </div>

        <div *ngIf="flowRuns?.length; else noResult">
            <div *ngFor="let flowRun of flowRuns" class="mb-2 cursor-pointer" (click)="openFlowRun(flowRun)">
                <div class="flex flex-row justify-between">
                    <div class="flex items-center">
                        <mat-icon
                            [matTooltip]="('private.application.workflow-runs.status.label' | translate) + ('private.application.workflow-runs.status.' + flowRun.status | translate)"
                            [class]="
                                  flowRun.status === 'FAILED' ? 'icon-size-18' : flowRun.status === 'SUCCEEDED' ?  'icon-size-18 text-green':
                                   flowRun.status === 'RUNNING' ? 'icon-size-18 text-blue' :  'icon-size-18 text-orange'"
                            [color]="flowRun.status === 'FAILED' ? 'warn' : null"
                            color="warn">{{flowRun.status === 'FAILED' ? 'error' : flowRun.status === 'SUCCEEDED' ? 'check_circle' :
                            flowRun.status === 'RUNNING' ? 'play_circle' : 'pause_circle' }}
                        </mat-icon>
                        <div class="pl-1">
                            <div class="flex">
                                <p class="font-medium" [matTooltip]="flowRun.flowDisplayName">
                                    {{ flowRun.flowDisplayName.length >= 33 ? ((flowRun.flowDisplayName | slice:0:32) + '...') : flowRun.flowDisplayName}}</p>
                                <span class="ml-2 text-disabled">{{ flowRun.id }}</span>
                                <span *ngIf="flowRun.source === 'activepieces'"
                                      class="ml-2 px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded">(Activepieces)</span>
                            </div>
                            <span class="text-disabled text-sm"
                                  [matTooltip]="flowRun.startTime | date:'short'">{{ flowRun.tasks }} {{ 'private.application.workflow-runs.tasks' | translate }}
                                - {{ daysAgo(flowRun.startTime) }}</span>
                        </div>
                    </div>

                    <div class="flex items-center">
                        <button *ngIf="flowRun.status === 'FAILED'" mat-icon-button (click)="retryFlowRun(flowRun); $event.stopPropagation();" [disabled]="cardLoading"
                                type="button" class="mr-2">
                            <mat-icon color="warn">refresh</mat-icon>
                        </button>
                        <button mat-icon-button (click)="openFlowRun(flowRun); $event.stopPropagation();" [disabled]="cardLoading"
                                type="button">
                            <mat-icon color="primary">open_in_new</mat-icon>
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <ng-template #noResult>
            <p class="mt-3 mb-3 text-center m-auto">
                {{ 'private.application.workflow-runs.noData' | translate }}
            </p>
        </ng-template>
    </div>
</mat-card>
