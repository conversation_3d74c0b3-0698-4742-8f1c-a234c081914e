import {Component, ElementRef, Input, <PERSON><PERSON><PERSON><PERSON>, OnDestroy, SimpleChanges} from '@angular/core';
import {Router} from '@angular/router';
import {FlowRun, FlowRunResponse} from '../../api/models/flow-run';
import {OrganismApplicationEntryPoint} from '../../api/models/applications';
import {FlowRunService} from '../../api/services/flow-run.service';
import {Observable, Subject} from 'rxjs';
import {takeUntil} from 'rxjs/operators';
import {daysAgo} from '../../utils/date-utils';
import {BaseCardComponentDirective, RequiredCallSuper} from '../../utils/base-card/base-card.directive';
import {HttpErrorResponse} from '@angular/common/http';
import {TranslateService} from '@ngx-translate/core';
import {OrganismApplicationService} from '../../api/services/organism-application.service';
import {OrganismApplication} from '../../api/models/organism-application';
import {Subscription, SubscriptionConfigPromotional, SubscriptionPromotionalOffers} from '../../api/models/subscription';
import {Organism} from '../../api/models/organism';
import {Select} from '@ngxs/store';
import {SubscriptionState} from '../../api/state/subscription.state';
import {OrganismState} from '../../api/state/organism.state';

@Component({
    selector: 'app-workflow-runs-card',
    templateUrl: './workflow-runs-card.component.html',
    styleUrls: ['./workflow-runs-card.component.scss']
})
export class WorkflowRunsCardComponent extends BaseCardComponentDirective implements OnChanges, OnDestroy {
    static COMPONENT_ID = 'workflowRuns';
    total = 0;
    flowRuns: FlowRun[] = [];
    errorMessages: string[] = [];
    shouldShowCard = true;
    showPromotionalContent = false;
    @Input() context: OrganismApplicationEntryPoint;
    @Input() entityId: number | string;
    @Input() entityClass: string;
    private _unsubscribeAll: Subject<void> = new Subject();

    @Select(SubscriptionState.subscription) subscription$: Observable<Subscription>;
    @Select(OrganismState.organism) organism$: Observable<Organism>;
    subscription: Subscription;
    organism: Organism;
    workflowPromoConfig = SubscriptionConfigPromotional[SubscriptionPromotionalOffers.WORKFLOW];

    constructor(private _flowRunService: FlowRunService,
                private _translateService: TranslateService, private _el: ElementRef, private _router: Router,
                private _organismApplicationService: OrganismApplicationService) {
        super(WorkflowRunsCardComponent.COMPONENT_ID, _el);
    }

    ngOnChanges(_changes: SimpleChanges): void {
        this.panelLoading();
        this.checkIfShouldShowCard();
    }

    checkIfShouldShowCard(): void {
        this._organismApplicationService.findAll().subscribe((organismApplications: OrganismApplication[]) => {
            const workflowApp = organismApplications.find(app => app.appId === 'workflow');
            const workflowEnabled = workflowApp?.enabled || false;
            const activepiecesApp = organismApplications.find(app => app.appId === 'activepieces');
            const activepiecesEnabled = activepiecesApp?.enabled &&
                activepiecesApp.metadata?.domain &&
                activepiecesApp.metadata?.apiKey;

            const hasWorkflowAccess = workflowEnabled || activepiecesEnabled;
            this.showPromotionalContent = !hasWorkflowAccess;
            if (hasWorkflowAccess) {
                this.initForm();
            } else {
                this.loadSubscriptionData();
            }
        });
    }

    initForm(): void {
        this.errorMessages = [];
        this._flowRunService.getFlowRunsByEntity(this.entityClass, this.entityId.toString()).subscribe({
            next: (flowRunResponse: FlowRunResponse) => {
                this.flowRuns = flowRunResponse?.data || [];
                this.total = this.flowRuns.length;
                this.panelLoaded();
            },
            error: (_error: HttpErrorResponse) => {
                this.errorMessages = [this._translateService.instant('private.application.workflow-runs.errors.loadingError')];
                this.panelLoaded();
            }
        });
    }

    openFlowRun(flowRun: FlowRun): void {
        if (flowRun.source === 'activepieces' && flowRun.baseUrl) {
            const activepiecesUrl = `${flowRun.baseUrl}/projects/${flowRun.projectId}/runs/${flowRun.id}`;
            window.open(activepiecesUrl, '_blank');
        } else {
            const workflowPath = `/projects/${flowRun.projectId}/runs/${flowRun.id}`;
            this._router.navigate(['mes-applications', 'workflow', 'app'], {
                state: { workflowPath: workflowPath }
            });
        }
    }

    retryFlowRun(flowRun: FlowRun): void {
        this.cardLoading = true;
        this._flowRunService.retryFlowRun(flowRun).subscribe({
            next: () => {
                this.initForm();
                this.cardLoading = false;
            },
            error: (_error: HttpErrorResponse) => {
                this.cardLoading = false;
                this.errorMessages = [this._translateService.instant('private.application.workflow-runs.errors.retryError')];
            }
        });
    }

    daysAgo(time: string | Date): string {
        const date = typeof time === 'string' ? new Date(time) : time;
        const getDays = daysAgo(date);
        return getDays === 0 ? 'Aujourd\'hui' : getDays > 0 ? 'Il y a ' + getDays + ' jours' : 'Dans ' + -getDays + ' jours';
    }

    loadSubscriptionData(): void {
        this.subscription$.pipe(takeUntil(this._unsubscribeAll)).subscribe(subscription => {
            this.subscription = subscription;
            this.checkDataLoaded();
        });
        this.organism$.pipe(takeUntil(this._unsubscribeAll)).subscribe(organism => {
            this.organism = organism;
            this.checkDataLoaded();
        });
    }

    private checkDataLoaded(): void {
        if (this.subscription && this.organism) {
            this.panelLoaded();
        }
    }

    activateWorkflowApp(): void {
        this._router.navigate(['/mes-applications'], {
            state: { enableAppId: 'workflow' }
        });
    }

    ngOnDestroy(): RequiredCallSuper {
        this._unsubscribeAll.next();
        this._unsubscribeAll.complete();
        return super.ngOnDestroy();
    }
}
