import {Injectable} from '@angular/core';
import {TranslateService} from '@ngx-translate/core';
import {BehaviorSubject, forkJoin, Observable} from 'rxjs';
import {map} from 'rxjs/operators';
import {TreoNavigationItem} from '../../../@treo/components/navigation';
import {Organism} from '../api/models/organism';
import {MatDialog} from '@angular/material/dialog';
import {
    Subscription,
    SubscriptionCertifierTypes,
    SubscriptionTrainingTypes,
    SubscriptionTypes
} from '../api/models/subscription';
import {DialogUpgradeSubscriptionComponent} from '../subscription/dialog-upgrade-subscription/dialog-upgrade-subscription.component';
import {OrganismApplication} from '../api/models/organism-application';
import {WorkflowApplication} from '../../applications/workflow/workflow.application';

interface NavigationItem extends TreoNavigationItem {
    enabled: boolean;
    children?: NavigationItem[];
}

@Injectable({
    providedIn: 'root'
})
export class NavigationService {

    constructor(
        private _translateService: TranslateService,
        private _dialog: MatDialog
    ) {
    }

    displayLayout = new BehaviorSubject(true);

    private _defaultNavigation(organism: Organism,
                               navigationI18n: any,
                               subscriptionI18n: any,
                               subscription: Subscription,
                               organismApplications: OrganismApplication[],
                               dialog: MatDialog): NavigationItem[] {
        const defaultBackground = '#fff';
        const defaultBackgroundHover = '#e0e0e0';
        const defaultColor = '#000';
        const defaultColorHover = '#232323';
        const trialBackground = '#8da2fb';
        const trialBackgroundHover = '#7790fc';
        const trialColor = '#fbfdfe';
        const trialColorHover = '#e5e5e5';
        const trainingColors = {
            background: subscription.trainingType === SubscriptionTrainingTypes.TRIAL ? trialBackground : defaultBackground,
            backgroundHover: subscription.trainingType === SubscriptionTrainingTypes.TRIAL ? trialBackgroundHover : defaultBackgroundHover,
            color: subscription.trainingType === SubscriptionTrainingTypes.TRIAL ? trialColor : defaultColor,
            colorHover: subscription.trainingType === SubscriptionTrainingTypes.TRIAL ? trialColorHover : defaultColorHover,
        };
        const certifierColors = {
            background: subscription.certifierType === SubscriptionCertifierTypes.TRIAL ? trialBackground : defaultBackground,
            backgroundHover: subscription.certifierType === SubscriptionCertifierTypes.TRIAL ? trialBackgroundHover : defaultBackgroundHover,
            color: subscription.certifierType === SubscriptionCertifierTypes.TRIAL ? trialColor : defaultColor,
            colorHover: subscription.certifierType === SubscriptionCertifierTypes.TRIAL ? trialColorHover : defaultColorHover,
        };
        const workflowApplicationEnabled = organismApplications.find(app => app.appId === WorkflowApplication.appId())?.enabled;

        return [
            {
                enabled: true,
                type: 'divider'
            },
            {
                id: 'workflow',
                title: 'Processus Métiers Automatisés',
                enabled: true,
                type: 'basic',
                icon: 'schema',
                badge: !workflowApplicationEnabled ? {
                    title: 'Nouveau',
                    style: 'rounded',
                    background: trialBackground,
                    backgroundHover: trialBackgroundHover,
                    color: trialColor,
                    colorHover: trialColorHover,
                } : {},
                link: '/mes-applications/workflow/app'
            },
            {
                enabled: true,
                type: 'divider'
            },
            {
                id: 'trainingOrganism',
                title: navigationI18n.trainingOrganism.title,
                enabled: !!organism?.isTrainingOrganism,
                type: 'group',
                badge: {
                    title: subscription.trainingType === SubscriptionTrainingTypes.NONE && subscription.trainingTrialEndDate
                        ? subscriptionI18n.training[subscription.trainingType].badge_subscribe :
                        subscriptionI18n.training[subscription.trainingType]?.badge,
                    style: 'rounded',
                    ...trainingColors,
                    function: () => this.openSubscriptionDialog(organism, subscription, dialog, SubscriptionTypes.TRAINING)
                },
                link: '/formation/dossiers',
                children: [
                    {
                        id: 'trainingOrganismFolders',
                        title: navigationI18n.trainingOrganism.children.folders.title,
                        enabled: true,
                        type: 'basic',
                        icon: 'library_books',
                        link: '/formation/dossiers'
                    },
                    {
                        id: 'trainingOrganismStats',
                        title: navigationI18n.trainingOrganism.children.stats.title,
                        enabled: true,
                        type: 'basic',
                        icon: 'poll',
                        link: '/formation/statistiques'
                    },
                    {
                        id: 'trainingOrganismCertifications',
                        title: navigationI18n.trainingOrganism.children.certifications.title,
                        enabled: true,
                        type: 'basic',
                        icon: 'group',
                        link: '/formation/certifications/catalogue',
                        queryParams: {state: 'active'}
                    },
                    {
                        id: 'trainingOrganismProposals',
                        title: navigationI18n.trainingOrganism.children.proposals.title,
                        enabled: true,
                        type: 'basic',
                        icon: 'local_offer',
                        link: '/formation/propositions'
                    },
                ]
            },
            {
                enabled: !!organism?.isCertifierOrganism ||
                    subscription?.certifierType === SubscriptionCertifierTypes.PARTNER ||
                    subscription?.certifierType === SubscriptionCertifierTypes.PARTNER_PLUS,
                type: 'divider'
            },
            {
                id: 'certificationOrganism',
                title: navigationI18n.certificationOrganism.title,
                enabled: !!organism?.isCertifierOrganism
                    || subscription?.certifierType === SubscriptionCertifierTypes.PARTNER ||
                    subscription?.certifierType === SubscriptionCertifierTypes.PARTNER_PLUS,
                type: 'group',
                badge: {
                    title: subscription?.certifierType === SubscriptionCertifierTypes.USAGE ?
                        (subscriptionI18n.certifier.usage.subtype[subscription.allowCertifierPlus ? 'premium' : 'standard']) :
                        (subscription.certifierType === SubscriptionCertifierTypes.NONE && subscription.certifierTrialEndDate ?
                            subscriptionI18n.certifier[subscription.certifierType].badge_subscribe :
                            subscriptionI18n.certifier[subscription.certifierType]?.badge),
                    style: 'rounded',
                    ...certifierColors,
                    function: () => {
                        if (organism?.isCertifierOrganism) {
                            this.openSubscriptionDialog(organism, subscription, dialog, SubscriptionTypes.CERTIFIER);
                        }
                    }
                },
                children: [
                    {
                        id: 'certificationFolders',
                        title: navigationI18n.certificationOrganism.children.certificationFolders.title,
                        enabled: true,
                        type: 'basic',
                        icon: 'library_books',
                        link: '/certification/dossiers'
                    },
                    {
                        id: 'partners',
                        title: navigationI18n.certificationOrganism.children.partners.title,
                        enabled: !!organism?.isCertifierOrganism,
                        type: 'basic',
                        icon: 'group',
                        link: '/certification/partenariats'
                    }
                ]
            },
            {
                enabled: true,
                type: 'divider'
            },
            {
                id: 'helpCenter',
                title: navigationI18n.helpCenter.title,
                enabled: true,
                type: 'group',
                children: [
                    {
                        id: 'documentation',
                        title: navigationI18n.helpCenter.children.documentation.title,
                        enabled: true,
                        type: 'basic',
                        icon: 'find_in_page',
                        link: '/aide'
                    },
                    {
                        id: 'faq',
                        title: navigationI18n.helpCenter.children.faq.title,
                        enabled: true,
                        type: 'basic',
                        icon: 'quiz',
                        link: '/aide/faqs'
                    },
                    {
                        id: 'support',
                        title: navigationI18n.helpCenter.children.support.title,
                        enabled: true,
                        type: 'basic',
                        icon: 'mail',
                        link: '/aide/support'
                    },
                ]
            }
        ];
    }

    getNavigation(organism: Organism, subscription: Subscription, organismApplications: OrganismApplication[]): Observable<TreoNavigationItem[]> {
        return forkJoin([
            this._translateService.get('private.navigation'),
            this._translateService.get('private.layout.user.subscription'),
        ]).pipe(
            map(([navigationI18n, subscriptionI18n]) =>
                this._defaultNavigation(organism, navigationI18n, subscriptionI18n, subscription, organismApplications, this._dialog)
            ),
            map((navigationItems: NavigationItem[]) => {
                return navigationItems
                    .filter(nav => nav.enabled)
                    .map(nav => ({
                        ...nav,
                        ...{
                            children: nav.children?.filter(subNav => subNav.enabled)
                        }
                    }));
            })
        );
    }

    public seDisplayLayout(displayLayout: boolean): void {
        this.displayLayout.next(displayLayout);
    }

    private openSubscriptionDialog(organism: Organism, subscription: Subscription, dialog: MatDialog, subscriptionType: SubscriptionTypes): void {
        dialog.open(DialogUpgradeSubscriptionComponent, {
            panelClass: ['full-page-scroll-50'],
            data: {
                organism: organism,
                subscription: subscription,
                subscriptionTypeToShow: subscriptionType
            }
        });
    }
}
