import {Component, Input, OnChanges, OnDestroy, OnInit, SimpleChanges, TemplateRef, ViewChild} from '@angular/core';
import {MatSnackBar} from '@angular/material/snack-bar';
import {Attendee} from '../../api/models/attendee';
import {Select} from '@ngxs/store';
import {OrganismState} from '../../api/state/organism.state';
import {combineLatest, Observable, Subject} from 'rxjs';
import {Organism} from '../../api/models/organism';
import {takeUntil} from 'rxjs/operators';
import {DialogUpgradeSubscriptionComponent} from '../../subscription/dialog-upgrade-subscription/dialog-upgrade-subscription.component';
import {
    Subscription,
    SubscriptionCertifierTypes,
    SubscriptionTrainingTypes,
    SubscriptionTypes
} from '../../api/models/subscription';
import {MatDialog} from '@angular/material/dialog';
import {TranslateService} from '@ngx-translate/core';
import {UserState} from '../../api/state/user.state';
import {User} from '../../api/models/user';
import {RegistrationFolderService} from '../../api/services/registration-folder.service';
import {CertificationFolderService} from '../../api/services/certification-folder.service';

@Component({
    selector: 'app-attendee-menu-folders',
    templateUrl: './attendee-menu-folders.component.html',
    styleUrls: ['./attendee-menu-folders.component.scss']
})
export class AttendeeMenuFoldersComponent implements OnInit, OnDestroy, OnChanges {


    @Input() externalId: string;
    @Input() attendeeLink: string;
    @Input() addToPassportLink?: string;
    @Input() surveyLink: string;
    @Input() attendee: Attendee;
    @Input() subscription?: Subscription;
    @Input() organism?: Organism;
    @Input() attendeeType: 'attendee' | 'candidate';
    @Input() permalink: string;
    @ViewChild('copySuccess') copySuccessTemplate: TemplateRef<any>;

    @Select(UserState.user) user$: Observable<User>;
    @Select(OrganismState.organism) organism$: Observable<Organism>;

    user: User;
    magicLink: string;
    organismName: string;
    attendeeTypeI18n = '';

    private _unsubscribeAll = new Subject<void>();

    constructor(
        private _snackBar: MatSnackBar,
        private _translateService: TranslateService,
        private _registrationFolderService: RegistrationFolderService,
        private _certificationFolderService: CertificationFolderService,
        private _dialog: MatDialog
    ) {
    }

    ngOnInit(): void {
        this.attendeeTypeI18n = this._translateService.instant('private.attendee.type.' + this.attendeeType);
        combineLatest([
            this.user$,
            this.organism$
        ]).pipe(
            takeUntil(this._unsubscribeAll)
        ).subscribe(([user, organism]) => {
            this.user = user;
            this.organismName = organism.name;
            this.generateMagicLink();
        });
    }

    getTooltip(): string {
        const prefix = this.attendeeType === 'attendee' ? 'l\'' : 'le ';
        const type = prefix + this._translateService.instant('private.attendee.type.' + this.attendeeType);
        return this._translateService.instant('private.attendee.share', {type: type});
    }

    copy(): void {
        this._snackBar.openFromTemplate(this.copySuccessTemplate, {
            duration: 2500,
        });
    }

    ngOnDestroy(): void {
        this._unsubscribeAll.next();
        this._unsubscribeAll.complete();
    }

    openDialogSubscription(): void {
        this._dialog.open(DialogUpgradeSubscriptionComponent, {
            panelClass: ['full-page-scroll-50'],
            data: {
                organism: this.organism,
                subscription: this.subscription,
                fromPage: 'attendee',
                subscriptionTypeToShow: this.attendeeType === 'attendee' ? SubscriptionTypes.TRAINING : SubscriptionTypes.CERTIFIER
            }
        });
    }

    hasAccess(): boolean {
        if (this.attendeeType === 'attendee') {
            return [SubscriptionTrainingTypes.TRIAL, SubscriptionTrainingTypes.ACCESS, SubscriptionTrainingTypes.ACCESS_PLUS, SubscriptionTrainingTypes.STANDARD,
                SubscriptionTrainingTypes.PREMIUM].includes(this.subscription.trainingType);
        } else {
            return this.subscription.allowCertifierPlus || [SubscriptionCertifierTypes.PARTNER, SubscriptionCertifierTypes.PARTNER_PLUS].includes(this.subscription.certifierType);
        }
    }

    ngOnChanges(changes: SimpleChanges): void {
        this.generateMagicLink();
    }

    private generateMagicLink(): void {
        if (this.user && this.user.is_impersonator) {
            this.magicLink = null;
            if (this.attendeeType === 'attendee') {
                this._registrationFolderService.getMagicLink(this.externalId).subscribe(data => {
                    this.magicLink = data.url;
                });
            } else {
                this._certificationFolderService.getMagicLink(this.externalId).subscribe(data => {
                    this.magicLink = data.url;
                });
            }
        }
    }
}
