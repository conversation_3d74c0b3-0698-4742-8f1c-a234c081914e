<table class="w-full mt-2 mb-10 table offers">
    <colgroup>
        <col>
        <col
            [class]="subscription.certifierType === SubscriptionCertifierTypes.PARTNER  ? 'highlightCard' : '' ">
        <col
            [class]="subscription.certifierType === SubscriptionCertifierTypes.PARTNER_PLUS ? 'highlightCard' : '' ">
    </colgroup>
    <thead>
    <tr class="h-5">
        <th class="w-1/6 mr-3"></th>
        <th class="w-1/6 mr-3"></th>
        <th class="w-1/6 mr-3"></th>
    </tr>
    <tr>
        <th class="w-1/6 mr-3"></th>
        <th [class]="subscription.certifierType === SubscriptionCertifierTypes.PARTNER ? 'rounded-t w-1/5 mr-3 pb-2' : 'w-1/6 mr-3 pb-2'">
            {{'private.layout.user.subscription.certifier.partner.offer' | translate}}
        </th>
        <th [class]="subscription.certifierType === SubscriptionCertifierTypes.PARTNER_PLUS ? 'rounded-t w-1/5 pb-2' : 'w-1/6 pb-2'">
            {{'private.layout.user.subscription.certifier.partnerPlus.offer' | translate}}
        </th>
    </tr>
    </thead>
    <tbody>
    <tr>
        <td style="padding-left: 8px;"></td>
        <td>{{'private.layout.user.subscription.certifier.free.badge' | translate}}</td>
        <td>
            <strong> 29,90€ HT / mois </strong>
        </td>
    </tr>
    <tr class="h-8 block-standard-content first-row">
        <td class="font-bold flex items-center no-text-center">
            {{'private.layout.user.subscription.certifier.api.header' | translate}}
        </td>
        <td>
            <mat-icon color="warn" class="align-middle opacity-54">cancel</mat-icon>
        </td>
        <td>
            <mat-icon color="primary" class="align-middle opacity-54">check_circle</mat-icon>
        </td>
    </tr>
    <tr class="h-8 block-standard-content">
        <td class="font-bold">{{'private.layout.user.subscription.habilitation' | translate}}</td>
        <td>
            <mat-icon color="warn" class="align-middle opacity-54">cancel</mat-icon>
        </td>
        <td>
            <mat-icon color="primary" class="align-middle opacity-54">check_circle</mat-icon>
        </td>
    </tr>
    <tr class="h-8 block-standard-content">
        <td class="font-bold">{{'private.layout.user.subscription.updateTrainingActions' | translate}}</td>
        <td>
            <mat-icon color="warn" class="align-middle opacity-54">cancel</mat-icon>
        </td>
        <td>
            <mat-icon color="primary" class="align-middle opacity-54">check_circle</mat-icon>
        </td>
    </tr>
    <tr class="h-8 block-standard-content">
        <td class="font-bold">{{'private.layout.user.subscription.autoEvaluationAudit' | translate}}</td>
        <td>
            <mat-icon color="warn" class="align-middle opacity-54">cancel</mat-icon>
        </td>
        <td>
            <mat-icon color="primary" class="align-middle opacity-54">check_circle</mat-icon>
        </td>
    </tr>
    <tr class="h-8 block-standard-content last-row">
        <td class="font-bold">{{'private.layout.user.subscription.manageCertifierContact' | translate}}</td>
        <td>
            <mat-icon color="warn" class="align-middle opacity-54">cancel</mat-icon>
        </td>
        <td>
            <mat-icon color="primary" class="align-middle opacity-54">check_circle</mat-icon>
        </td>
    </tr>

    <tr *ngIf="!subscription.certifierPartnership">
        <td></td>
        <td [class]="subscription.certifierType === SubscriptionCertifierTypes.PARTNER ? 'rounded-b' : ''">
            <ng-template [ngTemplateOutlet]="genericButton"
                         [ngTemplateOutletContext]="{isMySubscription: subscription.certifierType == SubscriptionCertifierTypes.PARTNER}">
            </ng-template>
        </td>
        <td [class]="subscription.certifierType === SubscriptionCertifierTypes.PARTNER_PLUS  ? 'rounded-b' : ''">
            <ng-template [ngTemplateOutlet]="genericButton"
                         [ngTemplateOutletContext]="{isMySubscription: subscription.certifierType === SubscriptionCertifierTypes.PARTNER_PLUS}">
            </ng-template>
        </td>
        <ng-template #genericButton let-isMySubscription="isMySubscription" let-type="type">
            <button *ngIf="isMySubscription; else notMineSubscription"
                    color="primary" mat-flat-button class="w-5/6 mr-auto ml-auto flex mb-2 mt-4"
                    [disabled]="isMySubscription">
                {{ 'private.layout.user.subscription.mySubscription' | translate }}
            </button>
            <ng-template #notMineSubscription>
                <button
                    color="primary" mat-flat-button class="w-5/6 mr-auto ml-auto flex mb-2 mt-4"
                    (click)="subscriptionLink()">
                    {{ 'private.layout.user.subscription.select' | translate }}
                </button>
            </ng-template>
        </ng-template>
    </tr>
    </tbody>
</table>
