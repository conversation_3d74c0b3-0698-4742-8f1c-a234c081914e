::ng-deep {
    li {
        list-style-type: none;
    }

    .highlightCard {
        background-color: #F1F5F9;
    }

    table.offers {
        tr > td:not(:first-of-type) {
            text-align: center;
        }
    }

    .bg-trial-purple {
        background-color: #9E9CF3;
    }
}
.block-standard-content {
    &:first-of-type td {
        padding-top: 8px;
    }

    &:last-of-type td {
        padding-bottom: 8px;
    }

    > td:first-of-type {
        padding-left: 12px;
    }
}

.block-premium-content {
    &:first-of-type td {
        padding-top: 8px;
    }

    &:last-of-type td {
        padding-bottom: 8px;
    }

    background: rgba(158, 156, 243, 0.2);

    > td:first-of-type {
        padding-left: 12px;
    }
}

.first-row {
    > td {
        padding-top: 12px;
    }
}

.last-row {
    > td {
        padding-top: 6px;
        padding-bottom: 12px;
    }
}
