import {Component, Input, OnInit} from '@angular/core';
import {Organism} from '../../api/models/organism';
import {Subscription, SubscriptionCertifierTypes} from '../../api/models/subscription';
import {SubscriptionService} from '../../api/services/subscription.service';

@Component({
  selector: 'app-subscription-certifier-types-for-partner',
  templateUrl: './subscription-certifier-types-for-partner.component.html',
  styleUrls: ['./subscription-certifier-types-for-partner.component.scss']
})
export class SubscriptionCertifierTypesForPartnerComponent implements OnInit {

    SubscriptionCertifierTypes = SubscriptionCertifierTypes;

    @Input() subscription: Subscription;
    @Input() organism: Organism;

  constructor(
      private _subscriptionService: SubscriptionService
  ) { }

  ngOnInit(): void {
  }

    subscriptionLink(): void {
        this._subscriptionService.showStripeCertifierAccount({
            subscriptionCertifierType: SubscriptionCertifierTypes.PARTNER_PLUS
        }).subscribe(response => {
            document.location.href = response.url;
        });
    }

}
