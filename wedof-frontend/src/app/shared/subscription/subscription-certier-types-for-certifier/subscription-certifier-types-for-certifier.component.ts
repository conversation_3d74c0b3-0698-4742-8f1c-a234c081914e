import {Component, Input, OnInit} from '@angular/core';
import {Subscription, SubscriptionCertifierTypes, SubscriptionOptions} from '../../api/models/subscription';
import {Organism} from '../../api/models/organism';
import {SubscriptionService} from '../../api/services/subscription.service';

@Component({
    selector: 'app-subscription-certifier-types-for-certifier',
    templateUrl: './subscription-certifier-types-for-certifier.component.html',
    styleUrls: ['./subscription-certifier-types-for-certifier.component.scss']
})
export class SubscriptionCertifierTypesForCertifierComponent implements OnInit {

    remainingDays = '0';
    SubscriptionCertifierTypes = SubscriptionCertifierTypes;

    @Input() userName: string;
    @Input() defaultCertificationFoldersNumberCap: number;
    @Input() defaultCertificationFoldersNumberCapAccess: number;
    @Input() subscription: Subscription;
    @Input() organism: Organism;

    constructor(
        private _subscriptionService: SubscriptionService
    ) {
    }

    ngOnInit(): void {
        if (this.subscription.certifierType === SubscriptionCertifierTypes.TRIAL) {
            this.remainingDays = new Date(this.subscription.certifierPeriodEndDate).remainingDays();
        }
    }

    isCustomer(): boolean {
        return [SubscriptionCertifierTypes.UNLIMITED, SubscriptionCertifierTypes.USAGE].includes(this.subscription.certifierType);
    }

    getMail(subscriptionCertifierType: SubscriptionCertifierTypes, certifierPlus: boolean): string {
        const currentSubscription =
            this.subscription.certifierType === SubscriptionCertifierTypes.UNLIMITED ? 'Illimité' :
                (this.subscription.certifierType === SubscriptionCertifierTypes.USAGE && this.subscription.allowCertifierPlus ? 'Premium Certificateur' : 'Standard Certificateur');
        const targetSubscription = subscriptionCertifierType === SubscriptionCertifierTypes.UNLIMITED ? 'Illimité' :
            (subscriptionCertifierType === SubscriptionCertifierTypes.USAGE && certifierPlus ? 'Premium Certificateur' : 'Standard Certificateur');
        return 'mailto:<EMAIL>?' +
            'subject=Modification de l\'abonnement&' +
            'body=Bonjour l\'équipe Wedof, %0D%0A  %0D%0A Je souhaiterais modifier mon abonnement actuel ' + currentSubscription + ' pour l\'organisme ' +
            this.organism.name + ' ' + this.organism.siret + ' ' + ' et avoir l\'abonnement ' + targetSubscription + ' %0D%0A %0D%0A' + 'Bien à vous, %0D%0A ' + this.userName;
    }

    getCertificationFolderPrice(certifierPlus: boolean): number {
        return certifierPlus ? 8.90 : 9.90;
    }

    subscriptionLink(subscriptionCertifierType?: SubscriptionCertifierTypes, options?: SubscriptionOptions): void {
        this._subscriptionService.showStripeCertifierAccount({
            subscriptionCertifierType: subscriptionCertifierType ?? null,
            options: options
        }).subscribe(response => {
            document.location.href = response.url;
        });
    }

}
