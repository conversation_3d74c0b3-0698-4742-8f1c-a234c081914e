<table class="w-full mt-2 mb-10 table offers">
    <colgroup>
        <col>
        <col
            [class]="subscription.certifierType === SubscriptionCertifierTypes.USAGE && !subscription.allowCertifierPlus ? 'highlightCard' : '' ">
        <col
            [class]="subscription.certifierType === SubscriptionCertifierTypes.ACCESS ? 'highlightCard' : '' ">
        <col
            [class]="(subscription.certifierType === SubscriptionCertifierTypes.USAGE && subscription.allowCertifierPlus) ||
                subscription.certifierType === SubscriptionCertifierTypes.UNLIMITED  || subscription.certifierType === SubscriptionCertifierTypes.TRIAL   ? 'highlightCard' : '' ">
    </colgroup>
    <thead
        [class]="subscription.certifierType === SubscriptionCertifierTypes.PARTNER ? 'w-1/6 mr-3' : 'w-1/5 mr-3'">
    <tr class="h-5" *ngIf="subscription.certifierType === SubscriptionCertifierTypes.TRIAL">
        <th class="w-1/6 mr-3"></th>
        <th class="w-1/6 mr-3"></th>
        <th class="w-1/6 mr-3"></th>
        <th class="rounded-t w-1/5 mr-3 pt-2 pb-2 text-white bg-trial-purple">
            {{'private.layout.user.subscription.certifier.trial.offer' | translate }}<br>
            {{ remainingDays }} {{ 'private.layout.user.subscription.certifier.trial.remaining-days' | translate }}
        </th>
    </tr>
    <tr>
        <th class="w-1/6 mr-3"></th>
        <th [class]="(subscription.certifierType === SubscriptionCertifierTypes.USAGE && !subscription.allowCertifierPlus) ||
        subscription.certifierType === SubscriptionCertifierTypes.PARTNER ? 'rounded-t w-1/5 mr-3 pt-4 pb-2' : 'w-1/6 mr-3 pt-4 pb-2'">
            {{'private.layout.user.subscription.certifier.usage.offer' | translate}} {{'private.layout.user.subscription.certifier.usage.subtype.standard' | translate}}
        </th>
        <th [class]="(subscription.certifierType === SubscriptionCertifierTypes.ACCESS) ? 'rounded-t w-1/5 pt-4 pb-2' : 'w-1/6 pt-4 pb-2'">
            {{'private.layout.user.subscription.certifier.access.offer' | translate}}
        </th>
        <th [class]="(subscription.certifierType === SubscriptionCertifierTypes.USAGE && subscription.allowCertifierPlus) || subscription.certifierType === SubscriptionCertifierTypes.UNLIMITED ||
            subscription.certifierType === SubscriptionCertifierTypes.PARTNER || subscription.certifierType === SubscriptionCertifierTypes.TRIAL ? 'rounded-t w-1/5 pt-4 pb-2' : 'w-1/6 pt-4 pb-2'">
            <ng-container
                *ngIf="subscription.certifierType === SubscriptionCertifierTypes.UNLIMITED else usageOffer">
                {{'private.layout.user.subscription.certifier.unlimited.offer' | translate}}
            </ng-container>
            <ng-template #usageOffer>
                {{'private.layout.user.subscription.certifier.usage.offer' | translate}} {{'private.layout.user.subscription.certifier.usage.subtype.premium' | translate}}
            </ng-template>
        </th>
    </tr>
    </thead>
    <tbody>
    <tr>
        <td style="padding-left: 8px;"></td>
        <td>
            <strong>{{ getCertificationFolderPrice(false).toFixed(2) }}€ HT / dossier</strong>
            <br> <span
            class="text-sm">{{'private.layout.user.subscription.certifier.foldersCountExplanation' | translate : {
            count: 500
        }    }}</span>
        </td>
        <td>
            <strong>49€ HT / mois
                + {{ getCertificationFolderPrice(true).toFixed(2) }}€ HT / dossier</strong>
            <br> <span
            class="text-sm">{{'private.layout.user.subscription.certifier.foldersCountExplanationAccess' | translate : {
            count: 100
        }    }}</span>
        </td>
        <td>
            <strong>
                <ng-container
                    *ngIf="subscription.certifierType === SubscriptionCertifierTypes.UNLIMITED else mensualUsage">
                    {{'private.layout.user.subscription.annual' | translate}}
                </ng-container>
                <ng-template #mensualUsage>
                    199€ HT / mois
                    + {{ getCertificationFolderPrice(true).toFixed(2) }}€ HT / dossier
                    <br> <span
                    class="text-sm">{{'private.layout.user.subscription.certifier.foldersCountExplanation' | translate : {
                    count: 500
                }    }}</span>
                </ng-template>
            </strong>
        </td>
    </tr>
    <tr class="h-8 block-standard-content first-row">
        <td class="font-bold no-text-center">{{'private.layout.user.subscription.table.header.userLimit' | translate}}
        </td>
        <td>1</td>
        <td>
            <mat-icon color="primary" class="opacity-54">all_inclusive</mat-icon>
        </td>
        <td>
            <mat-icon color="primary" class="opacity-54">all_inclusive</mat-icon>
        </td>
    </tr>
    <tr class="h-8 block-standard-content">
        <td class="font-bold flex items-center no-text-center">{{'private.layout.user.subscription.certifier.folders.header' | translate}}
            <mat-icon class="pl-1"
                      [matTooltip]="'private.layout.user.subscription.certifier.folders.explanation' | translate"
                      [matTooltipPosition]="'above'"
                      [matTooltipShowDelay]="500"
                      [svgIcon]="'help'"></mat-icon>
        </td>
        <td>
            <mat-icon color="primary" class="align-middle opacity-54">check_circle</mat-icon>
        </td>
        <td>
            <mat-icon color="primary" class="align-middle opacity-54">check_circle</mat-icon>
        </td>
        <td>
            <mat-icon color="primary" class="align-middle opacity-54">check_circle</mat-icon>
        </td>
    </tr>
    <tr class="h-8 block-standard-content">
        <td class="font-bold flex items-center no-text-center">{{'private.layout.user.subscription.certifier.api.header' | translate}}
            <mat-icon class="pl-1"
                      [matTooltip]="'private.layout.user.subscription.certifier.api.explanation' | translate"
                      [matTooltipPosition]="'above'"
                      [matTooltipShowDelay]="500"
                      [svgIcon]="'help'"></mat-icon>
        </td>
        <td>
            <mat-icon color="primary" class="align-middle opacity-54">check_circle</mat-icon>
        </td>
        <td>
            <mat-icon color="primary" class="align-middle opacity-54">check_circle</mat-icon>
        </td>
        <td>
            <mat-icon color="primary" class="align-middle opacity-54">check_circle</mat-icon>
        </td>
    </tr>
    <tr class="h-8 block-standard-content">
        <td class="font-bold flex items-center no-text-center">{{'private.layout.user.subscription.certifier.webhooks.header' | translate}}
            <mat-icon class="pl-1"
                      [matTooltip]="'private.layout.user.subscription.certifier.webhooks.explanation' | translate"
                      [matTooltipPosition]="'above'"
                      [matTooltipShowDelay]="500"
                      [svgIcon]="'help'"></mat-icon>
        </td>
        <td>
            <mat-icon color="primary" class="align-middle opacity-54">check_circle</mat-icon>
        </td>
        <td>
            <mat-icon color="primary" class="align-middle opacity-54">check_circle</mat-icon>
        </td>
        <td>
            <mat-icon color="primary" class="align-middle opacity-54">check_circle</mat-icon>
        </td>
    </tr>
    <tr class="h-8 block-standard-content">
        <td class="font-bold flex items-center">{{'private.layout.user.subscription.certifier.synchronization.header' | translate}}
            <mat-icon class="pl-1"
                      [matTooltip]="'private.layout.user.subscription.certifier.synchronization.explanation' | translate"
                      [matTooltipPosition]="'above'"
                      [matTooltipShowDelay]="500"
                      [svgIcon]="'help'"></mat-icon>
        </td>
        <td>
            <mat-icon color="primary" class="align-middle opacity-54">check_circle</mat-icon>
        </td>
        <td>
            <mat-icon color="primary" class="align-middle opacity-54">check_circle</mat-icon>
        </td>
        <td>
            <mat-icon color="primary" class="align-middle opacity-54">check_circle</mat-icon>
        </td>
    </tr>
    <tr class="h-8 block-standard-content">
        <td class="font-bold flex items-center">{{'private.layout.user.subscription.certifier.apps.header' | translate}}
            <mat-icon class="pl-1"
                      [matTooltip]="'private.layout.user.subscription.certifier.apps.explanation' | translate"
                      [matTooltipPosition]="'above'"
                      [matTooltipShowDelay]="500"
                      [svgIcon]="'help'"></mat-icon>
        </td>
        <td>
            <mat-icon color="primary" class="align-middle opacity-54">check_circle</mat-icon>
        </td>
        <td>
            <mat-icon color="primary" class="align-middle opacity-54">check_circle</mat-icon>
        </td>
        <td>
            <mat-icon color="primary" class="align-middle opacity-54">check_circle</mat-icon>
        </td>
    </tr>
    <tr class="h-8 block-standard-content">
        <td class="font-bold">{{'private.layout.user.subscription.certifier.additionalCost.header' | translate}}*
        </td>
        <td>{{'private.layout.user.subscription.certifier.additionalCost.price' | translate}}</td>
        <td>{{'private.layout.user.subscription.certifier.additionalCost.priceAccess' | translate}}</td>
        <td>{{'private.layout.user.subscription.certifier.additionalCost.price' | translate}}</td>
    </tr>
    <tr class="h-8 block-standard-content">
        <td class="font-bold">{{'private.layout.user.subscription.signature' | translate}} </td>
        <td>
            49€ HT / mois
        </td>
        <td>
            49€ HT / mois
        </td>
        <td>
            49€ HT / mois
        </td>
    </tr>
    <tr class="h-8 block-standard-content last-row">
        <td class="font-bold">{{'private.layout.user.subscription.workflow' | translate}} </td>
        <td>
            49€ HT / mois
        </td>
        <td>
            49€ HT / mois
        </td>
        <td>
            49€ HT / mois
        </td>
    </tr>
    <tr class="h-8 block-premium-content first-row">
        <td class="font-bold flex items-center">{{'private.layout.user.subscription.certifier.autoDeposit.header' | translate}}
            <mat-icon class="pl-1"
                      [matTooltip]="'private.layout.user.subscription.certifier.autoDeposit.explanation' | translate"
                      [matTooltipPosition]="'above'"
                      [matTooltipShowDelay]="500"
                      [svgIcon]="'help'"></mat-icon>
        </td>
        <td>
            <mat-icon color="warn" class="align-middle opacity-54">cancel</mat-icon>
        </td>
        <td>
            <mat-icon color="primary" class="align-middle opacity-54">check_circle</mat-icon>
        </td>
        <td>
            <mat-icon color="primary" class="align-middle opacity-54">check_circle</mat-icon>
        </td>
    </tr>
    <tr class="h-8 block-premium-content">
        <td class="font-bold">{{'private.layout.user.subscription.certifier.managePartnership' | translate}}</td>
        <td>
            <mat-icon color="warn" class="align-middle opacity-54">cancel</mat-icon>
        </td>
        <td>
            <mat-icon color="primary" class="align-middle opacity-54">check_circle</mat-icon>
        </td>
        <td>
            <mat-icon color="primary" class="align-middle opacity-54">check_circle</mat-icon>
        </td>
    </tr>
    <tr class="h-8 block-premium-content">
        <td class="font-bold flex items-center">{{'private.layout.user.subscription.certifier.nocode.header' | translate}}
            <mat-icon class="pl-1"
                      [matTooltip]="'private.layout.user.subscription.certifier.nocode.explanation' | translate"
                      [matTooltipPosition]="'above'"
                      [matTooltipShowDelay]="500"
                      [svgIcon]="'help'"></mat-icon>
        </td>
        <td>
            <mat-icon color="warn" class="align-middle opacity-54">cancel</mat-icon>
        </td>
        <td>
            <mat-icon color="primary" class="align-middle opacity-54">check_circle</mat-icon>
        </td>
        <td>
            <mat-icon color="primary" class="align-middle opacity-54">check_circle</mat-icon>
        </td>
    </tr>
    <tr class="h-8 block-premium-content">
        <td class="font-bold">{{'private.layout.user.subscription.certifier.surveys' | translate}}</td>
        <td>
            <mat-icon color="warn" class="align-middle opacity-54">cancel</mat-icon>
        </td>
        <td>
            <mat-icon color="primary" class="align-middle opacity-54">check_circle</mat-icon>
        </td>
        <td>
            <mat-icon color="primary" class="align-middle opacity-54">check_circle</mat-icon>
        </td>
    </tr>
    <tr class="h-8 block-premium-content">
        <td class="font-bold">{{'private.layout.user.subscription.certifier.messageTemplates' | translate}}</td>
        <td>
            <mat-icon color="warn" class="align-middle opacity-54">cancel</mat-icon>
        </td>
        <td>
            <mat-icon color="primary" class="align-middle opacity-54">check_circle</mat-icon>
        </td>
        <td>
            <mat-icon color="primary" class="align-middle opacity-54">check_circle</mat-icon>
        </td>
    </tr>
    <tr class="h-8 block-premium-content">
        <td class="font-bold">{{'private.layout.user.subscription.certifier.documents' | translate}}</td>
        <td>
            <mat-icon color="warn" class="align-middle opacity-54">cancel</mat-icon>
        </td>
        <td>
            <mat-icon color="primary" class="align-middle opacity-54">check_circle</mat-icon>
        </td>
        <td>
            <mat-icon color="primary" class="align-middle opacity-54">check_circle</mat-icon>
        </td>
    </tr>
    <tr class="h-8 block-premium-content">
        <td class="font-bold">{{'private.layout.user.subscription.certifier.promoteCertification' | translate}}</td>
        <td>
            <mat-icon color="warn" class="align-middle opacity-54">cancel</mat-icon>
        </td>
        <td>
            <mat-icon color="primary" class="align-middle opacity-54">check_circle</mat-icon>
        </td>
        <td>
            <mat-icon color="primary" class="align-middle opacity-54">check_circle</mat-icon>
        </td>
    </tr>
    <tr class="h-8 block-premium-content">
        <td class="font-bold">{{'private.layout.user.subscription.certifier.attendee' | translate}}</td>
        <td>
            <mat-icon color="warn" class="align-middle opacity-54">cancel</mat-icon>
        </td>
        <td>
            <mat-icon color="primary" class="align-middle opacity-54">check_circle</mat-icon>
        </td>
        <td>
            <mat-icon color="primary" class="align-middle opacity-54">check_circle</mat-icon>
        </td>
    </tr>
    <tr class="h-8 block-premium-content">
        <td class="font-bold">{{'private.layout.user.subscription.certifier.generateCertificate' | translate}}</td>
        <td>
            <mat-icon color="warn" class="align-middle opacity-54">cancel</mat-icon>
        </td>
        <td>
            <mat-icon color="primary" class="align-middle opacity-54">check_circle</mat-icon>
        </td>
        <td>
            <mat-icon color="primary" class="align-middle opacity-54">check_circle</mat-icon>
        </td>
    </tr>
    <tr class="h-8 block-premium-content">
        <td class="font-bold">{{'private.layout.user.subscription.certifier.partnerStatistics' | translate}}</td>
        <td>
            <mat-icon color="warn" class="align-middle opacity-54">cancel</mat-icon>
        </td>
        <td>
            <mat-icon color="primary" class="align-middle opacity-54">check_circle</mat-icon>
        </td>
        <td>
            <mat-icon color="primary" class="align-middle opacity-54">check_circle</mat-icon>
        </td>
    </tr>
    <tr class="h-8 block-premium-content">
        <td class="font-bold">{{'private.layout.user.subscription.certifier.importExport' | translate}}</td>
        <td>
            <mat-icon color="warn" class="align-middle opacity-54">cancel</mat-icon>
        </td>
        <td>
            <mat-icon color="primary" class="align-middle opacity-54">check_circle</mat-icon>
        </td>
        <td>
            <mat-icon color="primary" class="align-middle opacity-54">check_circle</mat-icon>
        </td>
    </tr>
    <tr class="h-8 block-premium-content">
        <td class="font-bold">{{'private.layout.user.subscription.certifier.openData' | translate}}</td>
        <td>
            <mat-icon color="warn" class="align-middle opacity-54">cancel</mat-icon>
        </td>
        <td>
            <mat-icon color="primary" class="align-middle opacity-54">check_circle</mat-icon>
        </td>
        <td>
            <mat-icon color="primary" class="align-middle opacity-54">check_circle</mat-icon>
        </td>
    </tr>
    <tr class="h-8 block-premium-content">
        <td class="font-bold">{{'private.layout.user.subscription.certifier.adequation' | translate}}</td>
        <td>
            <mat-icon color="accent" class="align-middle opacity-54">check_circle</mat-icon>
        </td>
        <td>
            <mat-icon color="primary" class="align-middle opacity-54">check_circle</mat-icon>
        </td>
        <td>
            <mat-icon color="primary" class="align-middle opacity-54">check_circle</mat-icon>
        </td>
    </tr>
    <tr class="h-8 block-premium-content last-row">
        <td class="font-bold">{{'private.layout.user.subscription.certifier.support' | translate}}</td>
        <td>{{'private.layout.user.subscription.certifier.supportStandard' | translate}}</td>
        <td>{{'private.layout.user.subscription.certifier.supportPremium' | translate}}</td>
        <td>{{'private.layout.user.subscription.certifier.supportPremium' | translate}}</td>
    </tr>
    <tr *ngIf="!subscription.certifierPartnership">
        <td [class]="subscription.certifierType === SubscriptionCertifierTypes.FREE ? 'rounded-b' : ''">
        </td>
        <td [class]="subscription.certifierType === SubscriptionCertifierTypes.USAGE && !subscription.allowCertifierPlus ? 'rounded-b' : ''">
            <ng-template [ngTemplateOutlet]="genericButton"
                         [ngTemplateOutletContext]="{isMySubscription: subscription.certifierType == SubscriptionCertifierTypes.USAGE && !subscription.allowCertifierPlus, type:SubscriptionCertifierTypes.USAGE, certifierPlus : false}">
            </ng-template>
        </td>
        <td [class]="(subscription.certifierType === SubscriptionCertifierTypes.ACCESS) ? 'rounded-b' : ''">
            <ng-template [ngTemplateOutlet]="genericButton"
                         [ngTemplateOutletContext]="{isMySubscription: subscription.certifierType == SubscriptionCertifierTypes.ACCESS, type: SubscriptionCertifierTypes.ACCESS, certifierPlus : true}">
            </ng-template>
        </td>
        <td [class]="(subscription.certifierType === SubscriptionCertifierTypes.USAGE && subscription.allowCertifierPlus) || subscription.certifierType === SubscriptionCertifierTypes.UNLIMITED  ? 'rounded-b' : ''">
            <ng-template [ngTemplateOutlet]="genericButton"
                         [ngTemplateOutletContext]="{isMySubscription: ((subscription.certifierType == SubscriptionCertifierTypes.USAGE) && subscription.allowCertifierPlus) || subscription.certifierType === SubscriptionCertifierTypes.UNLIMITED, type: SubscriptionCertifierTypes.USAGE, certifierPlus : true}">
            </ng-template>
        </td>
        <ng-template #genericButton let-isMySubscription="isMySubscription" let-type="type"
                     let-certifierPlus="certifierPlus">
            <button *ngIf="isMySubscription; else notMineSubscription"
                    color="primary" mat-flat-button class="w-5/6 mr-auto ml-auto flex mb-2 mt-4"
                    [disabled]="isMySubscription">
                {{ 'private.layout.user.subscription.mySubscription' | translate }}
            </button>
            <ng-template #notMineSubscription>
                <button *ngIf="isCustomer(); else notCustomer"
                        color="primary" mat-flat-button class="w-5/6 mr-auto ml-auto flex mb-2 mt-4">
                    <a class="no-underline"
                       [href]="getMail(type, certifierPlus)"> {{ 'private.layout.user.subscription.select' | translate }}</a>
                </button>
                <ng-template #notCustomer>
                    <button
                        color="primary" mat-flat-button class="w-5/6 mr-auto ml-auto flex mb-2 mt-4"
                        (click)="subscriptionLink(type, {certifierPlus: certifierPlus, quantity: type == SubscriptionCertifierTypes.ACCESS ? this.defaultCertificationFoldersNumberCapAccess : this.defaultCertificationFoldersNumberCap, price: getCertificationFolderPrice(certifierPlus)})">
                        {{ 'private.layout.user.subscription.select' | translate }}
                    </button>
                </ng-template>
            </ng-template>
        </ng-template>
    </tr>
    <tr>
        <td></td>
        <td></td>
        <td></td>
        <td>
            <a class="no-underline" *ngIf="subscription.certifierType !== SubscriptionCertifierTypes.UNLIMITED"
               [href]="getMail(SubscriptionCertifierTypes.UNLIMITED, true)">{{'private.layout.user.subscription.certifier.goAnnual' | translate}}
            </a>
        </td>
    </tr>
    </tbody>
</table>
