import {NgModule} from '@angular/core';
import {CommonModule} from '@angular/common';
import {SubscriptionCardComponent} from './subscription-card/subscription-card.component';
import {TranslateModule} from '@ngx-translate/core';
import {MatButtonModule} from '@angular/material/button';
import {MaterialModule} from '../material/material.module';
import {DialogUpgradeTrainingSubscriptionPlanComponent} from './dialog-upgrade-training-subscription-plan/dialog-upgrade-training-subscription-plan.component';
import {MatTooltipModule} from '@angular/material/tooltip';
import {SubscriptionSmallCardComponent} from './subscription-small-card/subscription-small-card.component';
import {DialogUpgradeCertifierSubscriptionPlanComponent} from './dialog-upgrade-certifier-subscription-plan/dialog-upgrade-certifier-subscription-plan.component';
import {DialogUpgradeSubscriptionComponent} from './dialog-upgrade-subscription/dialog-upgrade-subscription.component';
import {RouterModule} from '@angular/router';
import {MatCardModule} from '@angular/material/card';
import {SubscriptionApplicationsCardComponent} from './subscription-applications-card/subscription-applications-card.component';
import {SubscriptionCertifierTypesForCertifierComponent} from './subscription-certier-types-for-certifier/subscription-certifier-types-for-certifier.component';
import {SubscriptionCertifierTypesForPartnerComponent} from './subscription-certifier-types-for-partner/subscription-certifier-types-for-partner.component';

@NgModule({
    declarations: [
        SubscriptionCardComponent,
        DialogUpgradeTrainingSubscriptionPlanComponent,
        DialogUpgradeCertifierSubscriptionPlanComponent,
        SubscriptionSmallCardComponent,
        DialogUpgradeSubscriptionComponent,
        SubscriptionApplicationsCardComponent,
        SubscriptionCertifierTypesForCertifierComponent,
        SubscriptionCertifierTypesForPartnerComponent
    ],
    imports: [
        CommonModule,
        TranslateModule,
        MatButtonModule,
        MaterialModule,
        MatTooltipModule,
        RouterModule,
        MatCardModule
    ],
    exports: [
        SubscriptionCardComponent,
        DialogUpgradeTrainingSubscriptionPlanComponent,
        DialogUpgradeCertifierSubscriptionPlanComponent,
        SubscriptionSmallCardComponent,
        SubscriptionApplicationsCardComponent
    ]
})
export class SubscriptionModule {
}
