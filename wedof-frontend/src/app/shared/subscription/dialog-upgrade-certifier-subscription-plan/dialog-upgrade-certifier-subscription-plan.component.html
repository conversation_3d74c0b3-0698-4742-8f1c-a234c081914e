<app-dialog-layout [title]="'private.layout.user.subscription.modal.title.certifier' | translate"
                   cancelText="{{'common.actions.close' | translate}}"
                   (dialogClose)="closeModal()">

    <treo-message *ngIf="action" type="info"
                  [showIcon]="false" appearance="outline">
        <p class="py-2">{{'private.layout.user.subscription.help.action' | translate }}</p>
    </treo-message>

    <div class="flex justify-between"
         *ngIf="subscription.certifierType === SubscriptionCertifierTypes.PARTNER">
        <strong> {{'private.layout.user.subscription.modal.subtitlePartner' | translate}} </strong>
    </div>
    <div class="flex flex-row mt-5">
        <div class="w-full">
            <div class="mb-5 flex flex-col items-center"
                 *ngIf="(subscription.certifierType === SubscriptionCertifierTypes.NONE ||
                 ((subscription.certifierType === SubscriptionCertifierTypes.PARTNER ||
                 subscription.certifierType === SubscriptionCertifierTypes.PARTNER_PLUS) && organism.isCertifierOrganism)
                ) && !subscription.certifierTrialEndDate">
                <p>{{'private.layout.user.subscription.switchTrialCertifier' | translate}}</p>
                <button
                    id="switch-trial-button"
                    [disabled]="isSwitching"
                    color="primary" mat-flat-button class="w-5/7 mr-auto ml-auto flex mb-4 mt-4"
                    (click)="switchToTrial()">
                    <span *ngIf="!isSwitching">{{'private.layout.user.subscription.try'| translate }}</span>
                    <mat-progress-spinner class="mr-4" *ngIf="isSwitching"
                                          [diameter]="24"
                                          [mode]="'indeterminate'"></mat-progress-spinner>
                </button>
                <div *ngIf="trialErrorMessages.length" class="flex items-center">
                    <treo-message class="flex-auto" appearance="outline" [showIcon]="false" type="error">
                        <ul>
                            <li *ngFor="let errorMessage of trialErrorMessages">
                                {{ errorMessage }}
                            </li>
                        </ul>
                    </treo-message>
                </div>
            </div>

            <table class="flex flex-col"
                   *ngIf="subscription.certifierType === SubscriptionCertifierTypes.PARTNER_PLUS">
                <tr class="flex justify-between">
                    <th class="flex flex-row items-center">{{'common.token.requestCountTitle' | translate}}</th>
                    <td class="flex flex-row items-center">{{subscription.requestCount}}</td>
                </tr>
                <tr class="flex justify-between">
                    <th class="flex flex-row items-center">{{'private.layout.user.subscription.startDate' | translate}}</th>
                    <td class="flex flex-row items-center">{{subscription.certifierStartDate |  date:'mediumDate'}}</td>
                </tr>
                <tr class="flex justify-between" *ngIf="subscription.certifierPendingCancellation">
                    <th class="flex flex-row items-center">{{'private.layout.user.subscription.endDate' | translate}}</th>
                    <td class="flex flex-row items-center">{{subscription.certifierPeriodEndDate |  date:'mediumDate'}}</td>
                </tr>
            </table>

            <table class="flex flex-col"
                   *ngIf="[SubscriptionCertifierTypes.USAGE.toString(), SubscriptionCertifierTypes.UNLIMITED.toString(), SubscriptionCertifierTypes.TRIAL.toString(), SubscriptionCertifierTypes.ACCESS].includes(subscription.certifierType)">
                <tr class="flex justify-between"
                    *ngIf="subscription.allowCertifiers && subscription.certifierType !== SubscriptionCertifierTypes.ACCESS">
                    <th class="flex flex-row items-center"><a (click)="this.closeModal()"
                                                              matTooltip="Voir les dossiers créés sur la période"
                                                              [routerLink]="'/certification/dossiers'"
                                                              [queryParams]="{filterOnStateDate: 'wedofInvoice', period: PeriodTypesForCertificationFolders.WEDOF_INVOICE}"
                    >{{'private.layout.user.subscription.certificationFoldersCount' | translate}}</a></th>
                    <td class="flex justify-center items-center">
                        <a class="no-underline flex flex-row items-center" (click)="this.closeModal()"
                           [routerLink]="['/aide/guides/certificateurs/facturation']"
                           [queryParams]="{filterOnStateDate: 'wedofInvoice', period: PeriodTypesForCertificationFolders.WEDOF_INVOICE}"
                        >
                            <mat-icon class="pr-1"
                                      matTooltip="En savoir plus sur la facturation des dossiers de certification"
                                      [svgIcon]="'help'"></mat-icon>
                            <p>{{certificationFoldersCounter}} {{(subscription.certifierType === SubscriptionCertifierTypes.TRIAL || subscription.certifierType === SubscriptionCertifierTypes.USAGE) ? ' / ' + subscription.certificationFoldersNumberCap : ''}} </p>
                        </a>
                    </td>
                </tr>
                <tr class="flex justify-between"
                    *ngIf="subscription.certifierType === SubscriptionCertifierTypes.ACCESS">
                    <th class="flex flex-row items-center"><a (click)="this.closeModal()"
                                                              matTooltip="Voir les dossiers créés sur la période"
                                                              [routerLink]="'/certification/dossiers'"
                                                              [queryParams]="{filterOnStateDate: 'wedofInvoice', period: PeriodTypesForCertificationFolders.WEDOF_INVOICE}"
                    >{{'private.layout.user.subscription.certificationFoldersCountAccessMonthly' | translate}}</a></th>
                    <td class="flex justify-center items-center">
                        <a class="no-underline flex flex-row items-center" (click)="this.closeModal()"
                           [routerLink]="['/aide/guides/certificateurs/facturation']"
                           [queryParams]="{filterOnStateDate: 'wedofInvoice', period: PeriodTypesForCertificationFolders.WEDOF_INVOICE}"
                        >
                            <mat-icon class="pr-1"
                                      matTooltip="En savoir plus sur la facturation des dossiers de certification"
                                      [svgIcon]="'help'"></mat-icon>
                            <p>{{certificationFoldersCounter}}</p>
                        </a>
                    </td>
                </tr>
                <tr class="flex justify-between">
                    <th class="flex flex-row items-center">{{'private.layout.user.subscription.activeApps' | translate}}</th>
                    <td class="flex flex-row items-center"> {{counterApplications}}</td>
                </tr>
                <tr class="flex justify-between" *ngIf="subscription.smsSentNumberPeriodStartDate">
                    <th class="flex flex-row items-center">{{'private.layout.user.subscription.smsSent.title' | translate}}</th>
                    <td class="flex flex-row items-center">
                        <mat-icon class="pr-1" matTooltip="{{'private.layout.user.subscription.smsSent.tooltip' | translate: {
                                    startDate: subscription.smsSentNumberPeriodStartDate | date:'mediumDate',
                                    endDate: subscription.smsSentNumberPeriodEndDate | date:'mediumDate'
                            }
                            }}" [svgIcon]="'help'"></mat-icon>
                        {{subscription.smsSentNumberCount}}
                    </td>
                </tr>
                <tr class="flex justify-between">
                    <th class="flex flex-row items-center">{{'common.token.requestCountTitle' | translate}}</th>
                    <td class="flex flex-row items-center">{{subscription.requestCount}}</td>
                </tr>
                <ng-container *ngFor="let organismApplication of organismApplicationsOptions">
                    <tr class="flex justify-between">
                        <th class="flex flex-row items-center">{{ getApplicationName(organismApplication.appId) }}</th>
                        <td class="flex flex-row items-center">
                            <a (click)="closeModal()"
                               [routerLink]="[organismApplication.state != OrganismApplicationStates.DISABLED ? '/mes-applications/'+organismApplication.appId+'/reglages' : '/mes-applications']"
                               [state]="{'enableAppId': (organismApplication.state == OrganismApplicationStates.DISABLED ? organismApplication.appId : null)}">
                                {{'private.layout.user.subscription.subscriptionOptionStates.' + organismApplication.state | translate}}
                                <ng-container
                                    *ngIf="organismApplication.endDate">
                                    ({{organismApplication.endDate | date:'mediumDate'}})
                                </ng-container>
                            </a>
                        </td>
                    </tr>
                </ng-container>
                <tr class="flex justify-between">
                    <th class="flex flex-row items-center">{{'private.layout.user.subscription.allowAudits' | translate}}</th>
                    <td class="flex flex-row items-center">{{certificationAllowAudits}}</td>
                </tr>
                <tr class="flex justify-between">
                    <th class="flex flex-row items-center"
                        *ngIf="subscription.certifierType !== SubscriptionCertifierTypes.TRIAL">{{'private.layout.user.subscription.startDate' | translate}}</th>
                    <th class="flex flex-row items-center"
                        *ngIf="subscription.certifierType === SubscriptionCertifierTypes.TRIAL">{{'private.layout.user.subscription.startTrial' | translate}}</th>
                    <td class="flex flex-row items-center">{{subscription.certifierStartDate |  date:'mediumDate'}}</td>
                </tr>
                <ng-container *ngIf="subscription.certifierPendingCancellation; else noCancellation">
                    <table>
                        <tr class="flex justify-between">
                            <th class="flex flex-row items-center">{{'private.layout.user.subscription.endDate' | translate}}</th>
                            <td class="flex flex-row items-center">{{subscription.certifierPeriodEndDate |  date:'mediumDate'}}</td>
                        </tr>
                    </table>
                </ng-container>
                <ng-template #noCancellation>
                    <table>
                        <tr class="flex justify-between"
                            *ngIf="!subscription.trainingPeriodEndDate.includes('2199-12-31') && subscription.certifierType !== SubscriptionCertifierTypes.TRIAL">
                            <th class="flex flex-row items-center">{{'private.layout.user.subscription.period' | translate}}</th>
                            <td class="flex flex-row items-center">
                                {{'private.layout.user.subscription.fromTo' | translate : {
                                from: subscription.certifierPeriodStartDate |  date:'mediumDate',
                                to: subscription.certifierPeriodEndDate |  date:'mediumDate'
                            } }}
                            </td>
                        </tr>
                    </table>
                </ng-template>
                <tr class="flex justify-between"
                    *ngIf="subscription.certifierType === SubscriptionCertifierTypes.ACCESS">
                    <th class="flex flex-row items-center">{{'private.layout.user.subscription.certificationFoldersCountAccessAnnual' | translate}}</th>
                    <td class="flex justify-center items-center">
                        <a (click)="this.closeModal()"
                           matTooltip="Voir les dossiers créés sur la période annuelle"
                           [routerLink]="'/certification/dossiers'"
                           [queryParams]="{filterOnStateDate: 'wedofInvoice', period: PeriodTypesForCertificationFolders.WEDOF_QUOTA}"
                        >{{subscription.certificationFoldersNumberAnnualCount}}
                            / {{subscription.certificationFoldersNumberAnnualLimit}}
                        </a>
                    </td>
                </tr>
            </table>
            <div class="flex justify-between mt-3"
                 *ngIf="![SubscriptionCertifierTypes.NONE.toString(), SubscriptionCertifierTypes.PARTNER.toString()].includes(subscription.certifierType) || organism.isReseller">
                <div>
                    <button
                        color="primary" mat-flat-button
                        (click)="subscriptionLink()"
                        *ngIf="organism.isReseller || (subscription.isStripeCustomer && user.isOwner && subscription.certifierType !== SubscriptionCertifierTypes.PARTNER)">{{ 'private.layout.user.subscription.invoices.downloadInvoice' | translate }}
                    </button>
                    <button *ngIf="organism.isTrainingOrganism && subscription.trainingType"
                            mat-flat-button
                            (click)="switchToTrainingAction($event)">{{ 'private.layout.user.subscription.modal.access.training' | translate }}</button>
                </div>
                <button
                    color="primary" mat-flat-button
                    (click)="subscriptionLink()"
                    *ngIf="subscription.certifierType === SubscriptionCertifierTypes.USAGE || subscription.certifierType === SubscriptionCertifierTypes.UNLIMITED">{{ 'private.layout.user.subscription.manageMySubscription' | translate }}
                </button>

            </div>
        </div>
    </div>
    <div class="flex flex-row items-center mt-4 mb-4"
         *ngIf="fromPage && subscription.certifierType !== SubscriptionCertifierTypes.UNLIMITED">
        <mat-icon color="primary">lightbulb</mat-icon>
        <p [innerHTML]=" 'private.layout.user.subscription.help.' + fromPage | translate "></p>
    </div>

    <div class="mt-5 mb-2 flex justify-center" *ngIf="showBothButtons">
        <button mat-flat-button
                type="button"
                [color]="showCertifier ? 'primary' : null"
                [class]="showCertifier ? '' : 'bg-gray-200'"
                class="mr-4"
                (click)="showCertifierType()">
            {{'private.layout.user.subscription.certifier.usage.offer' | translate }}
        </button>
        <button mat-flat-button
                type="button"
                [class]="showPartner ? '' : 'bg-gray-200'"
                [color]="showPartner ? 'primary' : null"
                (click)="showPartnerType()">
            {{'private.layout.user.subscription.certifier.partner.offer' | translate}}
        </button>
    </div>

    <app-subscription-certifier-types-for-certifier *ngIf="showCertifier"
                                                    [subscription]="subscription"
                                                    [userName]="user.name"
                                                    [defaultCertificationFoldersNumberCap]="defaultCertificationFoldersNumberCap"
                                                    [defaultCertificationFoldersNumberCapAccess]="defaultCertificationFoldersNumberCapAccess"
                                                    [organism]="organism">
    </app-subscription-certifier-types-for-certifier>

    <app-subscription-certifier-types-for-partner *ngIf="showPartner"
                                                  [subscription]="subscription"
                                                  [organism]="organism"></app-subscription-certifier-types-for-partner>

    <mat-card class="text-center m-auto p-5 shadow-none mb-4" *ngIf="showCertifier">
        <div class="mb-2"><strong>{{'private.layout.user.subscription.audit.title' | translate}}</strong>
        </div>
        <p><strong
            class="text-xl">99€</strong> HT / mois / certification</p>
        <div class="flex items-center text-center">
            <mat-icon color="primary" class="opacity-54">check_circle</mat-icon>
            {{'private.layout.user.subscription.audit.description' | translate}}
            <mat-icon color="primary" class="opacity-54 ml-3">check_circle</mat-icon>
            {{'private.layout.user.subscription.audit.description2' | translate}}
        </div>
        <ng-container *ngIf="isAnnualSubscription; else showText">
            <a class="cursor-pointer" (click)="sendMail()">
                {{'private.common.certification.audit.activateContact' | translate}}
            </a>
        </ng-container>
        <ng-template #showText>
            {{'private.layout.user.subscription.audit.activate' | translate}}
        </ng-template>
    </mat-card>

    <ng-container *ngIf="showCertifier">
        <p class="mb-2 mat-caption">
            * {{'private.layout.user.subscription.certifier.additionalCost.title' | translate}}</p>
        <p class="mb-2 mat-caption">
            ** {{'private.layout.user.subscription.certifier.additionalCostBeta' | translate}}</p>
    </ng-container>
    <!-- frais de mise en service payant -->
    <!--    <p *ngIf="subscription.certifierType === SubscriptionCertifierTypes.FREE || subscription.certifierType === SubscriptionCertifierTypes.TRIAL" class="mb-2 text-red">* {{'private.layout.user.subscription.certifier.additionalCostFree' | translate}}</p> &lt;!&ndash; frais de mise en service offerts &ndash;&gt;-->

    <div *ngIf="subscription.certifierType !== SubscriptionCertifierTypes.PARTNER">
        <div class="mb-5 flex flex-col items-center"
             *ngIf=" !subscription.certifierPartnership; else contactPartnership">
            <p>{{'private.layout.user.subscription.table.contact.question' | translate}}</p>
            <p>{{'private.layout.user.subscription.table.contact.contact' | translate}} : <a class="link font-semibold"
                                                                                             href="mailto:{{'private.support.mail' | translate}}">{{'private.support.mail' | translate}}</a>
                ou au <a
                    href="tel:{{'private.layout.user.subscription.table.contact.phoneNumber' | translate}}">{{'private.layout.user.subscription.table.contact.phoneNumber' | translate}}</a>
            </p>
        </div>
        <ng-template #contactPartnership>
            <div class="mb-5 flex flex-col items-center">
                <p>{{'private.layout.user.subscription.table.contact.questionPartnership' | translate}}</p>
                <p>{{'private.layout.user.subscription.table.contact.contactPartnership' | translate : {partnership: subscription.certifierPartnership} }}</p>
            </div>
        </ng-template>
    </div>
</app-dialog-layout>
